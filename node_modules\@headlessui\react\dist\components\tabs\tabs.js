import C,{createContext as V,Fragment as ne,useContext as Q,useMemo as I,useReducer as re,useRef as J}from"react";import{Keys as y}from'../../components/keyboard.js';import{useEvent as _}from'../../hooks/use-event.js';import{useId as Y}from'../../hooks/use-id.js';import{useIsoMorphicEffect as N}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as B}from'../../hooks/use-latest-value.js';import{useResolveButtonType as ae}from'../../hooks/use-resolve-button-type.js';import{useSyncRefs as w}from'../../hooks/use-sync-refs.js';import{FocusSentinel as le}from'../../internal/focus-sentinel.js';import{Hidden as oe}from'../../internal/hidden.js';import{Focus as x,focusIn as D,FocusResult as j,sortByDomNode as v}from'../../utils/focus-management.js';import{match as G}from'../../utils/match.js';import{microTask as se}from'../../utils/micro-task.js';import{getOwnerDocument as ie}from'../../utils/owner.js';import{Features as Z,forwardRefWithAs as H,render as U}from'../../utils/render.js';import{StableCollection as pe,useStableCollectionIndex as ee}from'../../utils/stable-collection.js';var ue=(t=>(t[t.Forwards=0]="Forwards",t[t.Backwards=1]="Backwards",t))(ue||{}),Te=(l=>(l[l.Less=-1]="Less",l[l.Equal=0]="Equal",l[l.Greater=1]="Greater",l))(Te||{}),de=(a=>(a[a.SetSelectedIndex=0]="SetSelectedIndex",a[a.RegisterTab=1]="RegisterTab",a[a.UnregisterTab=2]="UnregisterTab",a[a.RegisterPanel=3]="RegisterPanel",a[a.UnregisterPanel=4]="UnregisterPanel",a))(de||{});let ce={[0](e,n){var i;let t=v(e.tabs,c=>c.current),l=v(e.panels,c=>c.current),o=t.filter(c=>{var p;return!((p=c.current)!=null&&p.hasAttribute("disabled"))}),a={...e,tabs:t,panels:l};if(n.index<0||n.index>t.length-1){let c=G(Math.sign(n.index-e.selectedIndex),{[-1]:()=>1,[0]:()=>G(Math.sign(n.index),{[-1]:()=>0,[0]:()=>0,[1]:()=>1}),[1]:()=>0});if(o.length===0)return a;let p=G(c,{[0]:()=>t.indexOf(o[0]),[1]:()=>t.indexOf(o[o.length-1])});return{...a,selectedIndex:p===-1?e.selectedIndex:p}}let T=t.slice(0,n.index),m=[...t.slice(n.index),...T].find(c=>o.includes(c));if(!m)return a;let b=(i=t.indexOf(m))!=null?i:e.selectedIndex;return b===-1&&(b=e.selectedIndex),{...a,selectedIndex:b}},[1](e,n){if(e.tabs.includes(n.tab))return e;let t=e.tabs[e.selectedIndex],l=v([...e.tabs,n.tab],a=>a.current),o=e.selectedIndex;return e.info.current.isControlled||(o=l.indexOf(t),o===-1&&(o=e.selectedIndex)),{...e,tabs:l,selectedIndex:o}},[2](e,n){return{...e,tabs:e.tabs.filter(t=>t!==n.tab)}},[3](e,n){return e.panels.includes(n.panel)?e:{...e,panels:v([...e.panels,n.panel],t=>t.current)}},[4](e,n){return{...e,panels:e.panels.filter(t=>t!==n.panel)}}},X=V(null);X.displayName="TabsDataContext";function F(e){let n=Q(X);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,F),t}return n}let $=V(null);$.displayName="TabsActionsContext";function q(e){let n=Q($);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,q),t}return n}function fe(e,n){return G(n.type,ce,e,n)}let be=ne;function me(e,n){let{defaultIndex:t=0,vertical:l=!1,manual:o=!1,onChange:a,selectedIndex:T=null,...R}=e;const m=l?"vertical":"horizontal",b=o?"manual":"auto";let i=T!==null,c=B({isControlled:i}),p=w(n),[u,f]=re(fe,{info:c,selectedIndex:T!=null?T:t,tabs:[],panels:[]}),P=I(()=>({selectedIndex:u.selectedIndex}),[u.selectedIndex]),g=B(a||(()=>{})),E=B(u.tabs),L=I(()=>({orientation:m,activation:b,...u}),[m,b,u]),A=_(s=>(f({type:1,tab:s}),()=>f({type:2,tab:s}))),S=_(s=>(f({type:3,panel:s}),()=>f({type:4,panel:s}))),k=_(s=>{h.current!==s&&g.current(s),i||f({type:0,index:s})}),h=B(i?e.selectedIndex:u.selectedIndex),W=I(()=>({registerTab:A,registerPanel:S,change:k}),[]);N(()=>{f({type:0,index:T!=null?T:t})},[T]),N(()=>{if(h.current===void 0||u.tabs.length<=0)return;let s=v(u.tabs,d=>d.current);s.some((d,M)=>u.tabs[M]!==d)&&k(s.indexOf(u.tabs[h.current]))});let O={ref:p};return C.createElement(pe,null,C.createElement($.Provider,{value:W},C.createElement(X.Provider,{value:L},L.tabs.length<=0&&C.createElement(le,{onFocus:()=>{var s,r;for(let d of E.current)if(((s=d.current)==null?void 0:s.tabIndex)===0)return(r=d.current)==null||r.focus(),!0;return!1}}),U({ourProps:O,theirProps:R,slot:P,defaultTag:be,name:"Tabs"}))))}let Pe="div";function ye(e,n){let{orientation:t,selectedIndex:l}=F("Tab.List"),o=w(n);return U({ourProps:{ref:o,role:"tablist","aria-orientation":t},theirProps:e,slot:{selectedIndex:l},defaultTag:Pe,name:"Tabs.List"})}let xe="button";function ge(e,n){var O,s;let t=Y(),{id:l=`headlessui-tabs-tab-${t}`,...o}=e,{orientation:a,activation:T,selectedIndex:R,tabs:m,panels:b}=F("Tab"),i=q("Tab"),c=F("Tab"),p=J(null),u=w(p,n);N(()=>i.registerTab(p),[i,p]);let f=ee("tabs"),P=m.indexOf(p);P===-1&&(P=f);let g=P===R,E=_(r=>{var M;let d=r();if(d===j.Success&&T==="auto"){let K=(M=ie(p))==null?void 0:M.activeElement,z=c.tabs.findIndex(te=>te.current===K);z!==-1&&i.change(z)}return d}),L=_(r=>{let d=m.map(K=>K.current).filter(Boolean);if(r.key===y.Space||r.key===y.Enter){r.preventDefault(),r.stopPropagation(),i.change(P);return}switch(r.key){case y.Home:case y.PageUp:return r.preventDefault(),r.stopPropagation(),E(()=>D(d,x.First));case y.End:case y.PageDown:return r.preventDefault(),r.stopPropagation(),E(()=>D(d,x.Last))}if(E(()=>G(a,{vertical(){return r.key===y.ArrowUp?D(d,x.Previous|x.WrapAround):r.key===y.ArrowDown?D(d,x.Next|x.WrapAround):j.Error},horizontal(){return r.key===y.ArrowLeft?D(d,x.Previous|x.WrapAround):r.key===y.ArrowRight?D(d,x.Next|x.WrapAround):j.Error}}))===j.Success)return r.preventDefault()}),A=J(!1),S=_(()=>{var r;A.current||(A.current=!0,(r=p.current)==null||r.focus({preventScroll:!0}),i.change(P),se(()=>{A.current=!1}))}),k=_(r=>{r.preventDefault()}),h=I(()=>{var r;return{selected:g,disabled:(r=e.disabled)!=null?r:!1}},[g,e.disabled]),W={ref:u,onKeyDown:L,onMouseDown:k,onClick:S,id:l,role:"tab",type:ae(e,p),"aria-controls":(s=(O=b[P])==null?void 0:O.current)==null?void 0:s.id,"aria-selected":g,tabIndex:g?0:-1};return U({ourProps:W,theirProps:o,slot:h,defaultTag:xe,name:"Tabs.Tab"})}let Ee="div";function Ae(e,n){let{selectedIndex:t}=F("Tab.Panels"),l=w(n),o=I(()=>({selectedIndex:t}),[t]);return U({ourProps:{ref:l},theirProps:e,slot:o,defaultTag:Ee,name:"Tabs.Panels"})}let Re="div",Le=Z.RenderStrategy|Z.Static;function _e(e,n){var E,L,A,S;let t=Y(),{id:l=`headlessui-tabs-panel-${t}`,tabIndex:o=0,...a}=e,{selectedIndex:T,tabs:R,panels:m}=F("Tab.Panel"),b=q("Tab.Panel"),i=J(null),c=w(i,n);N(()=>b.registerPanel(i),[b,i,l]);let p=ee("panels"),u=m.indexOf(i);u===-1&&(u=p);let f=u===T,P=I(()=>({selected:f}),[f]),g={ref:c,id:l,role:"tabpanel","aria-labelledby":(L=(E=R[u])==null?void 0:E.current)==null?void 0:L.id,tabIndex:f?o:-1};return!f&&((A=a.unmount)==null||A)&&!((S=a.static)!=null&&S)?C.createElement(oe,{as:"span","aria-hidden":"true",...g}):U({ourProps:g,theirProps:a,slot:P,defaultTag:Re,features:Le,visible:f,name:"Tabs.Panel"})}let Se=H(ge),Ie=H(me),De=H(ye),Fe=H(Ae),he=H(_e),$e=Object.assign(Se,{Group:Ie,List:De,Panels:Fe,Panel:he});export{$e as Tab};
