'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Star, Quote, ArrowLeft, ArrowRight, 
  MapPin, Calendar, Shield, Zap,
  Users, CheckCircle
} from 'lucide-react';

export default function TestimonialsSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const sectionRef = useRef(null);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Business Owner",
      location: "Kuala Lumpur, Malaysia",
      avatar: "👩‍💼",
      rating: 5,
      date: "2 days ago",
      content: "ReportU transformed how I report traffic violations near my shop. What used to take hours now takes 2 minutes. The AI routing is incredibly accurate!",
      highlight: "2 minutes vs 3 hours",
      category: "Traffic Reporting",
      verified: true
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Community Leader",
      location: "Singapore",
      avatar: "👨‍🏫",
      rating: 5,
      date: "1 week ago",
      content: "The cross-border functionality is amazing. I can report issues that affect both Malaysia and Singapore seamlessly. Real-time tracking keeps me informed.",
      highlight: "Cross-border reporting",
      category: "Community Safety",
      verified: true
    },
    {
      id: 3,
      name: "Dr. Priya Sharma",
      role: "Healthcare Professional",
      location: "Johor Bahru, Malaysia",
      avatar: "👩‍⚕️",
      rating: 5,
      date: "3 days ago",
      content: "As a healthcare worker, I witness many incidents. ReportU's anonymous reporting feature gives me confidence to report without fear. The emergency priority system works perfectly.",
      highlight: "Anonymous & secure",
      category: "Emergency Reporting",
      verified: true
    },
    {
      id: 4,
      name: "Alex Wong",
      role: "Student",
      location: "Singapore",
      avatar: "👨‍🎓",
      rating: 5,
      date: "5 days ago",
      content: "The mobile app is incredibly intuitive. I've reported counterfeit products and public disturbances. The multimedia upload feature with drag-drop is so smooth!",
      highlight: "Mobile-first design",
      category: "Product Safety",
      verified: true
    },
    {
      id: 5,
      name: "Linda Ibrahim",
      role: "Retail Manager",
      location: "Penang, Malaysia",
      avatar: "👩‍💼",
      rating: 5,
      date: "1 week ago",
      content: "The analytics dashboard helps me understand safety trends in my area. ReportU isn't just reporting - it's community intelligence. Absolutely revolutionary!",
      highlight: "Community insights",
      category: "Business Intelligence",
      verified: true
    },
    {
      id: 6,
      name: "David Lim",
      role: "Security Officer",
      location: "Singapore",
      avatar: "👮‍♂️",
      rating: 5,
      date: "4 days ago",
      content: "Working in security, I need reliable reporting tools. ReportU's real-time tracking and instant notifications have improved our response time by 70%.",
      highlight: "70% faster response",
      category: "Security & Safety",
      verified: true
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    let interval;
    if (isAutoPlaying) {
      interval = setInterval(() => {
        setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
      }, 5000);
    }
    return () => clearInterval(interval);
  }, [isAutoPlaying, testimonials.length]);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const goToTestimonial = (index) => {
    setCurrentTestimonial(index);
    setIsAutoPlaying(false);
  };

  const current = testimonials[currentTestimonial];

  return (
    <section ref={sectionRef} className="py-20 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div 
            className={`inline-flex items-center gap-2 glass px-4 py-2 rounded-full mb-6 transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <Users className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-gray-300">Real User Stories</span>
          </div>
          
          <h2 
            className={`text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 transition-all duration-1000 delay-200 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <span className="text-white">Trusted by </span>
            <span 
              className="text-neon"
              style={{
                background: 'linear-gradient(45deg, #00d4ff, #8b5cf6, #10b981)',
                backgroundSize: '200% 200%',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                color: 'transparent',
                animation: 'gradient-shift 3s ease infinite'
              }}
            >
              Thousands
            </span>
          </h2>
          
          <p 
            className={`text-xl text-gray-300 max-w-3xl mx-auto transition-all duration-1000 delay-400 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            Real stories from real users across Malaysia and Singapore who transformed 
            their reporting experience with ReportU.
          </p>
        </div>

        {/* Main Testimonial */}
        <div 
          className={`relative transition-all duration-1000 delay-600 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="glass p-8 lg:p-12 rounded-2xl max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-3 gap-8 items-center">
              {/* Avatar & Info */}
              <div className="text-center lg:text-left">
                <div className="relative inline-block mb-4">
                  <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-4xl mb-4 mx-auto lg:mx-0">
                    {current.avatar}
                  </div>
                  {current.verified && (
                    <div className="absolute -bottom-1 -right-1 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-5 h-5 text-white" />
                    </div>
                  )}
                </div>
                
                <h3 className="text-xl font-bold text-white mb-1">{current.name}</h3>
                <p className="text-gray-400 mb-2">{current.role}</p>
                
                <div className="flex items-center justify-center lg:justify-start gap-2 text-sm text-gray-500 mb-3">
                  <MapPin className="w-4 h-4" />
                  <span>{current.location}</span>
                </div>
                
                <div className="flex items-center justify-center lg:justify-start gap-2 text-sm text-gray-500 mb-4">
                  <Calendar className="w-4 h-4" />
                  <span>{current.date}</span>
                </div>
                
                <div className="flex items-center justify-center lg:justify-start gap-1 mb-4">
                  {[...Array(current.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                
                <div className="inline-flex items-center gap-2 bg-primary/20 px-3 py-1 rounded-full">
                  <Shield className="w-4 h-4 text-primary" />
                  <span className="text-sm text-primary font-medium">{current.category}</span>
                </div>
              </div>

              {/* Testimonial Content */}
              <div className="lg:col-span-2">
                <Quote className="w-12 h-12 text-primary/30 mb-4" />
                
                <blockquote className="text-xl lg:text-2xl text-white leading-relaxed mb-6">
                  "{current.content}"
                </blockquote>
                
                <div className="flex items-center gap-3 p-4 bg-primary/10 rounded-lg border border-primary/20">
                  <Zap className="w-5 h-5 text-primary" />
                  <span className="text-primary font-semibold">Key Benefit: {current.highlight}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 -translate-y-1/2 glass p-3 rounded-full hover-glow transition-all duration-300 hover:scale-110"
          >
            <ArrowLeft className="w-6 h-6 text-white" />
          </button>
          
          <button
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 -translate-y-1/2 glass p-3 rounded-full hover-glow transition-all duration-300 hover:scale-110"
          >
            <ArrowRight className="w-6 h-6 text-white" />
          </button>
        </div>

        {/* Testimonial Indicators */}
        <div 
          className={`flex justify-center mt-8 transition-all duration-1000 delay-800 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="flex gap-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentTestimonial 
                    ? 'bg-primary scale-125' 
                    : 'bg-gray-600 hover:bg-gray-500'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Stats */}
        <div 
          className={`grid grid-cols-2 lg:grid-cols-4 gap-6 mt-16 transition-all duration-1000 delay-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          {[
            { number: '50K+', label: 'Happy Users', icon: Users },
            { number: '4.9/5', label: 'Average Rating', icon: Star },
            { number: '2M+', label: 'Reports Processed', icon: Shield },
            { number: '99.9%', label: 'Satisfaction Rate', icon: CheckCircle }
          ].map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={index} className="glass p-6 rounded-xl text-center hover-glow">
                <Icon className="w-8 h-8 text-primary mx-auto mb-3" />
                <div className="text-2xl font-bold text-white mb-1">{stat.number}</div>
                <div className="text-sm text-gray-400">{stat.label}</div>
              </div>
            );
          })}
        </div>
      </div>

      <style jsx>{`
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </section>
  );
}
