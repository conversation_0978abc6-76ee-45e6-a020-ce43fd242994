/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

var Ellipse = require('./Ellipse');

Ellipse.Area = require('./Area');
Ellipse.Circumference = require('./Circumference');
Ellipse.CircumferencePoint = require('./CircumferencePoint');
Ellipse.Clone = require('./Clone');
Ellipse.Contains = require('./Contains');
Ellipse.ContainsPoint = require('./ContainsPoint');
Ellipse.ContainsRect = require('./ContainsRect');
Ellipse.CopyFrom = require('./CopyFrom');
Ellipse.Equals = require('./Equals');
Ellipse.GetBounds = require('./GetBounds');
Ellipse.GetPoint = require('./GetPoint');
Ellipse.GetPoints = require('./GetPoints');
Ellipse.Offset = require('./Offset');
Ellipse.OffsetPoint = require('./OffsetPoint');
Ellipse.Random = require('./Random');

module.exports = Ellipse;
