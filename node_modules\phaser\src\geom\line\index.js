/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

var Line = require('./Line');

Line.Angle = require('./Angle');
Line.BresenhamPoints = require('./BresenhamPoints');
Line.CenterOn = require('./CenterOn');
Line.Clone = require('./Clone');
Line.CopyFrom = require('./CopyFrom');
Line.Equals = require('./Equals');
Line.Extend = require('./Extend');
Line.GetEasedPoints = require('./GetEasedPoints');
Line.GetMidPoint = require('./GetMidPoint');
Line.GetNearestPoint = require('./GetNearestPoint');
Line.GetNormal = require('./GetNormal');
Line.GetPoint = require('./GetPoint');
Line.GetPoints = require('./GetPoints');
Line.GetShortestDistance = require('./GetShortestDistance');
Line.Height = require('./Height');
Line.Length = require('./Length');
Line.NormalAngle = require('./NormalAngle');
Line.NormalX = require('./NormalX');
Line.NormalY = require('./NormalY');
Line.Offset = require('./Offset');
Line.PerpSlope = require('./PerpSlope');
Line.Random = require('./Random');
Line.ReflectAngle = require('./ReflectAngle');
Line.Rotate = require('./Rotate');
Line.RotateAroundPoint = require('./RotateAroundPoint');
Line.RotateAroundXY = require('./RotateAroundXY');
Line.SetToAngle = require('./SetToAngle');
Line.Slope = require('./Slope');
Line.Width = require('./Width');

module.exports = Line;
