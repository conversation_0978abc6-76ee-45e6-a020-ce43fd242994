!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).zustandMiddleware={})}(this,(function(e){"use strict";function t(){return t=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},t.apply(this,arguments)}var n=function(e,t){var n;try{n=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==n&&t(n)},o=function e(t){return function(n){try{var o=t(n);return o instanceof Promise?o:{then:function(t){return e(t)(o)},catch:function(e){return this}}}catch(t){return{then:function(e){return this},catch:function(n){return e(n)(t)}}}}};e.combine=function(e,t){return function(n,o,r){return Object.assign({},e,t(n,o,r))}},e.devtools=function(e,t){return function(o,r,i){var a,s=!1;"string"!=typeof t||s||(console.warn("[zustand devtools middleware]: passing `name` as directly will be not allowed in next majorpass the `name` in an object `{ name: ... }` instead"),s=!0);var c,u=void 0===t?{name:void 0,anonymousActionType:void 0}:"string"==typeof t?{name:t}:t;void 0!==(null==u||null==(a=u.serialize)?void 0:a.options)&&console.warn("[zustand devtools middleware]: `serialize.options` is deprecated, just use `serialize`");try{c=window.__REDUX_DEVTOOLS_EXTENSION__||window.top.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!c)return e(o,r,i);var d=Object.create(c.connect(u)),l=!1;Object.defineProperty(i,"devtools",{get:function(){return l||(console.warn("[zustand devtools middleware] `devtools` property on the store is deprecated it will be removed in the next major.\nYou shouldn't interact with the extension directly. But in case you still want to you can patch `window.__REDUX_DEVTOOLS_EXTENSION__` directly"),l=!0),d},set:function(e){l||(console.warn("[zustand devtools middleware] `api.devtools` is deprecated, it will be removed in the next major.\nYou shouldn't interact with the extension directly. But in case you still want to you can patch `window.__REDUX_DEVTOOLS_EXTENSION__` directly"),l=!0),d=e}});var f=!1;Object.defineProperty(d,"prefix",{get:function(){return f||(console.warn("[zustand devtools middleware] along with `api.devtools`, `api.devtools.prefix` is deprecated.\nWe no longer prefix the actions/names"+u.name===void 0?", pass the `name` option to create a separate instance of devtools for each store.":", because the `name` option already creates a separate instance of devtools for each store."),f=!0),""},set:function(){f||(console.warn("[zustand devtools middleware] along with `api.devtools`, `api.devtools.prefix` is deprecated.\nWe no longer prefix the actions/names"+u.name===void 0?", pass the `name` option to create a separate instance of devtools for each store.":", because the `name` option already creates a separate instance of devtools for each store."),f=!0)}});var p=!0;i.setState=function(e,t,n){o(e,t),p&&d.send(void 0===n?{type:u.anonymousActionType||"anonymous"}:"string"==typeof n?{type:n}:n,r())};var v=function(){var e=p;p=!1,o.apply(void 0,arguments),p=e},h=e(i.setState,r,i);if(d.init(h),i.dispatchFromDevtools&&"function"==typeof i.dispatch){var m=!1,y=i.dispatch;i.dispatch=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];"__setState"!==t[0].type||m||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),m=!0),y.apply(void 0,t)}}return d.subscribe((function(e){switch(e.type){case"ACTION":return"string"!=typeof e.payload?void console.error("[zustand devtools middleware] Unsupported action format"):n(e.payload,(function(e){"__setState"!==e.type?i.dispatchFromDevtools&&"function"==typeof i.dispatch&&i.dispatch(e):v(e.state)}));case"DISPATCH":switch(e.payload.type){case"RESET":return v(h),d.init(i.getState());case"COMMIT":return d.init(i.getState());case"ROLLBACK":return n(e.state,(function(e){v(e),d.init(i.getState())}));case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return n(e.state,(function(e){v(e)}));case"IMPORT_STATE":var t,o=e.payload.nextLiftedState,r=null==(t=o.computedStates.slice(-1)[0])?void 0:t.state;if(!r)return;return v(r),void d.send(null,o);case"PAUSE_RECORDING":return p=!p}return}})),h}},e.persist=function(e,n){return function(r,i,a){var s=t({getStorage:function(){return localStorage},serialize:JSON.stringify,deserialize:JSON.parse,partialize:function(e){return e},version:0,merge:function(e,n){return t({},n,e)}},n);(s.blacklist||s.whitelist)&&console.warn("The "+(s.blacklist?"blacklist":"whitelist")+" option is deprecated and will be removed in the next version. Please use the 'partialize' option instead.");var c,u=!1,d=new Set,l=new Set;try{c=s.getStorage()}catch(e){}if(!c)return e((function(){console.warn("[zustand persist middleware] Unable to update item '"+s.name+"', the given storage is currently unavailable."),r.apply(void 0,arguments)}),i,a);c.removeItem||console.warn("[zustand persist middleware] The given storage for item '"+s.name+"' does not contain a 'removeItem' method, which will be required in v4.");var f=o(s.serialize),p=function(){var e,n=s.partialize(t({},i()));s.whitelist&&Object.keys(n).forEach((function(e){var t;(null==(t=s.whitelist)||!t.includes(e))&&delete n[e]})),s.blacklist&&s.blacklist.forEach((function(e){return delete n[e]}));var o=f({state:n,version:s.version}).then((function(e){return c.setItem(s.name,e)})).catch((function(t){e=t}));if(e)throw e;return o},v=a.setState;a.setState=function(e,t){v(e,t),p()};var h,m=e((function(){r.apply(void 0,arguments),p()}),i,a),y=function(){if(c){u=!1,d.forEach((function(e){return e(i())}));var e=(null==s.onRehydrateStorage?void 0:s.onRehydrateStorage(i()))||void 0;return o(c.getItem.bind(c))(s.name).then((function(e){if(e)return s.deserialize(e)})).then((function(e){if(e){if("number"!=typeof e.version||e.version===s.version)return e.state;if(s.migrate)return s.migrate(e.state,e.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}})).then((function(e){var t;return h=s.merge(e,null!=(t=i())?t:m),r(h,!0),p()})).then((function(){null==e||e(h,void 0),u=!0,l.forEach((function(e){return e(h)}))})).catch((function(t){null==e||e(void 0,t)}))}};return a.persist={setOptions:function(e){s=t({},s,e),e.getStorage&&(c=e.getStorage())},clearStorage:function(){var e;null==(e=c)||null==e.removeItem||e.removeItem(s.name)},rehydrate:function(){return y()},hasHydrated:function(){return u},onHydrate:function(e){return d.add(e),function(){d.delete(e)}},onFinishHydration:function(e){return l.add(e),function(){l.delete(e)}}},y(),h||m}},e.redux=function(e,n){return function(o,r,i){return i.dispatch=function(t){return o((function(n){return e(n,t)}),!1,t),t},i.dispatchFromDevtools=!0,t({dispatch:function(){return i.dispatch.apply(i,arguments)}},n)}},e.subscribeWithSelector=function(e){return function(t,n,o){var r=o.subscribe;return o.subscribe=function(e,t,n){var i=e;if(t){var a=(null==n?void 0:n.equalityFn)||Object.is,s=e(o.getState());i=function(n){var o=e(n);if(!a(s,o)){var r=s;t(s=o,r)}},null!=n&&n.fireImmediately&&t(s,s)}return r(i)},e(t,n,o)}},Object.defineProperty(e,"__esModule",{value:!0})}));
