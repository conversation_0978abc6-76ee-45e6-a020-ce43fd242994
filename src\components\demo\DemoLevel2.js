'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Brain, Zap, Target, CheckCircle, 
  ArrowRight, Clock, Shield, Globe,
  Building, Users, AlertTriangle, FileText
} from 'lucide-react';

export default function DemoLevel2() {
  const [isVisible, setIsVisible] = useState(false);
  const [currentPhase, setCurrentPhase] = useState(0);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [reportData, setReportData] = useState(null);
  const sectionRef = useRef(null);

  const analysisPhases = [
    {
      id: 0,
      name: 'Content Analysis',
      description: 'AI analyzing report content and context',
      icon: Brain,
      duration: 2000,
      color: 'text-blue-400'
    },
    {
      id: 1,
      name: 'Category Classification',
      description: 'Determining offense type and severity',
      icon: Target,
      duration: 1500,
      color: 'text-purple-400'
    },
    {
      id: 2,
      name: 'Jurisdiction Detection',
      description: 'Identifying responsible authorities',
      icon: Globe,
      duration: 1800,
      color: 'text-green-400'
    },
    {
      id: 3,
      name: 'Department Routing',
      description: 'Selecting optimal department match',
      icon: Building,
      duration: 1200,
      color: 'text-orange-400'
    }
  ];

  const departments = [
    {
      id: 'traffic-my',
      name: 'Malaysia Traffic Police',
      country: 'Malaysia',
      logo: '🚔',
      confidence: 95,
      responseTime: '15 minutes',
      specialties: ['Traffic violations', 'Road safety', 'Vehicle offenses'],
      contact: '+60-3-2266-2222',
      status: 'recommended'
    },
    {
      id: 'jpj-my',
      name: 'Road Transport Department (JPJ)',
      country: 'Malaysia',
      logo: '🏛️',
      confidence: 78,
      responseTime: '2 hours',
      specialties: ['Vehicle registration', 'License violations', 'Commercial vehicles'],
      contact: '+60-3-8000-8000',
      status: 'alternative'
    },
    {
      id: 'traffic-sg',
      name: 'Singapore Traffic Police',
      country: 'Singapore',
      logo: '🚨',
      confidence: 45,
      responseTime: '20 minutes',
      specialties: ['Traffic enforcement', 'Road incidents', 'Cross-border violations'],
      contact: '+65-6547-0000',
      status: 'cross-border'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Load report data from previous level
          const savedReport = localStorage.getItem('currentReport');
          if (savedReport) {
            setReportData(JSON.parse(savedReport));
          }
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const startAnalysis = async () => {
    setIsAnalyzing(true);
    setCurrentPhase(0);

    for (let i = 0; i < analysisPhases.length; i++) {
      setCurrentPhase(i);
      await new Promise(resolve => setTimeout(resolve, analysisPhases[i].duration));
    }

    setIsAnalyzing(false);
    setAnalysisComplete(true);
    
    // Update demo progress
    localStorage.setItem('demoProgress', JSON.stringify({ level: 2, completed: true }));
  };

  const selectDepartment = (departmentId) => {
    const selectedDept = departments.find(d => d.id === departmentId);
    if (selectedDept && reportData) {
      const updatedReport = {
        ...reportData,
        assignedDepartment: selectedDept,
        status: 'routed',
        routingTimestamp: new Date().toISOString()
      };
      localStorage.setItem('currentReport', JSON.stringify(updatedReport));
      setReportData(updatedReport);
    }
  };

  return (
    <section id="demo-level-2" ref={sectionRef} className="min-h-screen py-20 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div 
            className={`inline-flex items-center gap-2 glass px-4 py-2 rounded-full mb-6 transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <Brain className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-gray-300">Demo Level 2</span>
          </div>
          
          <h2 
            className={`text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 transition-all duration-1000 delay-200 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <span className="text-white">AI-Powered </span>
            <span 
              className="text-neon"
              style={{
                background: 'linear-gradient(45deg, #00d4ff, #8b5cf6, #10b981)',
                backgroundSize: '200% 200%',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                color: 'transparent',
                animation: 'gradient-shift 3s ease infinite'
              }}
            >
              Smart Routing
            </span>
          </h2>
          
          <p 
            className={`text-xl text-gray-300 max-w-3xl mx-auto transition-all duration-1000 delay-400 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            Watch our advanced AI analyze your report and automatically route it to the 
            most appropriate government department with 99% accuracy.
          </p>
        </div>

        {/* Report Summary */}
        {reportData && (
          <div 
            className={`glass p-6 rounded-xl mb-12 transition-all duration-1000 delay-600 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <div className="flex items-center gap-4 mb-4">
              <FileText className="w-6 h-6 text-primary" />
              <h3 className="text-xl font-bold text-white">Report Summary</h3>
              <span className="text-sm text-gray-400">ID: {reportData.id}</span>
            </div>
            
            <div className="grid md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Category:</span>
                <span className="text-white ml-2 capitalize">{reportData.category}</span>
              </div>
              <div>
                <span className="text-gray-400">Location:</span>
                <span className="text-white ml-2">{reportData.location}</span>
              </div>
              <div>
                <span className="text-gray-400">Priority:</span>
                <span className="text-white ml-2 capitalize">{reportData.urgency}</span>
              </div>
            </div>
          </div>
        )}

        {!isAnalyzing && !analysisComplete && (
          /* Start Analysis */
          <div 
            className={`text-center transition-all duration-1000 delay-800 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <div className="glass p-12 rounded-2xl max-w-2xl mx-auto">
              <Brain className="w-16 h-16 text-primary mx-auto mb-6" />
              <h3 className="text-2xl font-bold text-white mb-4">Ready for AI Analysis</h3>
              <p className="text-gray-300 mb-8">
                Our advanced AI will analyze your report content, determine the appropriate 
                jurisdiction, and route it to the most suitable government department.
              </p>
              <button 
                onClick={startAnalysis}
                className="btn-primary px-8 py-4 text-lg flex items-center gap-3 mx-auto"
              >
                <Zap className="w-5 h-5" />
                Start AI Analysis
                <ArrowRight className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}

        {isAnalyzing && (
          /* Analysis in Progress */
          <div className="space-y-8">
            {/* Analysis Phases */}
            <div className="grid lg:grid-cols-2 gap-8">
              <div className="space-y-6">
                {analysisPhases.map((phase, index) => {
                  const Icon = phase.icon;
                  const isActive = index === currentPhase;
                  const isCompleted = index < currentPhase;
                  
                  return (
                    <div 
                      key={phase.id}
                      className={`glass p-6 rounded-xl transition-all duration-500 ${
                        isActive ? 'ring-2 ring-primary/50 scale-105' : ''
                      } ${isCompleted ? 'bg-green-500/10' : ''}`}
                    >
                      <div className="flex items-center gap-4">
                        <div 
                          className={`p-3 rounded-lg transition-all duration-300 ${
                            isCompleted ? 'bg-green-500' :
                            isActive ? 'bg-primary animate-pulse' : 'bg-gray-700'
                          }`}
                        >
                          {isCompleted ? (
                            <CheckCircle className="w-6 h-6 text-white" />
                          ) : (
                            <Icon className={`w-6 h-6 ${isActive ? 'text-white' : 'text-gray-400'}`} />
                          )}
                        </div>
                        
                        <div className="flex-1">
                          <h4 className={`text-lg font-semibold transition-colors duration-300 ${
                            isActive ? 'text-primary' : isCompleted ? 'text-green-400' : 'text-white'
                          }`}>
                            {phase.name}
                          </h4>
                          <p className="text-gray-400 text-sm">{phase.description}</p>
                        </div>
                        
                        {isActive && (
                          <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Real-time Visualization */}
              <div className="glass p-8 rounded-2xl">
                <h3 className="text-xl font-bold text-white mb-6">AI Processing Visualization</h3>
                
                <div className="space-y-6">
                  {/* Processing Indicators */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {Math.floor((currentPhase + 1) / analysisPhases.length * 100)}%
                      </div>
                      <div className="text-sm text-gray-400">Analysis Complete</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-400">
                        {Math.floor(Math.random() * 20 + 80)}%
                      </div>
                      <div className="text-sm text-gray-400">Confidence Score</div>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-primary to-green-400 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${((currentPhase + 1) / analysisPhases.length) * 100}%` }}
                    />
                  </div>

                  {/* Mock Data Flow */}
                  <div className="space-y-3">
                    <div className="text-sm text-gray-400">Processing Keywords:</div>
                    <div className="flex flex-wrap gap-2">
                      {['traffic', 'violation', 'speeding', 'malaysia', 'kuala lumpur'].map((keyword, index) => (
                        <span 
                          key={keyword}
                          className={`px-2 py-1 rounded text-xs transition-all duration-300 ${
                            index <= currentPhase ? 'bg-primary text-white' : 'bg-gray-700 text-gray-400'
                          }`}
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {analysisComplete && (
          /* Analysis Results */
          <div className="space-y-8">
            <div className="text-center">
              <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
              <h3 className="text-3xl font-bold text-white mb-2">Analysis Complete!</h3>
              <p className="text-gray-300">AI has identified the optimal departments for your report</p>
            </div>

            {/* Department Recommendations */}
            <div className="grid lg:grid-cols-3 gap-6">
              {departments.map((dept, index) => (
                <div 
                  key={dept.id}
                  className={`glass p-6 rounded-xl transition-all duration-500 hover-glow cursor-pointer ${
                    dept.status === 'recommended' ? 'ring-2 ring-green-400/50 bg-green-400/5' :
                    dept.status === 'alternative' ? 'ring-2 ring-yellow-400/50 bg-yellow-400/5' :
                    'ring-2 ring-blue-400/50 bg-blue-400/5'
                  }`}
                  onClick={() => selectDepartment(dept.id)}
                  style={{ animationDelay: `${index * 200}ms` }}
                >
                  {/* Status Badge */}
                  <div className="flex items-center justify-between mb-4">
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                      dept.status === 'recommended' ? 'bg-green-500 text-white' :
                      dept.status === 'alternative' ? 'bg-yellow-500 text-black' :
                      'bg-blue-500 text-white'
                    }`}>
                      {dept.status === 'recommended' ? '✅ Recommended' :
                       dept.status === 'alternative' ? '⚡ Alternative' :
                       '🌐 Cross-Border'}
                    </div>
                    <div className="text-2xl">{dept.logo}</div>
                  </div>

                  {/* Department Info */}
                  <h4 className="text-lg font-bold text-white mb-2">{dept.name}</h4>
                  <p className="text-sm text-gray-400 mb-4">{dept.country}</p>

                  {/* Confidence & Response Time */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <div className="text-sm text-gray-400">Confidence</div>
                      <div className={`text-lg font-bold ${
                        dept.confidence >= 90 ? 'text-green-400' :
                        dept.confidence >= 70 ? 'text-yellow-400' : 'text-orange-400'
                      }`}>
                        {dept.confidence}%
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-400">Response Time</div>
                      <div className="text-lg font-bold text-white">{dept.responseTime}</div>
                    </div>
                  </div>

                  {/* Specialties */}
                  <div className="mb-4">
                    <div className="text-sm text-gray-400 mb-2">Specialties</div>
                    <div className="space-y-1">
                      {dept.specialties.slice(0, 2).map((specialty, idx) => (
                        <div key={idx} className="text-xs text-gray-300">• {specialty}</div>
                      ))}
                    </div>
                  </div>

                  {/* Contact */}
                  <div className="text-xs text-gray-400 mb-4">
                    📞 {dept.contact}
                  </div>

                  {/* Select Button */}
                  <button 
                    className={`w-full py-2 px-4 rounded-lg font-medium transition-all duration-300 ${
                      dept.status === 'recommended' 
                        ? 'btn-primary' 
                        : 'glass border border-gray-600 text-white hover:border-primary'
                    }`}
                  >
                    {dept.status === 'recommended' ? 'Select (Recommended)' : 'Select Department'}
                  </button>
                </div>
              ))}
            </div>

            {/* Continue Button */}
            <div className="text-center">
              <button 
                onClick={() => document.getElementById('demo-level-3')?.scrollIntoView({ behavior: 'smooth' })}
                className="btn-primary px-8 py-4 text-lg flex items-center gap-3 mx-auto"
              >
                Continue to Real-Time Tracking
                <ArrowRight className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </section>
  );
}
