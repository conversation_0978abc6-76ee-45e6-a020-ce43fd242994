/**
 * @typedef {object} Phaser.Types.Core.PhysicsConfig
 * @since 3.0.0
 *
 * @property {string} [default] - The default physics system. It will be started for each scene. Phaser provides 'arcade', 'impact', and 'matter'.
 * @property {Phaser.Types.Physics.Arcade.ArcadeWorldConfig} [arcade] - Arcade Physics configuration.
 * @property {Phaser.Types.Physics.Matter.MatterWorldConfig} [matter] - Matter Physics configuration.
 */
