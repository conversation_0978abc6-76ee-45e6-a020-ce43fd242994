/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * Phaser Tilemap constants for orientation.
 *
 * @namespace Phaser.Tilemaps.Orientation
 * @memberof Phaser.Tilemaps
 * @since 3.50.0
 */

/**
 * Phaser Tilemap constants for orientation.
 *
 * To find out what each mode does please see [Phaser.Tilemaps.Orientation]{@link Phaser.Tilemaps.Orientation}.
 *
 * @typedef {Phaser.Tilemaps.Orientation} Phaser.Tilemaps.OrientationType
 * @memberof Phaser.Tilemaps
 * @since 3.50.0
 */

module.exports = {

    /**
     * Orthogonal Tilemap orientation constant.
     *
     * @name Phaser.Tilemaps.Orientation.ORTHOGONAL
     * @type {number}
     * @const
     * @since 3.50.0
     */
    ORTHOGONAL: 0,

    /**
     * Isometric Tilemap orientation constant.
     *
     * @name Phaser.Tilemaps.Orientation.ISOMETRIC
     * @type {number}
     * @const
     * @since 3.50.0
     */
    ISOMETRIC: 1,

    /**
     * Staggered Tilemap orientation constant.
     *
     * @name Phaser.Tilemaps.Orientation.STAGGERED
     * @type {number}
     * @const
     * @since 3.50.0
     */
    STAGGERED: 2,

    /**
     * Hexagonal Tilemap orientation constant.
     *
     * @name Phaser.Tilemaps.Orientation.HEXAGONAL
     * @type {number}
     * @const
     * @since 3.50.0
     */
    HEXAGONAL: 3

};
