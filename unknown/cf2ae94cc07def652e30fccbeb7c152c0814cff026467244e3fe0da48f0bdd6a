module.exports = [
    '#define SHADER_NAME PHASER_MESH_FS',
    'precision mediump float;',
    'uniform vec3 uLightPosition;',
    'uniform vec3 uLightAmbient;',
    'uniform vec3 uLightDiffuse;',
    'uniform vec3 uLightSpecular;',
    'uniform vec3 uFogColor;',
    'uniform float uFogNear;',
    'uniform float uFogFar;',
    'uniform vec3 uMaterialAmbient;',
    'uniform vec3 uMaterialDiffuse;',
    'uniform vec3 uMaterialSpecular;',
    'uniform float uMaterialShine;',
    'uniform vec3 uCameraPosition;',
    'uniform sampler2D uTexture;',
    'varying vec2 vTextureCoord;',
    'varying vec3 vNormal;',
    'varying vec3 vPosition;',
    'void main (void)',
    '{',
    '    vec4 color = texture2D(uTexture, vTextureCoord);',
    '    vec3 ambient = uLightAmbient * uMaterialAmbient;',
    '    vec3 norm = normalize(vNormal);',
    '    vec3 lightDir = normalize(uLightPosition - vPosition);',
    '    float diff = max(dot(norm, lightDir), 0.0);',
    '    vec3 diffuse = uLightDiffuse * (diff * uMaterialDiffuse);',
    '    vec3 viewDir = normalize(uCameraPosition - vPosition);',
    '    vec3 reflectDir = reflect(-lightDir, norm);',
    '    float spec = pow(max(dot(viewDir, reflectDir), 0.0), uMaterialShine);',
    '    vec3 specular = uLightSpecular * (spec * uMaterialSpecular);',
    '    vec3 result = (ambient + diffuse + specular) * color.rgb;',
    '    float depth = gl_FragCoord.z / gl_FragCoord.w;',
    '    float fogFactor = smoothstep(uFogNear, uFogFar, depth);',
    '    gl_FragColor.rgb = mix(result.rgb, uFogColor, fogFactor);',
    '    gl_FragColor.a = color.a;',
    '}',
].join('\n');
