@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

:root {
  /* Dark Theme Colors */
  --background: #0a0a0f;
  --background-secondary: #0f0f1a;
  --background-tertiary: #1a1a2e;
  --foreground: #ffffff;
  --foreground-secondary: #e2e8f0;
  --foreground-muted: #94a3b8;

  /* Neon Accent Colors */
  --primary: #00d4ff;
  --primary-dark: #0099cc;
  --secondary: #8b5cf6;
  --secondary-dark: #7c3aed;
  --accent: #10b981;
  --accent-dark: #059669;
  --warning: #f59e0b;
  --danger: #ef4444;

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Aurora Gradients */
  --aurora-1: linear-gradient(45deg, #00d4ff, #8b5cf6);
  --aurora-2: linear-gradient(135deg, #8b5cf6, #10b981);
  --aurora-3: linear-gradient(225deg, #10b981, #00d4ff);

  /* Animations */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  overflow-x: hidden;
  min-height: 100vh;
  position: relative;
}

/* Animated Background */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  animation: aurora 20s ease-in-out infinite;
  z-index: -1;
}

@keyframes aurora {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1) rotate(0deg);
  }
  33% {
    opacity: 0.6;
    transform: scale(1.1) rotate(120deg);
  }
  66% {
    opacity: 0.4;
    transform: scale(0.9) rotate(240deg);
  }
}

/* Glassmorphism Base Class */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

/* Hover Effects */
.hover-glow {
  transition: all var(--transition-normal);
}

.hover-glow:hover {
  box-shadow:
    0 0 20px rgba(0, 212, 255, 0.3),
    0 8px 32px 0 rgba(31, 38, 135, 0.37);
  transform: translateY(-2px);
}

/* Button Styles */
.btn-primary {
  background: var(--aurora-1);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 212, 255, 0.4);
}

/* Text Glow Effects */
.text-glow {
  text-shadow: 0 0 10px currentColor;
}

.text-neon {
  color: var(--primary);
  text-shadow:
    0 0 5px var(--primary),
    0 0 10px var(--primary),
    0 0 15px var(--primary);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Selection Styling */
::selection {
  background: var(--primary);
  color: white;
}
