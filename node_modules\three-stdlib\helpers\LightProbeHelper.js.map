{"version": 3, "file": "LightProbeHelper.js", "sources": ["../../src/helpers/LightProbeHelper.js"], "sourcesContent": ["import { Mesh, ShaderMaterial, SphereGeometry } from 'three'\n\nclass LightProbeHelper extends Mesh {\n  constructor(lightProbe, size) {\n    const material = new ShaderMaterial({\n      type: 'LightProbeHelperMaterial',\n\n      uniforms: {\n        sh: { value: lightProbe.sh.coefficients }, // by reference\n\n        intensity: { value: lightProbe.intensity },\n      },\n\n      vertexShader: [\n        'varying vec3 vNormal;',\n\n        'void main() {',\n\n        '\tvNormal = normalize( normalMatrix * normal );',\n\n        '\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );',\n\n        '}',\n      ].join('\\n'),\n\n      fragmentShader: [\n        '#define RECIPROCAL_PI 0.318309886',\n\n        'vec3 inverseTransformDirection( in vec3 normal, in mat4 matrix ) {',\n\n        '\t// matrix is assumed to be orthogonal',\n\n        '\treturn normalize( ( vec4( normal, 0.0 ) * matrix ).xyz );',\n\n        '}',\n\n        '// source: https://graphics.stanford.edu/papers/envmap/envmap.pdf',\n        'vec3 shGetIrradianceAt( in vec3 normal, in vec3 shCoefficients[ 9 ] ) {',\n\n        '\t// normal is assumed to have unit length',\n\n        '\tfloat x = normal.x, y = normal.y, z = normal.z;',\n\n        '\t// band 0',\n        '\tvec3 result = shCoefficients[ 0 ] * 0.886227;',\n\n        '\t// band 1',\n        '\tresult += shCoefficients[ 1 ] * 2.0 * 0.511664 * y;',\n        '\tresult += shCoefficients[ 2 ] * 2.0 * 0.511664 * z;',\n        '\tresult += shCoefficients[ 3 ] * 2.0 * 0.511664 * x;',\n\n        '\t// band 2',\n        '\tresult += shCoefficients[ 4 ] * 2.0 * 0.429043 * x * y;',\n        '\tresult += shCoefficients[ 5 ] * 2.0 * 0.429043 * y * z;',\n        '\tresult += shCoefficients[ 6 ] * ( 0.743125 * z * z - 0.247708 );',\n        '\tresult += shCoefficients[ 7 ] * 2.0 * 0.429043 * x * z;',\n        '\tresult += shCoefficients[ 8 ] * 0.429043 * ( x * x - y * y );',\n\n        '\treturn result;',\n\n        '}',\n\n        'uniform vec3 sh[ 9 ]; // sh coefficients',\n\n        'uniform float intensity; // light probe intensity',\n\n        'varying vec3 vNormal;',\n\n        'void main() {',\n\n        '\tvec3 normal = normalize( vNormal );',\n\n        '\tvec3 worldNormal = inverseTransformDirection( normal, viewMatrix );',\n\n        '\tvec3 irradiance = shGetIrradianceAt( worldNormal, sh );',\n\n        '\tvec3 outgoingLight = RECIPROCAL_PI * irradiance * intensity;',\n\n        '\tgl_FragColor = linearToOutputTexel( vec4( outgoingLight, 1.0 ) );',\n\n        '}',\n      ].join('\\n'),\n    })\n\n    const geometry = new SphereGeometry(1, 32, 16)\n\n    super(geometry, material)\n\n    this.lightProbe = lightProbe\n    this.size = size\n    this.type = 'LightProbeHelper'\n\n    this.onBeforeRender()\n  }\n\n  dispose() {\n    this.geometry.dispose()\n    this.material.dispose()\n  }\n\n  onBeforeRender() {\n    this.position.copy(this.lightProbe.position)\n\n    this.scale.set(1, 1, 1).multiplyScalar(this.size)\n\n    this.material.uniforms.intensity.value = this.lightProbe.intensity\n  }\n}\n\nexport { LightProbeHelper }\n"], "names": [], "mappings": ";AAEA,MAAM,yBAAyB,KAAK;AAAA,EAClC,YAAY,YAAY,MAAM;AAC5B,UAAM,WAAW,IAAI,eAAe;AAAA,MAClC,MAAM;AAAA,MAEN,UAAU;AAAA,QACR,IAAI,EAAE,OAAO,WAAW,GAAG,aAAc;AAAA;AAAA,QAEzC,WAAW,EAAE,OAAO,WAAW,UAAW;AAAA,MAC3C;AAAA,MAED,cAAc;AAAA,QACZ;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,MACR,EAAQ,KAAK,IAAI;AAAA,MAEX,gBAAgB;AAAA,QACd;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QACA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QACA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,QAEA;AAAA,MACR,EAAQ,KAAK,IAAI;AAAA,IACjB,CAAK;AAED,UAAM,WAAW,IAAI,eAAe,GAAG,IAAI,EAAE;AAE7C,UAAM,UAAU,QAAQ;AAExB,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,SAAK,OAAO;AAEZ,SAAK,eAAgB;AAAA,EACtB;AAAA,EAED,UAAU;AACR,SAAK,SAAS,QAAS;AACvB,SAAK,SAAS,QAAS;AAAA,EACxB;AAAA,EAED,iBAAiB;AACf,SAAK,SAAS,KAAK,KAAK,WAAW,QAAQ;AAE3C,SAAK,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAe,KAAK,IAAI;AAEhD,SAAK,SAAS,SAAS,UAAU,QAAQ,KAAK,WAAW;AAAA,EAC1D;AACH;"}