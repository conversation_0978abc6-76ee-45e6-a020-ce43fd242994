"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("react-dom/client"),n=require("three"),a=require("@react-three/fiber"),o=require("zustand"),i=require("@use-gesture/react"),s=require("maath"),l=require("@react-spring/three"),c=require("zustand/middleware"),u=require("three-stdlib"),d=require("zustand/shallow"),m=require("troika-three-text"),f=require("suspend-react"),p=require("meshline"),h=require("camera-controls"),x=require("hls.js"),y=require("stats.js"),v=require("stats-gl"),g=require("detect-gpu"),w=require("three-mesh-bvh"),z=require("react-composer"),b=require("@monogrid/gainmap-js"),E=require("tunnel-rat");function M(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function S(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var T=M(e),C=S(t),P=S(r),R=S(n),D=M(h),F=M(y),k=M(v),_=M(z),A=M(E);const L=new n.Vector3,I=new n.Vector3,B=new n.Vector3,V=new n.Vector2;function U(e,t,r){const n=L.setFromMatrixPosition(e.matrixWorld);n.project(t);const a=r.width/2,o=r.height/2;return[n.x*a+a,-n.y*o+o]}const O=e=>Math.abs(e)<1e-10?0:e;function N(e,t,r=""){let n="matrix3d(";for(let r=0;16!==r;r++)n+=O(t[r]*e.elements[r])+(15!==r?",":")");return r+n}const j=(W=[1,-1,1,1,1,-1,1,1,1,-1,1,1,1,-1,1,1],e=>N(e,W));var W;const G=(e,t)=>{return N(e,[1/(r=t),1/r,1/r,1,-1/r,-1/r,-1/r,-1,1/r,1/r,1/r,1,1,1,1,1],"translate(-50%,-50%)");var r};const H=C.forwardRef((({children:e,eps:t=.001,style:r,className:o,prepend:i,center:s,fullscreen:l,portal:c,distanceFactor:u,sprite:d=!1,transform:m=!1,occlude:f,onOcclude:p,castShadow:h,receiveShadow:x,material:y,geometry:v,zIndexRange:g=[16777271,0],calculatePosition:w=U,as:z="div",wrapperClass:b,pointerEvents:E="auto",...M},S)=>{const{gl:R,camera:D,scene:F,size:k,raycaster:_,events:A,viewport:N}=a.useThree(),[W]=C.useState((()=>document.createElement(z))),H=C.useRef(),$=C.useRef(null),q=C.useRef(0),X=C.useRef([0,0]),Z=C.useRef(null),Y=C.useRef(null),Q=(null==c?void 0:c.current)||A.connected||R.domElement.parentNode,K=C.useRef(null),J=C.useRef(!1),ee=C.useMemo((()=>f&&"blending"!==f||Array.isArray(f)&&f.length&&function(e){return e&&"object"==typeof e&&"current"in e}(f[0])),[f]);C.useLayoutEffect((()=>{const e=R.domElement;f&&"blending"===f?(e.style.zIndex=`${Math.floor(g[0]/2)}`,e.style.position="absolute",e.style.pointerEvents="none"):(e.style.zIndex=null,e.style.position=null,e.style.pointerEvents=null)}),[f]),C.useLayoutEffect((()=>{if($.current){const e=H.current=P.createRoot(W);if(F.updateMatrixWorld(),m)W.style.cssText="position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;";else{const e=w($.current,D,k);W.style.cssText=`position:absolute;top:0;left:0;transform:translate3d(${e[0]}px,${e[1]}px,0);transform-origin:0 0;`}return Q&&(i?Q.prepend(W):Q.appendChild(W)),()=>{Q&&Q.removeChild(W),e.unmount()}}}),[Q,m]),C.useLayoutEffect((()=>{b&&(W.className=b)}),[b]);const te=C.useMemo((()=>m?{position:"absolute",top:0,left:0,width:k.width,height:k.height,transformStyle:"preserve-3d",pointerEvents:"none"}:{position:"absolute",transform:s?"translate3d(-50%,-50%,0)":"none",...l&&{top:-k.height/2,left:-k.width/2,width:k.width,height:k.height},...r}),[r,s,l,k,m]),re=C.useMemo((()=>({position:"absolute",pointerEvents:E})),[E]);C.useLayoutEffect((()=>{var t,n;(J.current=!1,m)?null==(t=H.current)||t.render(C.createElement("div",{ref:Z,style:te},C.createElement("div",{ref:Y,style:re},C.createElement("div",{ref:S,className:o,style:r,children:e})))):null==(n=H.current)||n.render(C.createElement("div",{ref:S,style:te,className:o,children:e}))}));const ne=C.useRef(!0);a.useFrame((e=>{if($.current){D.updateMatrixWorld(),$.current.updateWorldMatrix(!0,!1);const e=m?X.current:w($.current,D,k);if(m||Math.abs(q.current-D.zoom)>t||Math.abs(X.current[0]-e[0])>t||Math.abs(X.current[1]-e[1])>t){const t=function(e,t){const r=L.setFromMatrixPosition(e.matrixWorld),n=I.setFromMatrixPosition(t.matrixWorld),a=r.sub(n),o=t.getWorldDirection(B);return a.angleTo(o)>Math.PI/2}($.current,D);let r=!1;ee&&(Array.isArray(f)?r=f.map((e=>e.current)):"blending"!==f&&(r=[F]));const a=ne.current;if(r){const e=function(e,t,r,n){const a=L.setFromMatrixPosition(e.matrixWorld),o=a.clone();o.project(t),V.set(o.x,o.y),r.setFromCamera(V,t);const i=r.intersectObjects(n,!0);if(i.length){const e=i[0].distance;return a.distanceTo(r.ray.origin)<e}return!0}($.current,D,_,r);ne.current=e&&!t}else ne.current=!t;a!==ne.current&&(p?p(!ne.current):W.style.display=ne.current?"block":"none");const o=Math.floor(g[0]/2),i=f?ee?[g[0],o]:[o-1,0]:g;if(W.style.zIndex=`${function(e,t,r){if(t instanceof n.PerspectiveCamera||t instanceof n.OrthographicCamera){const n=L.setFromMatrixPosition(e.matrixWorld),a=I.setFromMatrixPosition(t.matrixWorld),o=n.distanceTo(a),i=(r[1]-r[0])/(t.far-t.near),s=r[1]-i*t.far;return Math.round(i*o+s)}}($.current,D,i)}`,m){const[e,t]=[k.width/2,k.height/2],r=D.projectionMatrix.elements[5]*t,{isOrthographicCamera:n,top:a,left:o,bottom:i,right:s}=D,l=j(D.matrixWorldInverse),c=n?`scale(${r})translate(${O(-(s+o)/2)}px,${O((a+i)/2)}px)`:`translateZ(${r}px)`;let m=$.current.matrixWorld;d&&(m=D.matrixWorldInverse.clone().transpose().copyPosition(m).scale($.current.scale),m.elements[3]=m.elements[7]=m.elements[11]=0,m.elements[15]=1),W.style.width=k.width+"px",W.style.height=k.height+"px",W.style.perspective=n?"":`${r}px`,Z.current&&Y.current&&(Z.current.style.transform=`${c}${l}translate(${e}px,${t}px)`,Y.current.style.transform=G(m,1/((u||10)/400)))}else{const t=void 0===u?1:function(e,t){if(t instanceof n.OrthographicCamera)return t.zoom;if(t instanceof n.PerspectiveCamera){const r=L.setFromMatrixPosition(e.matrixWorld),n=I.setFromMatrixPosition(t.matrixWorld),a=t.fov*Math.PI/180,o=r.distanceTo(n);return 1/(2*Math.tan(a/2)*o)}return 1}($.current,D)*u;W.style.transform=`translate3d(${e[0]}px,${e[1]}px,0) scale(${t})`}X.current=e,q.current=D.zoom}}if(!ee&&K.current&&!J.current)if(m){if(Z.current){const e=Z.current.children[0];if(null!=e&&e.clientWidth&&null!=e&&e.clientHeight){const{isOrthographicCamera:t}=D;if(t||v)M.scale&&(Array.isArray(M.scale)?M.scale instanceof n.Vector3?K.current.scale.copy(M.scale.clone().divideScalar(1)):K.current.scale.set(1/M.scale[0],1/M.scale[1],1/M.scale[2]):K.current.scale.setScalar(1/M.scale));else{const t=(u||10)/400,r=e.clientWidth*t,n=e.clientHeight*t;K.current.scale.set(r,n,1)}J.current=!0}}}else{const t=W.children[0];if(null!=t&&t.clientWidth&&null!=t&&t.clientHeight){const e=1/N.factor,r=t.clientWidth*e,n=t.clientHeight*e;K.current.scale.set(r,n,1),J.current=!0}K.current.lookAt(e.camera.position)}}));const ae=C.useMemo((()=>({vertexShader:m?void 0:'\n          /*\n            This shader is from the THREE\'s SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if "transfrom"\n            is false.\n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n\n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ',fragmentShader:"\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      "})),[m]);return C.createElement("group",T.default({},M,{ref:$}),f&&!ee&&C.createElement("mesh",{castShadow:h,receiveShadow:x,ref:K},v||C.createElement("planeGeometry",null),y||C.createElement("shaderMaterial",{side:n.DoubleSide,vertexShader:ae.vertexShader,fragmentShader:ae.fragmentShader})))}));let $=0;const q=o.create((e=>(n.DefaultLoadingManager.onStart=(t,r,n)=>{e({active:!0,item:t,loaded:r,total:n,progress:(r-$)/(n-$)*100})},n.DefaultLoadingManager.onLoad=()=>{e({active:!1})},n.DefaultLoadingManager.onError=t=>e((e=>({errors:[...e.errors,t]}))),n.DefaultLoadingManager.onProgress=(t,r,n)=>{r===n&&($=n),e({active:!0,item:t,loaded:r,total:n,progress:(r-$)/(n-$)*100||100})},{errors:[],active:!1,progress:0,item:"",loaded:0,total:0})));const X=e=>`Loading ${e.toFixed(2)}%`;const Z={container:{position:"absolute",top:0,left:0,width:"100%",height:"100%",background:"#171717",display:"flex",alignItems:"center",justifyContent:"center",transition:"opacity 300ms ease",zIndex:1e3},inner:{width:100,height:3,background:"#272727",textAlign:"center"},bar:{height:3,width:"100%",background:"white",transition:"transform 200ms",transformOrigin:"left center"},data:{display:"inline-block",position:"relative",fontVariantNumeric:"tabular-nums",marginTop:"0.8em",color:"#f0f0f0",fontSize:"0.6em",fontFamily:'-apple-system, BlinkMacSystemFont, "Inter", "Segoe UI", "Helvetica Neue", Helvetica, Arial, Roboto, Ubuntu, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',whiteSpace:"nowrap"}},Y=C.createContext(null);function Q(){return C.useContext(Y)}const K=C.forwardRef((({children:e},t)=>{const r=C.useRef(null);C.useImperativeHandle(t,(()=>r.current),[]);const n=Q(),{width:o,height:i}=a.useThree((e=>e.viewport));return a.useFrame((()=>{r.current.position.x=n.horizontal?-o*(n.pages-1)*n.offset:0,r.current.position.y=n.horizontal?0:i*(n.pages-1)*n.offset})),C.createElement("group",{ref:r},e)})),J=C.forwardRef((({children:e,style:t,...r},n)=>{const o=Q(),i=C.useRef(null);C.useImperativeHandle(n,(()=>i.current),[]);const{width:s,height:l}=a.useThree((e=>e.size)),c=C.useContext(a.context),u=C.useMemo((()=>P.createRoot(o.fixed)),[o.fixed]);return a.useFrame((()=>{o.delta>o.eps&&(i.current.style.transform=`translate3d(${o.horizontal?-s*(o.pages-1)*o.offset:0}px,${o.horizontal?0:l*(o.pages-1)*-o.offset}px,0)`)})),u.render(C.createElement("div",T.default({ref:i,style:{...t,position:"absolute",top:0,left:0,willChange:"transform"}},r),C.createElement(Y.Provider,{value:o},C.createElement(a.context.Provider,{value:c},e)))),null})),ee=C.forwardRef((({html:e,...t},r)=>{const n=e?J:K;return C.createElement(n,T.default({ref:r},t))}));const te=C.createContext(null);const re=C.createContext([]);const ne=C.forwardRef((function({children:e,follow:t=!0,lockX:r=!1,lockY:o=!1,lockZ:i=!1,...s},l){const c=C.useRef(null),u=C.useRef(null),d=new n.Quaternion;return a.useFrame((({camera:e})=>{if(!t||!u.current)return;const n=c.current.rotation.clone();u.current.updateMatrix(),u.current.updateWorldMatrix(!1,!1),u.current.getWorldQuaternion(d),e.getWorldQuaternion(c.current.quaternion).premultiply(d.invert()),r&&(c.current.rotation.x=n.x),o&&(c.current.rotation.y=n.y),i&&(c.current.rotation.z=n.z)})),C.useImperativeHandle(l,(()=>u.current),[]),C.createElement("group",T.default({ref:u},s),C.createElement("group",{ref:c},e))})),ae=C.forwardRef((({children:e,depth:t=-1,...r},n)=>{const o=C.useRef(null);return C.useImperativeHandle(n,(()=>o.current),[]),a.useFrame((({camera:e})=>{o.current.quaternion.copy(e.quaternion),o.current.position.copy(e.position)})),C.createElement("group",T.default({ref:o},r),C.createElement("group",{"position-z":-t},e))})),oe=new R.Vector3,ie=new R.Vector3,se=new R.Vector3,le=(e,t,r,n=1)=>{const a=oe.set(e.x/r.width*2-1,-e.y/r.height*2+1,n);return a.unproject(t),a},ce=(e,t,r,n)=>{const a=((e,t,r)=>{const n=r.width/2,a=r.height/2;t.updateMatrixWorld(!1);const o=e.project(t);return o.x=o.x*n+n,o.y=-o.y*a+a,o})(se.copy(e),r,n);let o=0;for(let i=0;i<2;++i){const s=ie.copy(a).setComponent(i,a.getComponent(i)+t),l=le(s,r,n,s.z);o=Math.max(o,e.distanceTo(l))}return o},ue=new n.Vector3,de=t.forwardRef((({scale:e=1,...r},n)=>{const o=t.useRef(null);return C.useImperativeHandle(n,(()=>o.current),[]),a.useFrame((t=>{const r=o.current;if(!r)return;const n=ce(r.getWorldPosition(ue),e,t.camera,t.size);r.scale.setScalar(n*e)})),C.createElement("object3D",T.default({ref:o},r))})),me=C.forwardRef((function({points:e,color:t=16777215,vertexColors:r,linewidth:o,lineWidth:i,segments:s,dashed:l,...c},d){var m,f;const p=a.useThree((e=>e.size)),h=C.useMemo((()=>s?new u.LineSegments2:new u.Line2),[s]),[x]=C.useState((()=>new u.LineMaterial)),y=4===(null==r||null==(m=r[0])?void 0:m.length)?4:3,v=C.useMemo((()=>{const a=s?new u.LineSegmentsGeometry:new u.LineGeometry,o=e.map((e=>{const t=Array.isArray(e);return e instanceof n.Vector3||e instanceof n.Vector4?[e.x,e.y,e.z]:e instanceof n.Vector2?[e.x,e.y,0]:t&&3===e.length?[e[0],e[1],e[2]]:t&&2===e.length?[e[0],e[1],0]:e}));if(a.setPositions(o.flat()),r){t=16777215;const e=r.map((e=>e instanceof n.Color?e.toArray():e));a.setColors(e.flat(),y)}return a}),[e,s,r,y]);return C.useLayoutEffect((()=>{h.computeLineDistances()}),[e,h]),C.useLayoutEffect((()=>{l?x.defines.USE_DASH="":delete x.defines.USE_DASH,x.needsUpdate=!0}),[l,x]),C.useEffect((()=>()=>{v.dispose(),x.dispose()}),[v]),C.createElement("primitive",T.default({object:h,ref:d},c),C.createElement("primitive",{object:v,attach:"geometry"}),C.createElement("primitive",T.default({object:x,attach:"material",color:t,vertexColors:Boolean(r),resolution:[p.width,p.height],linewidth:null!==(f=null!=o?o:i)&&void 0!==f?f:1,dashed:l,transparent:4===y},c)))})),fe=new n.Vector3,pe=C.forwardRef((function({start:e=[0,0,0],end:t=[0,0,0],mid:r,segments:a=20,...o},i){const s=C.useRef(null);C.useImperativeHandle(i,(()=>s.current));const[l]=C.useState((()=>new n.QuadraticBezierCurve3(void 0,void 0,void 0))),c=C.useCallback(((e,t,r,a=20)=>(e instanceof n.Vector3?l.v0.copy(e):l.v0.set(...e),t instanceof n.Vector3?l.v2.copy(t):l.v2.set(...t),r instanceof n.Vector3?l.v1.copy(r):Array.isArray(r)?l.v1.set(...r):l.v1.copy(l.v0.clone().add(l.v2.clone().sub(l.v0)).add(fe.set(0,l.v0.y-l.v2.y,0))),l.getPoints(a))),[]);C.useLayoutEffect((()=>{s.current.setPoints=(e,t,r)=>{const n=c(e,t,r);s.current.geometry&&s.current.geometry.setPositions(n.map((e=>e.toArray())).flat())}}),[]);const u=C.useMemo((()=>c(e,t,r,a)),[e,t,r,a]);return C.createElement(me,T.default({ref:s,points:u},o))})),he=C.forwardRef((function({start:e,end:t,midA:r,midB:a,segments:o=20,...i},s){const l=C.useMemo((()=>{const i=e instanceof n.Vector3?e:new n.Vector3(...e),s=t instanceof n.Vector3?t:new n.Vector3(...t),l=r instanceof n.Vector3?r:new n.Vector3(...r),c=a instanceof n.Vector3?a:new n.Vector3(...a);return new n.CubicBezierCurve3(i,l,c,s).getPoints(o)}),[e,t,r,a,o]);return C.createElement(me,T.default({ref:s,points:l},i))})),xe=C.forwardRef((function({points:e,closed:t=!1,curveType:r="centripetal",tension:a=.5,segments:o=20,vertexColors:i,...s},l){const c=C.useMemo((()=>{const o=e.map((e=>e instanceof n.Vector3?e:new n.Vector3(...e)));return new n.CatmullRomCurve3(o,t,r,a)}),[e,t,r,a]),u=C.useMemo((()=>c.getPoints(o)),[c,o]),d=C.useMemo((()=>{if(!i||i.length<2)return;if(i.length===o+1)return i;const e=i.map((e=>e instanceof n.Color?e:new n.Color(...e)));t&&e.push(e[0].clone());const r=[e[0]],a=o/(e.length-1);for(let t=1;t<o;t++){const n=t%a/a,o=Math.floor(t/a);r.push(e[o].clone().lerp(e[o+1],n))}return r.push(e[e.length-1]),r}),[i,o]);return C.createElement(me,T.default({ref:l,points:u,vertexColors:d},s))})),ye=C.forwardRef((({url:e,distance:t=1,loop:r=!0,autoplay:o,...i},s)=>{const l=C.useRef(null);C.useImperativeHandle(s,(()=>l.current),[]);const c=a.useThree((({camera:e})=>e)),[u]=C.useState((()=>new n.AudioListener)),d=a.useLoader(n.AudioLoader,e);return C.useEffect((()=>{const e=l.current;e&&(e.setBuffer(d),e.setRefDistance(t),e.setLoop(r),o&&!e.isPlaying&&e.play())}),[d,c,t,r]),C.useEffect((()=>{const e=l.current;return c.add(u),()=>{c.remove(u),e&&(e.isPlaying&&e.stop(),e.source&&e.source._connected&&e.disconnect())}}),[]),C.createElement("positionalAudio",T.default({ref:l,args:[u]},i))})),ve=C.forwardRef((({sdfGlyphSize:e=64,anchorX:t="center",anchorY:r="middle",font:n,fontSize:o=1,children:i,characters:s,onSync:l,...c},u)=>{const d=a.useThree((({invalidate:e})=>e)),[p]=C.useState((()=>new m.Text)),[h,x]=C.useMemo((()=>{const e=[];let t="";return C.Children.forEach(i,(r=>{"string"==typeof r||"number"==typeof r?t+=r:e.push(r)})),[e,t]}),[i]);return f.suspend((()=>new Promise((e=>m.preloadFont({font:n,characters:s},e)))),["troika-text",n,s]),C.useLayoutEffect((()=>{p.sync((()=>{d(),l&&l(p)}))})),C.useEffect((()=>()=>p.dispose()),[p]),C.createElement("primitive",T.default({object:p,ref:u,font:n,text:x,anchorX:t,anchorY:r,fontSize:o,sdfGlyphSize:e},c),h)}));let ge=null;async function we(e){const t=await async function(e){return"string"==typeof e?await(await fetch(e)).json():e}(e);return r=t,ge||(ge=new u.FontLoader),ge.parse(r);var r}function ze(e){return f.suspend(we,[e])}ze.preload=e=>f.preload(we,[e]),ze.clear=e=>f.clear([e]);const be=["string","number"],Ee=C.forwardRef((({font:e,letterSpacing:r=0,lineHeight:n=1,size:o=1,height:i=.2,bevelThickness:s=.1,bevelSize:l=.01,bevelEnabled:c=!1,bevelOffset:d=0,bevelSegments:m=4,curveSegments:f=8,smooth:p,children:h,...x},y)=>{C.useMemo((()=>a.extend({RenamedTextGeometry:u.TextGeometry})),[]);const v=C.useRef(null),g=ze(e),w=t.useMemo((()=>({font:g,size:o,height:i,bevelThickness:s,bevelSize:l,bevelEnabled:c,bevelSegments:m,bevelOffset:d,curveSegments:f,letterSpacing:r,lineHeight:n})),[g,o,i,s,l,c,m,d,f,r,n]),[z,...b]=t.useMemo((()=>(e=>{let t="";const r=[];return C.Children.forEach(e,(e=>{be.includes(typeof e)?t+=e+"":r.push(e)})),[t,...r]})(h)),[h]),E=C.useMemo((()=>[z,w]),[z,w]);return C.useLayoutEffect((()=>{p&&(v.current.geometry=u.mergeVertices(v.current.geometry,p),v.current.geometry.computeVertexNormals())}),[E,p]),C.useImperativeHandle(y,(()=>v.current),[]),C.createElement("mesh",T.default({},x,{ref:v}),C.createElement("renamedTextGeometry",{args:E}),b)})),Me=C.forwardRef((({children:e,multisamping:t=8,renderIndex:r=1,disableRender:o,disableGamma:i,disableRenderPass:s,depthBuffer:l=!0,stencilBuffer:c=!1,anisotropy:d=1,encoding:m,type:f,...p},h)=>{C.useMemo((()=>a.extend({EffectComposer:u.EffectComposer,RenderPass:u.RenderPass,ShaderPass:u.ShaderPass})),[]);const x=C.useRef(null);C.useImperativeHandle(h,(()=>x.current),[]);const{scene:y,camera:v,gl:g,size:w,viewport:z}=a.useThree(),[b]=C.useState((()=>{const e=new n.WebGLRenderTarget(w.width,w.height,{type:f||n.HalfFloatType,format:n.RGBAFormat,depthBuffer:l,stencilBuffer:c,anisotropy:d});return f===n.UnsignedByteType&&null!=m&&("colorSpace"in e?e.texture.colorSpace=m:e.texture.encoding=m),e.samples=t,e}));C.useEffect((()=>{var e,t;null==(e=x.current)||e.setSize(w.width,w.height),null==(t=x.current)||t.setPixelRatio(z.dpr)}),[g,w,z.dpr]),a.useFrame((()=>{var e;o||null==(e=x.current)||e.render()}),r);const E=[];return s||E.push(C.createElement("renderPass",{key:"renderpass",attach:`passes-${E.length}`,args:[y,v]})),i||E.push(C.createElement("shaderPass",{attach:`passes-${E.length}`,key:"gammapass",args:[u.GammaCorrectionShader]})),C.Children.forEach(e,(e=>{e&&E.push(C.cloneElement(e,{key:E.length,attach:`passes-${E.length}`}))})),C.createElement("effectComposer",T.default({ref:x,args:[g,b]},p),E)}));let Se=function(e){return e.Linear="linear",e.Radial="radial",e}({});function Te(e,t,r,n){const a=class extends R.ShaderMaterial{constructor(a={}){const o=Object.entries(e);super({uniforms:o.reduce(((e,[t,r])=>({...e,...R.UniformsUtils.clone({[t]:{value:r}})})),{}),vertexShader:t,fragmentShader:r}),this.key="",o.forEach((([e])=>Object.defineProperty(this,e,{get:()=>this.uniforms[e].value,set:t=>this.uniforms[e].value=t}))),Object.assign(this,a),n&&n(this)}};return a.key=R.MathUtils.generateUUID(),a}const Ce=e=>e===Object(e)&&!Array.isArray(e)&&"function"!=typeof e;function Pe(e,r){const o=a.useThree((e=>e.gl)),i=a.useLoader(n.TextureLoader,Ce(e)?Object.values(e):e);t.useLayoutEffect((()=>{null==r||r(i)}),[r]),t.useEffect((()=>{if("initTexture"in o){let e=[];Array.isArray(i)?e=i:i instanceof n.Texture?e=[i]:Ce(i)&&(e=Object.values(i)),e.forEach((e=>{e instanceof n.Texture&&o.initTexture(e)}))}}),[o,i]);const s=t.useMemo((()=>{if(Ce(e)){const t={};let r=0;for(const n in e)t[n]=i[r++];return t}return i}),[e,i]);return s}Pe.preload=e=>a.useLoader.preload(n.TextureLoader,e),Pe.clear=e=>a.useLoader.clear(n.TextureLoader,e);const Re=(()=>parseInt(n.REVISION.replace(/\D+/g,"")))(),De=Te({color:new R.Color("white"),scale:new R.Vector2(1,1),imageBounds:new R.Vector2(1,1),resolution:1024,map:null,zoom:1,radius:0,grayscale:0,opacity:1},"\n  varying vec2 vUv;\n  varying vec2 vPos;\n  void main() {\n    gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n    vUv = uv;\n    vPos = position.xy;\n  }\n",`\n  // mostly from https://gist.github.com/statico/df64c5d167362ecf7b34fca0b1459a44\n  varying vec2 vUv;\n  varying vec2 vPos;\n  uniform vec2 scale;\n  uniform vec2 imageBounds;\n  uniform float resolution;\n  uniform vec3 color;\n  uniform sampler2D map;\n  uniform float radius;\n  uniform float zoom;\n  uniform float grayscale;\n  uniform float opacity;\n  const vec3 luma = vec3(.299, 0.587, 0.114);\n  vec4 toGrayscale(vec4 color, float intensity) {\n    return vec4(mix(color.rgb, vec3(dot(color.rgb, luma)), intensity), color.a);\n  }\n  vec2 aspect(vec2 size) {\n    return size / min(size.x, size.y);\n  }\n  \n  const float PI = 3.14159265;\n    \n  // from https://iquilezles.org/articles/distfunctions\n  float udRoundBox( vec2 p, vec2 b, float r ) {\n    return length(max(abs(p)-b+r,0.0))-r;\n  }\n\n  void main() {\n    vec2 s = aspect(scale);\n    vec2 i = aspect(imageBounds);\n    float rs = s.x / s.y;\n    float ri = i.x / i.y;\n    vec2 new = rs < ri ? vec2(i.x * s.y / i.y, s.y) : vec2(s.x, i.y * s.x / i.x);\n    vec2 offset = (rs < ri ? vec2((new.x - s.x) / 2.0, 0.0) : vec2(0.0, (new.y - s.y) / 2.0)) / new;\n    vec2 uv = vUv * s / new + offset;\n    vec2 zUv = (uv - vec2(0.5, 0.5)) / zoom + vec2(0.5, 0.5);\n\n    vec2 res = vec2(scale * resolution);\n    vec2 halfRes = 0.5 * res;\n    float b = udRoundBox(vUv.xy * res - halfRes, halfRes, resolution * radius);    \n\t  vec3 a = mix(vec3(1.0,0.0,0.0), vec3(0.0,0.0,0.0), smoothstep(0.0, 1.0, b));\n    gl_FragColor = toGrayscale(texture2D(map, zUv) * vec4(color, opacity * a), grayscale);\n    \n    #include <tonemapping_fragment>\n    #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n  }\n`),Fe=C.forwardRef((({children:e,color:t,segments:r=1,scale:n=1,zoom:o=1,grayscale:i=0,opacity:s=1,radius:l=0,texture:c,toneMapped:u,transparent:d,side:m,...f},p)=>{a.extend({ImageMaterial:De});const h=C.useRef(null),x=a.useThree((e=>e.size)),y=Array.isArray(n)?[n[0],n[1]]:[n,n],v=[c.image.width,c.image.height],g=Math.max(x.width,x.height);return C.useImperativeHandle(p,(()=>h.current),[]),C.useLayoutEffect((()=>{h.current.geometry.parameters&&h.current.material.scale.set(y[0]*h.current.geometry.parameters.width,y[1]*h.current.geometry.parameters.height)}),[y[0],y[1]]),C.createElement("mesh",T.default({ref:h,scale:Array.isArray(n)?[...n,1]:n},f),C.createElement("planeGeometry",{args:[1,1,r,r]}),C.createElement("imageMaterial",{color:t,map:c,zoom:o,grayscale:i,opacity:s,scale:y,imageBounds:v,resolution:g,radius:l,toneMapped:u,transparent:d,side:m,key:De.key}),e)})),ke=C.forwardRef((({url:e,...t},r)=>{const n=Pe(e);return C.createElement(Fe,T.default({},t,{texture:n,ref:r}))})),_e=C.forwardRef((({url:e,...t},r)=>C.createElement(Fe,T.default({},t,{ref:r})))),Ae=C.forwardRef(((e,t)=>{if(e.url)return C.createElement(ke,T.default({},e,{ref:t}));if(e.texture)return C.createElement(_e,T.default({},e,{ref:t}));throw new Error("<Image /> requires a url or texture")})),Le=C.forwardRef((({threshold:e=15,geometry:t,...r},n)=>{const a=C.useRef(null);C.useImperativeHandle(n,(()=>a.current),[]);const o=C.useMemo((()=>[0,0,0,1,0,0]),[]),i=C.useRef(),s=C.useRef();return C.useLayoutEffect((()=>{const r=a.current.parent,n=null!=t?t:null==r?void 0:r.geometry;if(!n)return;if(i.current===n&&s.current===e)return;i.current=n,s.current=e;const o=new R.EdgesGeometry(n,e).attributes.position.array;a.current.geometry.setPositions(o),a.current.geometry.attributes.instanceStart.needsUpdate=!0,a.current.geometry.attributes.instanceEnd.needsUpdate=!0,a.current.computeLineDistances()})),C.createElement(me,T.default({segments:!0,points:o,ref:a,raycast:()=>null},r))})),Ie=Te({screenspace:!1,color:new R.Color("black"),opacity:1,thickness:.05,size:new R.Vector2},"#include <common>\n   #include <morphtarget_pars_vertex>\n   #include <skinning_pars_vertex>\n   #include <clipping_planes_pars_vertex>\n   uniform float thickness;\n   uniform bool screenspace;\n   uniform vec2 size;\n   void main() {\n     #if defined (USE_SKINNING)\n\t     #include <beginnormal_vertex>\n       #include <morphnormal_vertex>\n       #include <skinbase_vertex>\n       #include <skinnormal_vertex>\n       #include <defaultnormal_vertex>\n     #endif\n     #include <begin_vertex>\n\t   #include <morphtarget_vertex>\n\t   #include <skinning_vertex>\n     #include <project_vertex>\n     #include <clipping_planes_vertex>\n     vec4 tNormal = vec4(normal, 0.0);\n     vec4 tPosition = vec4(transformed, 1.0);\n     #ifdef USE_INSTANCING\n       tNormal = instanceMatrix * tNormal;\n       tPosition = instanceMatrix * tPosition;\n     #endif\n     if (screenspace) {\n       vec3 newPosition = tPosition.xyz + tNormal.xyz * thickness;\n       gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0); \n     } else {\n       vec4 clipPosition = projectionMatrix * modelViewMatrix * tPosition;\n       vec4 clipNormal = projectionMatrix * modelViewMatrix * tNormal;\n       vec2 offset = normalize(clipNormal.xy) * thickness / size * clipPosition.w * 2.0;\n       clipPosition.xy += offset;\n       gl_Position = clipPosition;\n     }\n   }",`uniform vec3 color;\n   uniform float opacity;\n   #include <clipping_planes_pars_fragment>\n   void main(){\n     #include <clipping_planes_fragment>\n     gl_FragColor = vec4(color, opacity);\n     #include <tonemapping_fragment>\n     #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n   }`);const Be={width:.2,length:1,decay:1,local:!1,stride:0,interval:1},Ve=(e,t=1)=>(e.set(e.subarray(t)),e.fill(-1/0,-t),e);function Ue(e,t){const{length:r,local:o,decay:i,interval:s,stride:l}={...Be,...t},c=C.useRef(),[u]=C.useState((()=>new n.Vector3));C.useLayoutEffect((()=>{e&&(c.current=Float32Array.from({length:10*r*3},((t,r)=>e.position.getComponent(r%3))))}),[r,e]);const d=C.useRef(new n.Vector3),m=C.useRef(0);return a.useFrame((()=>{if(e&&c.current){if(0===m.current){let t;o?t=e.position:(e.getWorldPosition(u),t=u);const r=1*i;for(let e=0;e<r;e++)t.distanceTo(d.current)<l||(Ve(c.current,3),c.current.set(t.toArray(),c.current.length-3));d.current.copy(t)}m.current++,m.current=m.current%s}})),c}const Oe=C.forwardRef(((e,t)=>{const{children:r}=e,{width:o,length:i,decay:s,local:l,stride:c,interval:u}={...Be,...e},{color:d="hotpink",attenuation:m,target:f}=e,h=a.useThree((e=>e.size)),x=a.useThree((e=>e.scene)),y=C.useRef(null),[v,g]=C.useState(null),w=Ue(v,{length:i,decay:s,local:l,stride:c,interval:u});C.useEffect((()=>{const e=(null==f?void 0:f.current)||y.current.children.find((e=>e instanceof n.Object3D));e&&g(e)}),[w,f]);const z=C.useMemo((()=>new p.MeshLineGeometry),[]),b=C.useMemo((()=>{var e;const t=new p.MeshLineMaterial({lineWidth:.1*o,color:d,sizeAttenuation:1,resolution:new n.Vector2(h.width,h.height)});let a;if(r)if(Array.isArray(r))a=r.find((e=>{const t=e;return"string"==typeof t.type&&"meshLineMaterial"===t.type}));else{const e=r;"string"==typeof e.type&&"meshLineMaterial"===e.type&&(a=e)}return"object"==typeof(null==(e=a)?void 0:e.props)&&t.setValues(a.props),t}),[o,d,h,r]);return C.useEffect((()=>{b.uniforms.resolution.value.set(h.width,h.height)}),[h]),a.useFrame((()=>{w.current&&z.setPoints(w.current,m)})),C.createElement("group",null,a.createPortal(C.createElement("mesh",{ref:t,geometry:z,material:b}),x),C.createElement("group",{ref:y},r))}));function Ne(e,t=16,r,a,o){const[i,s]=C.useState((()=>{const e=Array.from({length:t},(()=>[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])).flat();return new n.InstancedBufferAttribute(Float32Array.from(e),16)}));return C.useLayoutEffect((()=>{if(void 0===e.current)return;const l=new u.MeshSurfaceSampler(e.current);a&&l.setWeightAttribute(a),l.build();const c=new n.Vector3,d=new n.Vector3,m=new n.Color,f=new n.Object3D;e.current.updateMatrixWorld(!0);for(let n=0;n<t;n++)l.sample(c,d,m),"function"==typeof r?r({dummy:f,sampledMesh:e.current,position:c,normal:d,color:m},n):f.position.copy(c),f.updateMatrix(),null!=o&&o.current&&o.current.setMatrixAt(n,f.matrix),f.matrix.toArray(i.array,16*n);null!=o&&o.current&&(o.current.instanceMatrix.needsUpdate=!0),i.needsUpdate=!0,s(new n.InstancedBufferAttribute(i.array,i.itemSize).copy(i))}),[e,o,a,t,r]),i}const je=C.forwardRef((({isChild:e=!1,object:t,children:r,deep:n,castShadow:a,receiveShadow:o,inject:i,keys:s,...l},c)=>{const d={keys:s,deep:n,inject:i,castShadow:a,receiveShadow:o};if(t=C.useMemo((()=>{if(!1===e&&!Array.isArray(t)){let e=!1;if(t.traverse((t=>{t.isSkinnedMesh&&(e=!0)})),e)return u.SkeletonUtils.clone(t)}return t}),[t,e]),Array.isArray(t))return C.createElement("group",T.default({},l,{ref:c}),t.map((e=>C.createElement(je,T.default({key:e.uuid,object:e},d)))),r);const{children:m,...f}=function(e,{keys:t=["near","far","color","distance","decay","penumbra","angle","intensity","skeleton","visible","castShadow","receiveShadow","morphTargetDictionary","morphTargetInfluences","name","geometry","material","position","rotation","scale","up","userData","bindMode","bindMatrix","bindMatrixInverse","skeleton"],deep:r,inject:n,castShadow:a,receiveShadow:o}){let i={};for(const r of t)i[r]=e[r];return r&&(i.geometry&&"materialsOnly"!==r&&(i.geometry=i.geometry.clone()),i.material&&"geometriesOnly"!==r&&(i.material=i.material.clone())),n&&(i="function"==typeof n?{...i,children:n(e)}:C.isValidElement(n)?{...i,children:n}:{...i,...n}),e instanceof R.Mesh&&(a&&(i.castShadow=!0),o&&(i.receiveShadow=!0)),i}(t,d),p=t.type[0].toLowerCase()+t.type.slice(1);return C.createElement(p,T.default({},f,l,{ref:c}),t.children.map((e=>"Bone"===e.type?C.createElement("primitive",T.default({key:e.uuid,object:e},d)):C.createElement(je,T.default({key:e.uuid,object:e},d,{isChild:!0})))),r,m)})),We=C.createContext(null),Ge=C.forwardRef((({resolution:e=28,maxPolyCount:t=1e4,enableUvs:r=!1,enableColors:n=!1,children:o,...i},s)=>{const l=C.useRef(null);C.useImperativeHandle(s,(()=>l.current),[]);const c=C.useMemo((()=>new u.MarchingCubes(e,null,r,n,t)),[e,t,r,n]),d=C.useMemo((()=>({getParent:()=>l})),[]);return a.useFrame((()=>{c.update(),c.reset()}),-1),C.createElement(C.Fragment,null,C.createElement("primitive",T.default({object:c,ref:l},i),C.createElement(We.Provider,{value:d},o)))})),He=C.forwardRef((({strength:e=.5,subtract:t=12,color:r,...n},o)=>{const{getParent:i}=C.useContext(We),s=C.useMemo((()=>i()),[i]),l=C.useRef(null);C.useImperativeHandle(o,(()=>l.current),[]);const c=new R.Vector3;return a.useFrame((n=>{s.current&&l.current&&(l.current.getWorldPosition(c),s.current.addBall(.5+.5*c.x,.5+.5*c.y,.5+.5*c.z,e,t,r))})),C.createElement("group",T.default({ref:l},n))})),$e=C.forwardRef((({planeType:e="x",strength:t=.5,subtract:r=12,...n},o)=>{const{getParent:i}=C.useContext(We),s=C.useMemo((()=>i()),[i]),l=C.useRef(null);C.useImperativeHandle(o,(()=>l.current),[]);const c=C.useMemo((()=>"x"===e?"addPlaneX":"y"===e?"addPlaneY":"addPlaneZ"),[e]);return a.useFrame((()=>{s.current&&l.current&&s.current[c](t,r)})),C.createElement("group",T.default({ref:l},n))}));function qe(e=[0,0,0]){return function(e){return Array.isArray(e)}(e)?e:e instanceof R.Vector3||e instanceof R.Euler?[e.x,e.y,e.z]:[e,e,e]}const Xe=C.forwardRef((function({debug:e,depthTest:t=!1,polygonOffsetFactor:r=-10,map:n,mesh:o,children:i,position:s,rotation:l,scale:c,...d},m){const f=C.useRef(null);C.useImperativeHandle(m,(()=>f.current));const p=C.useRef(null),h=C.useRef({position:new R.Vector3,rotation:new R.Euler,scale:new R.Vector3(1,1,1)});return C.useLayoutEffect((()=>{const e=(null==o?void 0:o.current)||f.current.parent,t=f.current;if(!(e instanceof R.Mesh))throw new Error('Decal must have a Mesh as parent or specify its "mesh" prop');if(e){a.applyProps(h.current,{position:s,scale:c});const r=e.matrixWorld.clone();if(e.matrixWorld.identity(),l&&"number"!=typeof l)a.applyProps(h.current,{rotation:l});else{const t=new R.Object3D;t.position.copy(h.current.position);const r=e.geometry.attributes.position.array;void 0===e.geometry.attributes.normal&&e.geometry.computeVertexNormals();const n=e.geometry.attributes.normal.array;let o=1/0;new R.Vector3;let i=new R.Vector3;const s=t.position.x,c=t.position.y,u=t.position.z,d=r.length;let m=-1;for(let e=0;e<d;e+=3){const t=r[e]-s,n=r[e+1]-c,a=r[e+2]-u,i=t*t+n*n+a*a;i<o&&(o=i,m=e)}i.fromArray(n,m),t.lookAt(t.position.clone().add(i)),t.rotateZ(Math.PI),t.rotateY(Math.PI),"number"==typeof l&&t.rotateZ(l),a.applyProps(h.current,{rotation:t.rotation})}return t.geometry=new u.DecalGeometry(e,h.current.position,h.current.rotation,h.current.scale),e.matrixWorld=r,()=>{t.geometry.dispose()}}}),[o,...qe(s),...qe(c),...qe(l)]),C.useLayoutEffect((()=>{p.current&&(a.applyProps(p.current,h.current),p.current.traverse((e=>e.raycast=()=>null)))}),[e]),C.createElement("mesh",T.default({ref:f,"material-transparent":!0,"material-polygonOffset":!0,"material-polygonOffsetFactor":r,"material-depthTest":t,"material-map":n},d),i,e&&C.createElement("mesh",{ref:p},C.createElement("boxGeometry",null),C.createElement("meshNormalMaterial",{wireframe:!0}),C.createElement("axesHelper",null)))})),Ze=t.forwardRef((function({src:e,skipFill:r,skipStrokes:o,fillMaterial:i,strokeMaterial:s,fillMeshProps:l,strokeMeshProps:c,...d},m){const f=a.useLoader(u.SVGLoader,e.startsWith("<svg")?`data:image/svg+xml;utf8,${e}`:e),p=t.useMemo((()=>o?[]:f.paths.map((e=>{var t;return void 0===(null==(t=e.userData)?void 0:t.style.stroke)||"none"===e.userData.style.stroke?null:e.subPaths.map((t=>u.SVGLoader.pointsToStroke(t.getPoints(),e.userData.style)))}))),[f,o]);t.useEffect((()=>()=>p.forEach((e=>e&&e.map((e=>e.dispose()))))),[p]);let h=0;return C.createElement("object3D",T.default({ref:m},d),C.createElement("object3D",{scale:[1,-1,1]},f.paths.map(((e,a)=>{var d,m;return C.createElement(t.Fragment,{key:a},!r&&void 0!==(null==(d=e.userData)?void 0:d.style.fill)&&"none"!==e.userData.style.fill&&u.SVGLoader.createShapes(e).map(((t,r)=>C.createElement("mesh",T.default({key:r},l,{renderOrder:h++}),C.createElement("shapeGeometry",{args:[t]}),C.createElement("meshBasicMaterial",T.default({color:e.userData.style.fill,opacity:e.userData.style.fillOpacity,transparent:!0,side:n.DoubleSide,depthWrite:!1},i))))),!o&&void 0!==(null==(m=e.userData)?void 0:m.style.stroke)&&"none"!==e.userData.style.stroke&&e.subPaths.map(((t,r)=>C.createElement("mesh",T.default({key:r,geometry:p[a][r]},c,{renderOrder:h++}),C.createElement("meshBasicMaterial",T.default({color:e.userData.style.stroke,opacity:e.userData.style.strokeOpacity,transparent:!0,side:n.DoubleSide,depthWrite:!1},s))))))}))))}));let Ye=null,Qe="https://www.gstatic.com/draco/versioned/decoders/1.5.5/";function Ke(e=!0,t=!0,r){return n=>{r&&r(n),e&&(Ye||(Ye=new u.DRACOLoader),Ye.setDecoderPath("string"==typeof e?e:Qe),n.setDRACOLoader(Ye)),t&&n.setMeshoptDecoder("function"==typeof u.MeshoptDecoder?u.MeshoptDecoder():u.MeshoptDecoder)}}const Je=(e,t,r,n)=>a.useLoader(u.GLTFLoader,e,Ke(t,r,n));Je.preload=(e,t,r,n)=>a.useLoader.preload(u.GLTFLoader,e,Ke(t,r,n)),Je.clear=e=>a.useLoader.clear(u.GLTFLoader,e),Je.setDecoderPath=e=>{Qe=e};const et=C.forwardRef((({src:e,useDraco:t,useMeshOpt:r,extendLoader:n,...a},o)=>{const{scene:i}=Je(e,t,r,n);return C.createElement(je,T.default({ref:o},a,{object:i}))}));const tt=Te({alphaTest:0,viewport:new R.Vector2(1980,1080),focal:1e3,centerAndScaleTexture:null,covAndColorTexture:null},"\n    precision highp sampler2D;\n    precision highp usampler2D;\n    out vec4 vColor;\n    out vec3 vPosition;\n    uniform vec2 resolution;\n    uniform vec2 viewport;\n    uniform float focal;\n    attribute uint splatIndex;\n    uniform sampler2D centerAndScaleTexture;\n    uniform usampler2D covAndColorTexture;    \n\n    vec2 unpackInt16(in uint value) {\n      int v = int(value);\n      int v0 = v >> 16;\n      int v1 = (v & 0xFFFF);\n      if((v & 0x8000) != 0)\n        v1 |= 0xFFFF0000;\n      return vec2(float(v1), float(v0));\n    }\n\n    void main () {\n      ivec2 texSize = textureSize(centerAndScaleTexture, 0);\n      ivec2 texPos = ivec2(splatIndex%uint(texSize.x), splatIndex/uint(texSize.x));\n      vec4 centerAndScaleData = texelFetch(centerAndScaleTexture, texPos, 0);\n      vec4 center = vec4(centerAndScaleData.xyz, 1);\n      vec4 camspace = modelViewMatrix * center;\n      vec4 pos2d = projectionMatrix * camspace;\n\n      float bounds = 1.2 * pos2d.w;\n      if (pos2d.z < -pos2d.w || pos2d.x < -bounds || pos2d.x > bounds\n        || pos2d.y < -bounds || pos2d.y > bounds) {\n        gl_Position = vec4(0.0, 0.0, 2.0, 1.0);\n        return;\n      }\n\n      uvec4 covAndColorData = texelFetch(covAndColorTexture, texPos, 0);\n      vec2 cov3D_M11_M12 = unpackInt16(covAndColorData.x) * centerAndScaleData.w;\n      vec2 cov3D_M13_M22 = unpackInt16(covAndColorData.y) * centerAndScaleData.w;\n      vec2 cov3D_M23_M33 = unpackInt16(covAndColorData.z) * centerAndScaleData.w;\n      mat3 Vrk = mat3(\n        cov3D_M11_M12.x, cov3D_M11_M12.y, cov3D_M13_M22.x,\n        cov3D_M11_M12.y, cov3D_M13_M22.y, cov3D_M23_M33.x,\n        cov3D_M13_M22.x, cov3D_M23_M33.x, cov3D_M23_M33.y\n      );\n\n      mat3 J = mat3(\n        focal / camspace.z, 0., -(focal * camspace.x) / (camspace.z * camspace.z),\n        0., focal / camspace.z, -(focal * camspace.y) / (camspace.z * camspace.z),\n        0., 0., 0.\n      );\n\n      mat3 W = transpose(mat3(modelViewMatrix));\n      mat3 T = W * J;\n      mat3 cov = transpose(T) * Vrk * T;\n      vec2 vCenter = vec2(pos2d) / pos2d.w;\n      float diagonal1 = cov[0][0] + 0.3;\n      float offDiagonal = cov[0][1];\n      float diagonal2 = cov[1][1] + 0.3;\n      float mid = 0.5 * (diagonal1 + diagonal2);\n      float radius = length(vec2((diagonal1 - diagonal2) / 2.0, offDiagonal));\n      float lambda1 = mid + radius;\n      float lambda2 = max(mid - radius, 0.1);\n      vec2 diagonalVector = normalize(vec2(offDiagonal, lambda1 - diagonal1));\n      vec2 v1 = min(sqrt(2.0 * lambda1), 1024.0) * diagonalVector;\n      vec2 v2 = min(sqrt(2.0 * lambda2), 1024.0) * vec2(diagonalVector.y, -diagonalVector.x);\n      uint colorUint = covAndColorData.w;\n      vColor = vec4(\n        float(colorUint & uint(0xFF)) / 255.0,\n        float((colorUint >> uint(8)) & uint(0xFF)) / 255.0,\n        float((colorUint >> uint(16)) & uint(0xFF)) / 255.0,\n        float(colorUint >> uint(24)) / 255.0\n      );\n      vPosition = position;\n\n      gl_Position = vec4(\n        vCenter \n          + position.x * v2 / viewport * 2.0 \n          + position.y * v1 / viewport * 2.0, pos2d.z / pos2d.w, 1.0);\n    }\n    ",`\n    #include <alphatest_pars_fragment>\n    #include <alphahash_pars_fragment>\n    in vec4 vColor;\n    in vec3 vPosition;\n    void main () {\n      float A = -dot(vPosition.xy, vPosition.xy);\n      if (A < -4.0) discard;\n      float B = exp(A) * vColor.a;\n      vec4 diffuseColor = vec4(vColor.rgb, B);\n      #include <alphatest_fragment>\n      #include <alphahash_fragment>\n      gl_FragColor = diffuseColor;\n      #include <tonemapping_fragment>\n      #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n    }\n  `);function rt(e){let t=null,r=0;e.onmessage=n=>{if("push"==n.data.method){0===r&&(t=new Float32Array(n.data.length));const e=new Float32Array(n.data.matrices);t.set(e,r),r+=e.length}else if("sort"==n.data.method&&null!==t){const r=function(e,r=!1){const n=t.length/16;let a=-1/0,o=1/0;const i=new Float32Array(n),s=new Int32Array(i.buffer),l=new Int32Array(n);let c=0;for(let s=0;s<n;s++){const n=e[0]*t[16*s+12]+e[1]*t[16*s+13]+e[2]*t[16*s+14]+e[3];(r||n<0&&t[16*s+15]>-1e-4*n)&&(i[c]=n,l[c]=s,c++,n>a&&(a=n),n<o&&(o=n))}const u=65535/(a-o),d=new Uint32Array(65536);for(let e=0;e<c;e++)s[e]=(i[e]-o)*u|0,d[s[e]]++;const m=new Uint32Array(65536);for(let e=1;e<65536;e++)m[e]=m[e-1]+d[e-1];const f=new Uint32Array(c);for(let e=0;e<c;e++)f[m[s[e]]++]=l[e];return f}(new Float32Array(n.data.view),n.data.hashed);e.postMessage({indices:r,key:n.data.key},[r.buffer])}}}class nt extends R.Loader{constructor(...e){super(...e),this.gl=null,this.chunkSize=25e3}load(e,t,r,n){const a={gl:this.gl,url:this.manager.resolveURL(e),worker:new Worker(URL.createObjectURL(new Blob(["(",rt.toString(),")(self)"],{type:"application/javascript"}))),manager:this.manager,update:(e,t,r)=>function(e,t,r,n){if(e.updateMatrixWorld(),t.gl.getCurrentViewport(r.viewport),r.material.viewport.x=r.viewport.z,r.material.viewport.y=r.viewport.w,r.material.focal=r.viewport.w/2*Math.abs(e.projectionMatrix.elements[5]),r.ready){if(n&&r.sorted)return;r.ready=!1;const e=new Float32Array([r.modelViewMatrix.elements[2],-r.modelViewMatrix.elements[6],r.modelViewMatrix.elements[10],r.modelViewMatrix.elements[14]]);t.worker.postMessage({method:"sort",src:t.url,key:r.uuid,view:e.buffer,hashed:n},[e.buffer]),n&&t.loaded&&(r.sorted=!0)}}(t,a,e,r),connect:e=>function(e,t){e.loading||async function(e){e.loading=!0;let t=0,r=0;const n=[];let a=0;const o=0!==e.totalDownloadBytes;for(;;)try{const{value:i,done:s}=await e.stream.read();if(s)break;if(t+=i.length,null!=e.totalDownloadBytes){const r=t/e.totalDownloadBytes*100;if(e.onProgress&&r-a>1){const n=new ProgressEvent("progress",{lengthComputable:o,loaded:t,total:e.totalDownloadBytes});e.onProgress(n),a=r}}n.push(i);const l=t-r;if(null!=e.totalDownloadBytes&&l>e.rowLength*e.chunkSize){let t=Math.floor(l/e.rowLength);const a=new Uint8Array(l);let i=0;for(const e of n)a.set(e,i),i+=e.length;if(n.length=0,l>t*e.rowLength){const r=new Uint8Array(l-t*e.rowLength);r.set(a.subarray(l-r.length,l),0),n.push(r)}const s=new Uint8Array(t*e.rowLength);s.set(a.subarray(0,s.byteLength),0);const c=at(e,s.buffer,t);if(e.worker.postMessage({method:"push",src:e.url,length:16*e.numVertices,matrices:c.buffer},[c.buffer]),r+=t*e.rowLength,e.onProgress){const t=new ProgressEvent("progress",{lengthComputable:o,loaded:e.totalDownloadBytes,total:e.totalDownloadBytes});e.onProgress(t)}}}catch(e){console.error(e);break}if(t-r>0){let t=new Uint8Array(n.reduce(((e,t)=>e+t.length),0)),r=0;for(const e of n)t.set(e,r),r+=e.length;let a=Math.floor(t.byteLength/e.rowLength);const o=at(e,t.buffer,a);e.worker.postMessage({method:"push",src:e.url,length:16*a,matrices:o.buffer},[o.buffer])}e.loaded=!0,e.manager.itemEnd(e.url)}(e);t.ready=!1,t.pm=new R.Matrix4,t.vm1=new R.Matrix4,t.vm2=new R.Matrix4,t.viewport=new R.Vector4;let r=new Uint32Array(e.bufferTextureWidth*e.bufferTextureHeight);const n=new R.InstancedBufferAttribute(r,1,!1);n.setUsage(R.DynamicDrawUsage);const a=t.geometry=new R.InstancedBufferGeometry,o=new Float32Array(18),i=new R.BufferAttribute(o,3);function s(e){if(t&&e.data.key===t.uuid){let r=new Uint32Array(e.data.indices);a.attributes.splatIndex.set(r),a.attributes.splatIndex.needsUpdate=!0,a.instanceCount=r.length,t.ready=!0}}async function l(){for(;;){const t=e.gl.properties.get(e.centerAndScaleTexture),r=e.gl.properties.get(e.covAndColorTexture);if(null!=t&&t.__webglTexture&&null!=r&&r.__webglTexture&&e.loadedVertexCount>0)break;await new Promise((e=>setTimeout(e,10)))}t.ready=!0}return a.setAttribute("position",i),i.setXYZ(2,-2,2,0),i.setXYZ(1,2,2,0),i.setXYZ(0,-2,-2,0),i.setXYZ(5,-2,-2,0),i.setXYZ(4,2,2,0),i.setXYZ(3,2,-2,0),i.needsUpdate=!0,a.setAttribute("splatIndex",n),a.instanceCount=1,e.worker.addEventListener("message",s),l(),()=>e.worker.removeEventListener("message",s)}(a,e),loading:!1,loaded:!1,loadedVertexCount:0,chunkSize:this.chunkSize,totalDownloadBytes:0,numVertices:0,rowLength:32,maxVertexes:0,bufferTextureWidth:0,bufferTextureHeight:0,stream:null,centerAndScaleData:null,covAndColorData:null,covAndColorTexture:null,centerAndScaleTexture:null,onProgress:r};(async function(e){e.manager.itemStart(e.url);const t=await fetch(e.url);if(null===t.body)throw"Failed to fetch file";let r=t.headers.get("Content-Length");const n=r?parseInt(r):void 0;if(null==n)throw"Failed to get content length";e.stream=t.body.getReader(),e.totalDownloadBytes=n,e.numVertices=Math.floor(e.totalDownloadBytes/e.rowLength);const a=e.gl.getContext();let o=a.getParameter(a.MAX_TEXTURE_SIZE);e.maxVertexes=o*o,e.numVertices>e.maxVertexes&&(e.numVertices=e.maxVertexes);return e.bufferTextureWidth=o,e.bufferTextureHeight=Math.floor((e.numVertices-1)/o)+1,e.centerAndScaleData=new Float32Array(e.bufferTextureWidth*e.bufferTextureHeight*4),e.covAndColorData=new Uint32Array(e.bufferTextureWidth*e.bufferTextureHeight*4),e.centerAndScaleTexture=new R.DataTexture(e.centerAndScaleData,e.bufferTextureWidth,e.bufferTextureHeight,R.RGBAFormat,R.FloatType),e.centerAndScaleTexture.needsUpdate=!0,e.covAndColorTexture=new R.DataTexture(e.covAndColorData,e.bufferTextureWidth,e.bufferTextureHeight,R.RGBAIntegerFormat,R.UnsignedIntType),e.covAndColorTexture.internalFormat="RGBA32UI",e.covAndColorTexture.needsUpdate=!0,e})(a).then(t).catch((e=>{null==n||n(e),a.manager.itemError(a.url)}))}}function at(e,t,r){const n=e.gl.getContext();if(e.loadedVertexCount+r>e.maxVertexes&&(r=e.maxVertexes-e.loadedVertexCount),r<=0)throw"Failed to parse file";const a=new Uint8Array(t),o=new Float32Array(t),i=new Float32Array(16*r),s=new Uint8Array(e.covAndColorData.buffer),l=new Int16Array(e.covAndColorData.buffer);for(let t=0;t<r;t++){const r=new R.Quaternion(-(a[32*t+28+1]-128)/128,(a[32*t+28+2]-128)/128,(a[32*t+28+3]-128)/128,-(a[32*t+28+0]-128)/128);r.invert();const n=new R.Vector3(o[8*t+0],o[8*t+1],-o[8*t+2]),c=new R.Vector3(o[8*t+3+0],o[8*t+3+1],o[8*t+3+2]),u=new R.Matrix4;u.makeRotationFromQuaternion(r),u.transpose(),u.scale(c);const d=u.clone();u.transpose(),u.premultiply(d),u.setPosition(n);const m=[0,1,2,5,6,10];let f=0;for(let e=0;e<m.length;e++)Math.abs(u.elements[m[e]])>f&&(f=Math.abs(u.elements[m[e]]));let p=4*e.loadedVertexCount+4*t;e.centerAndScaleData[p+0]=n.x,e.centerAndScaleData[p+1]=-n.y,e.centerAndScaleData[p+2]=n.z,e.centerAndScaleData[p+3]=f/32767,p=8*e.loadedVertexCount+4*t*2;for(let e=0;e<m.length;e++)l[p+e]=32767*u.elements[m[e]]/f;p=16*e.loadedVertexCount+4*(4*t+3);const h=new R.Color(a[32*t+24+0]/255,a[32*t+24+1]/255,a[32*t+24+2]/255);h.convertSRGBToLinear(),s[p+0]=255*h.r,s[p+1]=255*h.g,s[p+2]=255*h.b,s[p+3]=a[32*t+24+3],u.elements[15]=Math.max(c.x,c.y,c.z)*a[32*t+24+3]/255;for(let e=0;e<16;e++)i[16*t+e]=u.elements[e]}for(;r>0;){let t=0,a=0;const o=e.loadedVertexCount%e.bufferTextureWidth,i=Math.floor(e.loadedVertexCount/e.bufferTextureWidth);e.loadedVertexCount%e.bufferTextureWidth!=0?(t=Math.min(e.bufferTextureWidth,o+r)-o,a=1):Math.floor(r/e.bufferTextureWidth)>0?(t=e.bufferTextureWidth,a=Math.floor(r/e.bufferTextureWidth)):(t=r%e.bufferTextureWidth,a=1);const s=e.gl.properties.get(e.centerAndScaleTexture);n.bindTexture(n.TEXTURE_2D,s.__webglTexture),n.texSubImage2D(n.TEXTURE_2D,0,o,i,t,a,n.RGBA,n.FLOAT,e.centerAndScaleData,4*e.loadedVertexCount);const l=e.gl.properties.get(e.covAndColorTexture);n.bindTexture(n.TEXTURE_2D,l.__webglTexture),n.texSubImage2D(n.TEXTURE_2D,0,o,i,t,a,n.RGBA_INTEGER,n.UNSIGNED_INT,e.covAndColorData,4*e.loadedVertexCount),e.gl.resetState(),e.loadedVertexCount+=t*a,r-=t*a}return i}function ot(e,t,r){const n=a.useThree((e=>e.size)),o=a.useThree((e=>e.viewport)),i="number"==typeof e?e:n.width*o.dpr,s="number"==typeof t?t:n.height*o.dpr,l=("number"==typeof e?r:e)||{},{samples:c=0,depth:u,...d}=l,m=C.useMemo((()=>{const e=new R.WebGLRenderTarget(i,s,{minFilter:R.LinearFilter,magFilter:R.LinearFilter,type:R.HalfFloatType,...d});return u&&(e.depthTexture=new R.DepthTexture(i,s,R.FloatType)),e.samples=c,e}),[]);return C.useLayoutEffect((()=>{m.setSize(i,s),c&&(m.samples=c)}),[c,m,i,s]),C.useEffect((()=>()=>m.dispose()),[]),m}const it=C.forwardRef((({envMap:e,resolution:t=256,frames:r=1/0,children:n,makeDefault:o,...i},s)=>{const l=a.useThree((({set:e})=>e)),c=a.useThree((({camera:e})=>e)),u=a.useThree((({size:e})=>e)),d=C.useRef(null);C.useImperativeHandle(s,(()=>d.current),[]);const m=C.useRef(null),f=ot(t);C.useLayoutEffect((()=>{i.manual||d.current.updateProjectionMatrix()}),[u,i]),C.useLayoutEffect((()=>{d.current.updateProjectionMatrix()})),C.useLayoutEffect((()=>{if(o){const e=c;return l((()=>({camera:d.current}))),()=>l((()=>({camera:e})))}}),[d,o,l]);let p=0,h=null;const x="function"==typeof n;return a.useFrame((t=>{x&&(r===1/0||p<r)&&(m.current.visible=!1,t.gl.setRenderTarget(f),h=t.scene.background,e&&(t.scene.background=e),t.gl.render(t.scene,d.current),t.scene.background=h,t.gl.setRenderTarget(null),m.current.visible=!0,p++)})),C.createElement(C.Fragment,null,C.createElement("orthographicCamera",T.default({left:u.width/-2,right:u.width/2,top:u.height/2,bottom:u.height/-2,ref:d},i),!x&&n),C.createElement("group",{ref:m},x&&n(f.texture)))})),st=C.forwardRef((({envMap:e,resolution:t=256,frames:r=1/0,makeDefault:n,children:o,...i},s)=>{const l=a.useThree((({set:e})=>e)),c=a.useThree((({camera:e})=>e)),u=a.useThree((({size:e})=>e)),d=C.useRef(null);C.useImperativeHandle(s,(()=>d.current),[]);const m=C.useRef(null),f=ot(t);C.useLayoutEffect((()=>{i.manual||(d.current.aspect=u.width/u.height)}),[u,i]),C.useLayoutEffect((()=>{d.current.updateProjectionMatrix()}));let p=0,h=null;const x="function"==typeof o;return a.useFrame((t=>{x&&(r===1/0||p<r)&&(m.current.visible=!1,t.gl.setRenderTarget(f),h=t.scene.background,e&&(t.scene.background=e),t.gl.render(t.scene,d.current),t.scene.background=h,t.gl.setRenderTarget(null),m.current.visible=!0,p++)})),C.useLayoutEffect((()=>{if(n){const e=c;return l((()=>({camera:d.current}))),()=>l((()=>({camera:e})))}}),[d,n,l]),C.createElement(C.Fragment,null,C.createElement("perspectiveCamera",T.default({ref:d},i),!x&&o),C.createElement("group",{ref:m},x&&o(f.texture)))}));function lt({resolution:e=256,near:r=.1,far:o=1e3,envMap:i,fog:s}={}){const l=a.useThree((({gl:e})=>e)),c=a.useThree((({scene:e})=>e)),u=t.useMemo((()=>{const t=new n.WebGLCubeRenderTarget(e);return t.texture.type=n.HalfFloatType,t}),[e]);t.useEffect((()=>()=>{u.dispose()}),[u]);const d=t.useMemo((()=>new R.CubeCamera(r,o,u)),[r,o,u]);let m,f;const p=C.useCallback((()=>{m=c.fog,f=c.background,c.background=i||f,c.fog=s||m,d.update(l,c),c.fog=m,c.background=f}),[l,c,d]);return{fbo:u,camera:d,update:p}}const ct=C.forwardRef(((e,t)=>{const{camera:r,onChange:n,makeDefault:o,...i}=e,s=a.useThree((e=>e.camera)),l=a.useThree((e=>e.invalidate)),c=a.useThree((e=>e.get)),d=a.useThree((e=>e.set)),m=r||s,f=C.useMemo((()=>new u.DeviceOrientationControls(m)),[m]);return C.useEffect((()=>{const e=e=>{l(),n&&n(e)};return null==f||null==f.addEventListener||f.addEventListener("change",e),()=>null==f||null==f.removeEventListener?void 0:f.removeEventListener("change",e)}),[n,f,l]),a.useFrame((()=>null==f?void 0:f.update()),-1),C.useEffect((()=>{const e=f;return null==e||e.connect(),()=>null==e?void 0:e.dispose()}),[f]),C.useEffect((()=>{if(o){const e=c().controls;return d({controls:f}),()=>d({controls:e})}}),[o,f]),f?C.createElement("primitive",T.default({ref:t,object:f},i)):null})),ut=C.forwardRef((({domElement:e,...t},r)=>{const{onChange:n,makeDefault:o,...i}=t,s=a.useThree((e=>e.invalidate)),l=a.useThree((e=>e.camera)),c=a.useThree((e=>e.gl)),d=a.useThree((e=>e.events)),m=a.useThree((e=>e.get)),f=a.useThree((e=>e.set)),p=e||d.connected||c.domElement,h=C.useMemo((()=>new u.FlyControls(l)),[l]);return C.useEffect((()=>(h.connect(p),()=>{h.dispose()})),[p,h,s]),C.useEffect((()=>{const e=e=>{s(),n&&n(e)};return null==h.addEventListener||h.addEventListener("change",e),()=>null==h.removeEventListener?void 0:h.removeEventListener("change",e)}),[n,s]),C.useEffect((()=>{if(o){const e=m().controls;return f({controls:h}),()=>f({controls:e})}}),[o,h]),a.useFrame(((e,t)=>h.update(t))),C.createElement("primitive",T.default({ref:r,object:h,args:[l,p]},i))})),dt=C.forwardRef(((e={enableDamping:!0},t)=>{const{domElement:r,camera:n,makeDefault:o,onChange:i,onStart:s,onEnd:l,...c}=e,d=a.useThree((e=>e.invalidate)),m=a.useThree((e=>e.camera)),f=a.useThree((e=>e.gl)),p=a.useThree((e=>e.events)),h=a.useThree((e=>e.set)),x=a.useThree((e=>e.get)),y=r||p.connected||f.domElement,v=n||m,g=C.useMemo((()=>new u.MapControls(v)),[v]);return C.useEffect((()=>{g.connect(y);const e=e=>{d(),i&&i(e)};return g.addEventListener("change",e),s&&g.addEventListener("start",s),l&&g.addEventListener("end",l),()=>{g.dispose(),g.removeEventListener("change",e),s&&g.removeEventListener("start",s),l&&g.removeEventListener("end",l)}}),[i,s,l,g,d,y]),C.useEffect((()=>{if(o){const e=x().controls;return h({controls:g}),()=>h({controls:e})}}),[o,g]),a.useFrame((()=>g.update()),-1),C.createElement("primitive",T.default({ref:t,object:g,enableDamping:!0},c))})),mt=C.forwardRef((({makeDefault:e,camera:t,regress:r,domElement:n,enableDamping:o=!0,keyEvents:i=!1,onChange:s,onStart:l,onEnd:c,...d},m)=>{const f=a.useThree((e=>e.invalidate)),p=a.useThree((e=>e.camera)),h=a.useThree((e=>e.gl)),x=a.useThree((e=>e.events)),y=a.useThree((e=>e.setEvents)),v=a.useThree((e=>e.set)),g=a.useThree((e=>e.get)),w=a.useThree((e=>e.performance)),z=t||p,b=n||x.connected||h.domElement,E=C.useMemo((()=>new u.OrbitControls(z)),[z]);return a.useFrame((()=>{E.enabled&&E.update()}),-1),C.useEffect((()=>(i&&E.connect(!0===i?b:i),E.connect(b),()=>{E.dispose()})),[i,b,r,E,f]),C.useEffect((()=>{const e=e=>{f(),r&&w.regress(),s&&s(e)},t=e=>{l&&l(e)},n=e=>{c&&c(e)};return E.addEventListener("change",e),E.addEventListener("start",t),E.addEventListener("end",n),()=>{E.removeEventListener("start",t),E.removeEventListener("end",n),E.removeEventListener("change",e)}}),[s,l,c,E,f,y]),C.useEffect((()=>{if(e){const e=g().controls;return v({controls:E}),()=>v({controls:e})}}),[e,E]),C.createElement("primitive",T.default({ref:m,object:E,enableDamping:o},d))})),ft=C.forwardRef((({makeDefault:e,camera:t,domElement:r,regress:n,onChange:o,onStart:i,onEnd:s,...l},c)=>{const{invalidate:d,camera:m,gl:f,events:p,set:h,get:x,performance:y,viewport:v}=a.useThree(),g=t||m,w=r||p.connected||f.domElement,z=C.useMemo((()=>new u.TrackballControls(g)),[g]);return a.useFrame((()=>{z.enabled&&z.update()}),-1),C.useEffect((()=>(z.connect(w),()=>{z.dispose()})),[w,n,z,d]),C.useEffect((()=>{const e=e=>{d(),n&&y.regress(),o&&o(e)};return z.addEventListener("change",e),i&&z.addEventListener("start",i),s&&z.addEventListener("end",s),()=>{i&&z.removeEventListener("start",i),s&&z.removeEventListener("end",s),z.removeEventListener("change",e)}}),[o,i,s,z,d]),C.useEffect((()=>{z.handleResize()}),[v]),C.useEffect((()=>{if(e){const e=x().controls;return h({controls:z}),()=>h({controls:e})}}),[e,z]),C.createElement("primitive",T.default({ref:c,object:z},l))})),pt=t.forwardRef((({camera:e,makeDefault:r,regress:n,domElement:o,onChange:i,onStart:s,onEnd:l,...c},d)=>{const m=a.useThree((e=>e.invalidate)),f=a.useThree((e=>e.camera)),p=a.useThree((e=>e.gl)),h=a.useThree((e=>e.events)),x=a.useThree((e=>e.set)),y=a.useThree((e=>e.get)),v=a.useThree((e=>e.performance)),g=e||f,w=o||h.connected||p.domElement,z=t.useMemo((()=>new u.ArcballControls(g)),[g]);return a.useFrame((()=>{z.enabled&&z.update()}),-1),t.useEffect((()=>(z.connect(w),()=>{z.dispose()})),[w,n,z,m]),t.useEffect((()=>{const e=e=>{m(),n&&v.regress(),i&&i(e)};return z.addEventListener("change",e),s&&z.addEventListener("start",s),l&&z.addEventListener("end",l),()=>{z.removeEventListener("change",e),s&&z.removeEventListener("start",s),l&&z.removeEventListener("end",l)}}),[i,s,l]),t.useEffect((()=>{if(r){const e=y().controls;return x({controls:z}),()=>x({controls:e})}}),[r,z]),C.createElement("primitive",T.default({ref:d,object:z},c))})),ht=C.forwardRef((({children:e,domElement:t,onChange:r,onMouseDown:n,onMouseUp:o,onObjectChange:i,object:s,makeDefault:l,camera:c,enabled:d,axis:m,mode:f,translationSnap:p,rotationSnap:h,scaleSnap:x,space:y,size:v,showX:g,showY:w,showZ:z,...b},E)=>{const M=a.useThree((e=>e.controls)),S=a.useThree((e=>e.gl)),P=a.useThree((e=>e.events)),D=a.useThree((e=>e.camera)),F=a.useThree((e=>e.invalidate)),k=a.useThree((e=>e.get)),_=a.useThree((e=>e.set)),A=c||D,L=t||P.connected||S.domElement,I=C.useMemo((()=>new u.TransformControls(A,L)),[A,L]),B=C.useRef(null);C.useLayoutEffect((()=>(s?I.attach(s instanceof R.Object3D?s:s.current):B.current instanceof R.Object3D&&I.attach(B.current),()=>{I.detach()})),[s,e,I]),C.useEffect((()=>{if(M){const e=e=>M.enabled=!e.value;return I.addEventListener("dragging-changed",e),()=>I.removeEventListener("dragging-changed",e)}}),[I,M]);const V=C.useRef(),U=C.useRef(),O=C.useRef(),N=C.useRef();return C.useLayoutEffect((()=>{V.current=r}),[r]),C.useLayoutEffect((()=>{U.current=n}),[n]),C.useLayoutEffect((()=>{O.current=o}),[o]),C.useLayoutEffect((()=>{N.current=i}),[i]),C.useEffect((()=>{const e=e=>{F(),null==V.current||V.current(e)},t=e=>null==U.current?void 0:U.current(e),r=e=>null==O.current?void 0:O.current(e),n=e=>null==N.current?void 0:N.current(e);return I.addEventListener("change",e),I.addEventListener("mouseDown",t),I.addEventListener("mouseUp",r),I.addEventListener("objectChange",n),()=>{I.removeEventListener("change",e),I.removeEventListener("mouseDown",t),I.removeEventListener("mouseUp",r),I.removeEventListener("objectChange",n)}}),[F,I]),C.useEffect((()=>{if(l){const e=k().controls;return _({controls:I}),()=>_({controls:e})}}),[l,I]),C.createElement(C.Fragment,null,C.createElement("primitive",{ref:E,object:I,enabled:d,axis:m,mode:f,translationSnap:p,rotationSnap:h,scaleSnap:x,space:y,size:v,showX:g,showY:w,showZ:z}),C.createElement("group",T.default({ref:B},b),e))})),xt=C.forwardRef((({domElement:e,selector:t,onChange:r,onLock:n,onUnlock:o,enabled:i=!0,makeDefault:s,...l},c)=>{const{camera:d,...m}=l,f=a.useThree((e=>e.setEvents)),p=a.useThree((e=>e.gl)),h=a.useThree((e=>e.camera)),x=a.useThree((e=>e.invalidate)),y=a.useThree((e=>e.events)),v=a.useThree((e=>e.get)),g=a.useThree((e=>e.set)),w=d||h,z=e||y.connected||p.domElement,b=C.useMemo((()=>new u.PointerLockControls(w)),[w]);return C.useEffect((()=>{if(i){b.connect(z);const e=v().events.compute;return f({compute(e,t){const r=t.size.width/2,n=t.size.height/2;t.pointer.set(r/t.size.width*2-1,-n/t.size.height*2+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),()=>{b.disconnect(),f({compute:e})}}}),[i,b]),C.useEffect((()=>{const e=e=>{x(),r&&r(e)};b.addEventListener("change",e),n&&b.addEventListener("lock",n),o&&b.addEventListener("unlock",o);const a=()=>b.lock(),i=t?Array.from(document.querySelectorAll(t)):[document];return i.forEach((e=>e&&e.addEventListener("click",a))),()=>{b.removeEventListener("change",e),n&&b.removeEventListener("lock",n),o&&b.removeEventListener("unlock",o),i.forEach((e=>e?e.removeEventListener("click",a):void 0))}}),[r,n,o,t,b,x]),C.useEffect((()=>{if(s){const e=v().controls;return g({controls:b}),()=>g({controls:e})}}),[s,b]),C.createElement("primitive",T.default({ref:c,object:b},m))})),yt=C.forwardRef((({domElement:e,makeDefault:t,...r},n)=>{const o=a.useThree((e=>e.camera)),i=a.useThree((e=>e.gl)),s=a.useThree((e=>e.events)),l=a.useThree((e=>e.get)),c=a.useThree((e=>e.set)),d=e||s.connected||i.domElement,[m]=C.useState((()=>new u.FirstPersonControls(o,d)));return C.useEffect((()=>{if(t){const e=l().controls;return c({controls:m}),()=>c({controls:e})}}),[t,m]),a.useFrame(((e,t)=>{m.update(t)}),-1),m?C.createElement("primitive",T.default({ref:n,object:m},r)):null})),vt=t.forwardRef(((e,r)=>{t.useMemo((()=>{const e={Box3:n.Box3,MathUtils:{clamp:n.MathUtils.clamp},Matrix4:n.Matrix4,Quaternion:n.Quaternion,Raycaster:n.Raycaster,Sphere:n.Sphere,Spherical:n.Spherical,Vector2:n.Vector2,Vector3:n.Vector3,Vector4:n.Vector4};D.default.install({THREE:e}),a.extend({CameraControlsImpl:D.default})}),[]);const{camera:o,domElement:i,makeDefault:s,onStart:l,onEnd:c,onChange:u,regress:d,...m}=e,f=a.useThree((e=>e.camera)),p=a.useThree((e=>e.gl)),h=a.useThree((e=>e.invalidate)),x=a.useThree((e=>e.events)),y=a.useThree((e=>e.setEvents)),v=a.useThree((e=>e.set)),g=a.useThree((e=>e.get)),w=a.useThree((e=>e.performance)),z=o||f,b=i||x.connected||p.domElement,E=t.useMemo((()=>new D.default(z)),[z]);return a.useFrame(((e,t)=>{E.enabled&&E.update(t)}),-1),t.useEffect((()=>(E.connect(b),()=>{E.disconnect()})),[b,E]),t.useEffect((()=>{const e=e=>{h(),d&&w.regress(),u&&u(e)},t=e=>{l&&l(e)},r=e=>{c&&c(e)};return E.addEventListener("update",e),E.addEventListener("controlstart",t),E.addEventListener("controlend",r),E.addEventListener("control",e),E.addEventListener("transitionstart",e),E.addEventListener("wake",e),()=>{E.removeEventListener("update",e),E.removeEventListener("controlstart",t),E.removeEventListener("controlend",r),E.removeEventListener("control",e),E.removeEventListener("transitionstart",e),E.removeEventListener("wake",e)}}),[E,l,c,h,y,d,u]),t.useEffect((()=>{if(s){const e=g().controls;return v({controls:E}),()=>v({controls:e})}}),[s,E]),C.createElement("primitive",T.default({ref:r,object:E},m))})),gt=C.createContext(null);function wt(){const e=C.useContext(gt);if(!e)throw new Error("useMotion hook must be used in a MotionPathControls component.");return e}function zt({points:e=50,color:t="black"}){const{path:r}=wt(),[n,a]=C.useState([]),o=C.useMemo((()=>new R.MeshBasicMaterial({color:t})),[t]),i=C.useMemo((()=>new R.SphereGeometry(.025,16,16)),[]),s=C.useRef([]);return C.useEffect((()=>{r.curves!==s.current&&(a(r.getPoints(e)),s.current=r.curves)})),n.map(((e,t)=>C.createElement("mesh",{key:t,material:o,geometry:i,position:[e.x,e.y,e.z]})))}const bt=C.forwardRef((({children:e,curves:t=[],debug:r=!1,debugColor:n="black",object:o,focus:i,loop:l=!0,offset:c,smooth:u=!1,eps:d=1e-5,damping:m=.1,focusDamping:f=.1,maxSpeed:p=1/0,...h},x)=>{const{camera:y}=a.useThree(),v=C.useRef(null),g=C.useRef(null!=c?c:0),w=C.useMemo((()=>new R.CurvePath),[]),z=C.useMemo((()=>({focus:i,object:(null==o?void 0:o.current)instanceof R.Object3D?o:{current:y},path:w,current:g.current,offset:g.current,point:new R.Vector3,tangent:new R.Vector3,next:new R.Vector3})),[i,o]);C.useLayoutEffect((()=>{var e,r;w.curves=[];const n=t.length>0?t:null!==(e=null==(r=v.current)||null==(r=r.__r3f)?void 0:r.objects)&&void 0!==e?e:[];for(let e=0;e<n.length;e++)w.add(n[e]);if(u){const e=w.getPoints("number"==typeof u?u:1),t=new R.CatmullRomCurve3(e);w.curves=[t]}w.updateArcLengths()})),C.useImperativeHandle(x,(()=>Object.assign(v.current,{motion:z})),[z]),C.useLayoutEffect((()=>{g.current=s.misc.repeat(g.current,1)}),[c]);const b=C.useMemo((()=>new R.Vector3),[]);return a.useFrame(((e,t)=>{const r=z.offset;if(s.easing.damp(g,"current",void 0!==c?c:z.current,m,t,p,void 0,d),z.offset=l?s.misc.repeat(g.current,1):s.misc.clamp(g.current,0,1),w.getCurveLengths().length>0){w.getPointAt(z.offset,z.point),w.getTangentAt(z.offset,z.tangent).normalize(),w.getPointAt(s.misc.repeat(g.current-(r-z.offset),1),z.next);const e=(null==o?void 0:o.current)instanceof R.Object3D?o.current:y;e.position.copy(z.point),i&&s.easing.dampLookAt(e,(e=>(null==e?void 0:e.current)instanceof R.Object3D)(i)?i.current.getWorldPosition(b):i,f,t,p,void 0,d)}})),C.createElement("group",T.default({ref:v},h),C.createElement(gt.Provider,{value:z},e,r&&C.createElement(zt,{color:n})))}));function Et({defaultScene:e,defaultCamera:t,renderPriority:r=1}){const{gl:n,scene:o,camera:i}=a.useThree();let s;return a.useFrame((()=>{s=n.autoClear,1===r&&(n.autoClear=!0,n.render(e,t)),n.autoClear=!1,n.clearDepth(),n.render(o,i),n.autoClear=s}),r),C.createElement("group",{onPointerOver:()=>null})}function Mt({children:e,renderPriority:t=1}){const{scene:r,camera:n}=a.useThree(),[o]=C.useState((()=>new R.Scene));return C.createElement(C.Fragment,null,a.createPortal(C.createElement(C.Fragment,null,e,C.createElement(Et,{defaultScene:r,defaultCamera:n,renderPriority:t})),o,{events:{priority:t+1}}))}const St=C.createContext({}),Tt=()=>C.useContext(St),Ct=2*Math.PI,Pt=new n.Object3D,Rt=new n.Matrix4,[Dt,Ft]=[new n.Quaternion,new n.Quaternion],kt=new n.Vector3,_t=new n.Vector3,At=e=>"getTarget"in e,Lt="#f0f0f0",It="#999",Bt="black",Vt="black",Ut=["Right","Left","Top","Bottom","Front","Back"],Ot=e=>new n.Vector3(...e).multiplyScalar(.38),Nt=[[1,1,1],[1,1,-1],[1,-1,1],[1,-1,-1],[-1,1,1],[-1,1,-1],[-1,-1,1],[-1,-1,-1]].map(Ot),jt=[.25,.25,.25],Wt=[[1,1,0],[1,0,1],[1,0,-1],[1,-1,0],[0,1,1],[0,1,-1],[0,-1,1],[0,-1,-1],[-1,1,0],[-1,0,1],[-1,0,-1],[-1,-1,0]].map(Ot),Gt=Wt.map((e=>e.toArray().map((e=>0==e?.5:.25)))),Ht=({hover:e,index:t,font:r="20px Inter var, Arial, sans-serif",faces:o=Ut,color:i=Lt,hoverColor:s=It,textColor:l=Bt,strokeColor:c=Vt,opacity:u=1})=>{const d=a.useThree((e=>e.gl)),m=C.useMemo((()=>{const e=document.createElement("canvas");e.width=128,e.height=128;const a=e.getContext("2d");return a.fillStyle=i,a.fillRect(0,0,e.width,e.height),a.strokeStyle=c,a.strokeRect(0,0,e.width,e.height),a.font=r,a.textAlign="center",a.fillStyle=l,a.fillText(o[t].toUpperCase(),64,76),new n.CanvasTexture(e)}),[t,o,r,i,l,c]);return C.createElement("meshBasicMaterial",{map:m,"map-anisotropy":d.capabilities.getMaxAnisotropy()||1,attach:`material-${t}`,color:e?s:"white",transparent:!0,opacity:u})},$t=e=>{const{tweenCamera:t}=Tt(),[r,n]=C.useState(null);return C.createElement("mesh",{onPointerOut:e=>{e.stopPropagation(),n(null)},onPointerMove:e=>{e.stopPropagation(),n(Math.floor(e.faceIndex/2))},onClick:e.onClick||(e=>{e.stopPropagation(),t(e.face.normal)})},[...Array(6)].map(((t,n)=>C.createElement(Ht,T.default({key:n,index:n,hover:r===n},e)))),C.createElement("boxGeometry",null))},qt=({onClick:e,dimensions:t,position:r,hoverColor:n=It})=>{const{tweenCamera:a}=Tt(),[o,i]=C.useState(!1);return C.createElement("mesh",{scale:1.01,position:r,onPointerOver:e=>{e.stopPropagation(),i(!0)},onPointerOut:e=>{e.stopPropagation(),i(!1)},onClick:e||(e=>{e.stopPropagation(),a(r)})},C.createElement("meshBasicMaterial",{color:o?n:"white",transparent:!0,opacity:.6,visible:o}),C.createElement("boxGeometry",{args:t}))};function Xt({scale:e=[.8,.05,.05],color:t,rotation:r}){return C.createElement("group",{rotation:r},C.createElement("mesh",{position:[.4,0,0]},C.createElement("boxGeometry",{args:e}),C.createElement("meshBasicMaterial",{color:t,toneMapped:!1})))}function Zt({onClick:e,font:t,disabled:r,arcStyle:o,label:i,labelColor:s,axisHeadScale:l=1,...c}){const u=a.useThree((e=>e.gl)),d=C.useMemo((()=>{const e=document.createElement("canvas");e.width=64,e.height=64;const r=e.getContext("2d");return r.beginPath(),r.arc(32,32,16,0,2*Math.PI),r.closePath(),r.fillStyle=o,r.fill(),i&&(r.font=t,r.textAlign="center",r.fillStyle=s,r.fillText(i,32,41)),new n.CanvasTexture(e)}),[o,i,s,t]),[m,f]=C.useState(!1),p=(i?1:.75)*(m?1.2:1)*l;return C.createElement("sprite",T.default({scale:p,onPointerOver:r?void 0:e=>{e.stopPropagation(),f(!0)},onPointerOut:r?void 0:e||(e=>{e.stopPropagation(),f(!1)})},c),C.createElement("spriteMaterial",{map:d,"map-anisotropy":u.capabilities.getMaxAnisotropy()||1,alphaTest:.3,opacity:i?1:.75,toneMapped:!1}))}const Yt=Te({cellSize:.5,sectionSize:1,fadeDistance:100,fadeStrength:1,fadeFrom:1,cellThickness:.5,sectionThickness:1,cellColor:new R.Color,sectionColor:new R.Color,infiniteGrid:!1,followCamera:!1,worldCamProjPosition:new R.Vector3,worldPlanePosition:new R.Vector3},"\n    varying vec3 localPosition;\n    varying vec4 worldPosition;\n\n    uniform vec3 worldCamProjPosition;\n    uniform vec3 worldPlanePosition;\n    uniform float fadeDistance;\n    uniform bool infiniteGrid;\n    uniform bool followCamera;\n\n    void main() {\n      localPosition = position.xzy;\n      if (infiniteGrid) localPosition *= 1.0 + fadeDistance;\n      \n      worldPosition = modelMatrix * vec4(localPosition, 1.0);\n      if (followCamera) {\n        worldPosition.xyz += (worldCamProjPosition - worldPlanePosition);\n        localPosition = (inverse(modelMatrix) * worldPosition).xyz;\n      }\n\n      gl_Position = projectionMatrix * viewMatrix * worldPosition;\n    }\n  ",`\n    varying vec3 localPosition;\n    varying vec4 worldPosition;\n\n    uniform vec3 worldCamProjPosition;\n    uniform float cellSize;\n    uniform float sectionSize;\n    uniform vec3 cellColor;\n    uniform vec3 sectionColor;\n    uniform float fadeDistance;\n    uniform float fadeStrength;\n    uniform float fadeFrom;\n    uniform float cellThickness;\n    uniform float sectionThickness;\n\n    float getGrid(float size, float thickness) {\n      vec2 r = localPosition.xz / size;\n      vec2 grid = abs(fract(r - 0.5) - 0.5) / fwidth(r);\n      float line = min(grid.x, grid.y) + 1.0 - thickness;\n      return 1.0 - min(line, 1.0);\n    }\n\n    void main() {\n      float g1 = getGrid(cellSize, cellThickness);\n      float g2 = getGrid(sectionSize, sectionThickness);\n\n      vec3 from = worldCamProjPosition*vec3(fadeFrom);\n      float dist = distance(from, worldPosition.xyz);\n      float d = 1.0 - min(dist / fadeDistance, 1.0);\n      vec3 color = mix(cellColor, sectionColor, min(1.0, sectionThickness * g2));\n\n      gl_FragColor = vec4(color, (g1 + g2) * pow(d, fadeStrength));\n      gl_FragColor.a = mix(0.75 * gl_FragColor.a, gl_FragColor.a, g2);\n      if (gl_FragColor.a <= 0.0) discard;\n\n      #include <tonemapping_fragment>\n      #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n    }\n  `),Qt=C.forwardRef((({args:e,cellColor:t="#000000",sectionColor:r="#2080ff",cellSize:n=.5,sectionSize:o=1,followCamera:i=!1,infiniteGrid:s=!1,fadeDistance:l=100,fadeStrength:c=1,fadeFrom:u=1,cellThickness:d=.5,sectionThickness:m=1,side:f=R.BackSide,...p},h)=>{a.extend({GridMaterial:Yt});const x=C.useRef(null);C.useImperativeHandle(h,(()=>x.current),[]);const y=new R.Plane,v=new R.Vector3(0,1,0),g=new R.Vector3(0,0,0);a.useFrame((e=>{y.setFromNormalAndCoplanarPoint(v,g).applyMatrix4(x.current.matrixWorld);const t=x.current.material,r=t.uniforms.worldCamProjPosition,n=t.uniforms.worldPlanePosition;y.projectPoint(e.camera.position,r.value),n.value.set(0,0,0).applyMatrix4(x.current.matrixWorld)}));const w={cellSize:n,sectionSize:o,cellColor:t,sectionColor:r,cellThickness:d,sectionThickness:m},z={fadeDistance:l,fadeStrength:c,fadeFrom:u,infiniteGrid:s,followCamera:i};return C.createElement("mesh",T.default({ref:x,frustumCulled:!1},p),C.createElement("gridMaterial",T.default({transparent:!0,"extensions-derivatives":!0,side:f},w,z)),C.createElement("planeGeometry",{args:e}))}));function Kt(e,{path:t}){const[r]=a.useLoader(n.CubeTextureLoader,[e],(e=>e.setPath(t)));return r}function Jt(e){return a.useLoader(u.FBXLoader,e)}Kt.preload=(e,{path:t})=>a.useLoader.preload(n.CubeTextureLoader,[e],(e=>e.setPath(t))),Jt.preload=e=>a.useLoader.preload(u.FBXLoader,e),Jt.clear=e=>a.useLoader.clear(u.FBXLoader,e);const er="https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master";function tr(e,r=`${er}/basis/`){const n=a.useThree((e=>e.gl)),o=a.useLoader(u.KTX2Loader,Ce(e)?Object.values(e):e,(e=>{e.detectSupport(n),e.setTranscoderPath(r)}));if(t.useEffect((()=>{(Array.isArray(o)?o:[o]).forEach(n.initTexture)}),[n,o]),Ce(e)){const t=Object.keys(e),r={};return t.forEach((e=>Object.assign(r,{[e]:o[t.indexOf(e)]}))),r}return o}tr.preload=(e,t=`${er}/basis/`)=>a.useLoader.preload(u.KTX2Loader,e,(e=>{e.setTranscoderPath(t)})),tr.clear=e=>a.useLoader.clear(u.KTX2Loader,e);const rr=((e,t)=>"undefined"!=typeof window&&"function"==typeof(null==(e=window.document)?void 0:e.createElement)&&"string"==typeof(null==(t=window.navigator)?void 0:t.userAgent))();let nr=null;function ar(e,{unsuspend:r="loadedmetadata",start:n=!0,hls:o={},crossOrigin:i="anonymous",muted:s=!0,loop:l=!0,playsInline:c=!0,onVideoFrame:u,...d}={}){const m=a.useThree((e=>e.gl)),p=t.useRef(null),h=f.suspend((()=>new Promise((async t=>{let n,a;"string"==typeof e?n=e:a=e;const u=Object.assign(document.createElement("video"),{src:n,srcObject:a,crossOrigin:i,loop:l,muted:s,playsInline:c,...d});if(n&&rr&&n.endsWith(".m3u8")){const e=p.current=await async function(...e){var t;null!==(t=nr)&&void 0!==t||(nr=await Promise.resolve().then((function(){return S(require("hls.js"))})));const r=nr.default;return r.isSupported()?new r(...e):null}(o);e&&(e.on(x.Events.MEDIA_ATTACHED,(()=>{e.loadSource(n)})),e.attachMedia(u))}const f=new R.VideoTexture(u);"colorSpace"in f?f.colorSpace=m.outputColorSpace:f.encoding=m.outputEncoding,u.addEventListener(r,(()=>t(f)))}))),[e]),y=h.source.data;return ir(y,u),t.useEffect((()=>(n&&h.image.play(),()=>{p.current&&(p.current.destroy(),p.current=null)})),[h,n]),h}const or=t.forwardRef((({children:e,src:r,...n},a)=>{const o=ar(r,n);return t.useEffect((()=>()=>{o.dispose()}),[o]),t.useImperativeHandle(a,(()=>o),[o]),C.createElement(C.Fragment,null,null==e?void 0:e(o))})),ir=(e,r)=>{t.useEffect((()=>{if(!r)return;if(!e.requestVideoFrameCallback)return;let t;const n=(...a)=>{r(...a),t=e.requestVideoFrameCallback(n)};return e.requestVideoFrameCallback(n),()=>e.cancelVideoFrameCallback(t)}),[e,r])},sr=(e,t)=>{if(Array.isArray(e))return e[0];return e[null!=t?t:Object.keys(e)[0]][0]},lr=e=>{for(let t=3;t<e.length;t+=4)if(0!==e[t])return!1;return!0};function cr(e,r,o,i,s,l){const c=C.useRef(a.useThree((e=>e.viewport))),u=C.useRef(null),d=C.useRef(0),m=C.useRef(e),f=C.useRef(r),p=C.useRef(o),[h,x]=t.useState(null),[y,v]=C.useState(new R.Texture),g=C.useMemo((()=>new R.TextureLoader),[]),[w,z]=t.useState(null),b=C.useCallback(((e,t,r)=>{const n=t*(c.current.aspect>e/t?c.current.width/e:c.current.height/t),a=e*(c.current.aspect>e/t?c.current.width/e:c.current.height/t)*r,o=n*r;let i=Math.min(1,a),s=Math.min(1,o);return a>1&&(i=1,s=o/a*1),new R.Vector3(i,s,1)}),[]),E=C.useCallback(((e,t)=>{if(e.image){const r=document.createElement("canvas"),n=r.getContext("2d",l);if(!n)throw new Error("Failed to get 2d context");r.width=e.image.width,r.height=e.image.height,n.drawImage(e.image,0,0);const a=e.image.width,o=e.image.height,i=Math.round(Math.sqrt(t*(a/o))),s=Math.round(t/i),c=a/i,u=o/s,d=[];for(let e=0;e<s;e++)for(let r=0;r<i;r++){if(e*i+r>=t){d.push({row:e,col:r});continue}const a=n.getImageData(r*c,e*u,c,u).data;lr(a)&&d.push({row:e,col:r})}return{rows:s,columns:i,frameWidth:c,frameHeight:u,emptyFrames:d}}return{rows:0,columns:0,frameWidth:0,frameHeight:0,emptyFrames:[]}}),[l]),M=C.useCallback((e=>{const t=e=>{let t=null;for(const r of e){const{w:e,h:n}=r.frame,a=e*n;(!t||a>t.area)&&(t={w:e,h:n,area:a})}return e.map((e=>{const{w:r,h:n}=e.frame,a=r*n,o=t?a===t.area?1:Math.sqrt(a/t.area):1;return{...e,scaleRatio:o}}))};if(Array.isArray(e))return t(e);{const r={};for(const n in e)r[n]=t(e[n]);return r}}),[]),S=C.useCallback((()=>{const e={},t=u.current,r=p.current;if(t){if(r&&Array.isArray(t.frames)){for(let n=0;n<r.length;n++){e[r[n]]=[];for(const a of t.frames){const t=a.frame,o=a.sourceSize.w,i=a.sourceSize.h;"string"==typeof a.filename&&-1!==a.filename.toLowerCase().indexOf(r[n].toLowerCase())&&e[r[n]].push({...a,frame:t,sourceSize:{w:o,h:i}})}}for(const t in e){const r=M(e[t]);Array.isArray(r)&&(e[t]=r)}return e}if(r&&"object"==typeof t.frames){for(let n=0;n<r.length;n++){e[r[n]]=[];for(const a in t.frames){const o=t.frames[a],i=o.frame,s=o.sourceSize.w,l=o.sourceSize.h;"string"==typeof a&&-1!==a.toLowerCase().indexOf(r[n].toLowerCase())&&e[r[n]].push({...o,frame:i,sourceSize:{w:s,h:l}})}}for(const t in e){const r=M(e[t]);Array.isArray(r)&&(e[t]=r)}return e}{let e=[];return null!=t&&t.frames&&(e=Array.isArray(t.frames)?t.frames.map((e=>({...e,x:e.frame.x,y:e.frame.y,w:e.frame.w,h:e.frame.h}))):Object.values(t.frames).flat().map((e=>({...e,x:e.frame.x,y:e.frame.y,w:e.frame.w,h:e.frame.h})))),M(e)}}return[]}),[M,u]),T=C.useCallback(((e,t)=>{let r=new R.Vector3(1,1,1);if(null===e){if(t&&i){const e=t.image.width,n=t.image.height;d.current=i;const{rows:a,columns:o,frameWidth:s,frameHeight:l,emptyFrames:c}=E(t,i),m={frames:[],meta:{version:"1.0",size:{w:e,h:n},rows:a,columns:o,frameWidth:s,frameHeight:l,scale:"1"}};for(let e=0;e<a;e++)for(let t=0;t<o;t++){(null!=c?c:[]).some((r=>r.row===e&&r.col===t))||Array.isArray(m.frames)&&m.frames.push({frame:{x:t*s,y:e*l,w:s,h:l},scaleRatio:1,rotated:!1,trimmed:!1,spriteSourceSize:{x:0,y:0,w:s,h:l},sourceSize:{w:s,h:l}})}r=b(s,l,.1),u.current=m}u.current&&u.current.frames&&(u.current.frames=M(u.current.frames))}else if(t){u.current=e,u.current.frames=S(),d.current=Array.isArray(e.frames)?e.frames.length:Object.keys(e.frames).length;const{w:t,h:n}=sr(e.frames).sourceSize;r=b(t,n,.1)}x(u.current),"encoding"in t?t.encoding=3001:"colorSpace"in t&&(t.colorSpace=R.SRGBColorSpace),v(t),z({spriteTexture:t,spriteData:u.current,aspect:r})}),[E,i,S,b,M]),P=C.useCallback(((e,t,r)=>{const n=fetch(e).then((e=>e.json())),a=new Promise((e=>{g.load(t,e)}));Promise.all([n,a]).then((e=>{r(e[0],e[1])}))}),[g]),D=C.useCallback((e=>{if(!e&&!m.current)throw new Error("Either textureUrl or input must be provided");const t=null!=e?e:m.current;if(!t)throw new Error("A valid texture URL must be provided");g.load(t,(e=>T(null,e)))}),[g,T]),F=C.useCallback(((e,t)=>{t&&e?P(t,e,T):D(e)}),[P,D,T]);return C.useLayoutEffect((()=>{f.current&&m.current?P(f.current,m.current,T):m.current&&D();const e=m.current;return()=>{e&&a.useLoader.clear(n.TextureLoader,e)}}),[P,D,T]),C.useLayoutEffect((()=>{null==s||s(y,null!=h?h:null)}),[y,h,s]),{spriteObj:w,loadJsonAndTexture:F}}function ur(e,t,...r){const n=C.useRef(),o=a.useThree((e=>e.scene));return C.useLayoutEffect((()=>{let a;if(e&&null!=e&&e.current&&t&&(n.current=a=new t(e.current,...r)),a)return a.traverse((e=>e.raycast=()=>null)),o.add(a),()=>{n.current=void 0,o.remove(a),null==a.dispose||a.dispose()}}),[o,t,e,...r]),a.useFrame((()=>{var e;null==(e=n.current)||null==e.update||e.update()})),n}cr.preload=e=>a.useLoader.preload(n.TextureLoader,e),cr.clear=e=>a.useLoader.clear(n.TextureLoader,e);function dr(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}const mr=C.forwardRef((function({className:e,parent:t,id:r,clearStatsGlStyle:n,...o},i){const s=a.useThree((e=>e.gl)),l=C.useMemo((()=>{const e=new k.default({...o});return e.init(s),e}),[s]);return C.useImperativeHandle(i,(()=>l.domElement),[l]),C.useEffect((()=>{if(l){const o=t&&t.current||document.body;null==o||o.appendChild(l.domElement),l.domElement.querySelectorAll("canvas").forEach((e=>{e.style.removeProperty("position")})),r&&(l.domElement.id=r),n&&l.domElement.removeAttribute("style"),l.domElement.removeAttribute("style");const i=(null!=e?e:"").split(" ").filter((e=>e));i.length&&l.domElement.classList.add(...i);const s=a.addAfterEffect((()=>l.update()));return()=>{i.length&&l.domElement.classList.remove(...i),null==o||o.removeChild(l.domElement),s()}}}),[t,l,e,r,n]),null}));const fr=e=>f.suspend((()=>g.getGPUTier(e)),["useDetectGPU"]);const pr=e=>e.isMesh;const hr=C.forwardRef((({enabled:e=!0,firstHitOnly:t=!1,children:r,strategy:o=w.SAH,verbose:i=!1,setBoundingBox:s=!0,maxDepth:l=40,maxLeafTris:c=10,indirect:u=!1,...d},m)=>{const f=C.useRef(null),p=a.useThree((e=>e.raycaster));return C.useImperativeHandle(m,(()=>f.current),[]),C.useEffect((()=>{if(e){const e={strategy:o,verbose:i,setBoundingBox:s,maxDepth:l,maxLeafTris:c,indirect:u},r=f.current;return p.firstHitOnly=t,r.traverse((t=>{pr(t)&&!t.geometry.boundsTree&&t.raycast===n.Mesh.prototype.raycast&&(t.raycast=w.acceleratedRaycast,t.geometry.computeBoundsTree=w.computeBoundsTree,t.geometry.disposeBoundsTree=w.disposeBoundsTree,t.geometry.computeBoundsTree(e))})),()=>{delete p.firstHitOnly,r.traverse((e=>{pr(e)&&e.geometry.boundsTree&&(e.geometry.disposeBoundsTree(),e.raycast=n.Mesh.prototype.raycast)}))}}}),[]),C.createElement("group",T.default({ref:f},d),r)}));function xr(e){const t=C.useRef(null),r=C.useRef(!1),n=C.useRef(!1),o=C.useRef(e);return C.useLayoutEffect((()=>{o.current=e}),[e]),C.useEffect((()=>{const e=t.current;if(e){const t=a.addEffect((()=>(r.current=!1,!0))),i=e.onBeforeRender;e.onBeforeRender=()=>r.current=!0;const s=a.addAfterEffect((()=>(r.current!==n.current&&(null==o.current||o.current(n.current=r.current)),!0)));return()=>{e.onBeforeRender=i,t(),s()}}}),[]),t}const yr=new R.Box3,vr=new R.Vector3;const gr=e=>Math.sqrt(1-Math.pow(e-1,2));class wr{constructor({size:e=256,maxAge:t=750,radius:r=.3,intensity:n=.2,interpolate:a=0,smoothing:o=0,minForce:i=.3,blend:s="screen",ease:l=gr}={}){this.size=e,this.maxAge=t,this.radius=r,this.intensity=n,this.ease=l,this.interpolate=a,this.smoothing=o,this.minForce=i,this.blend=s,this.trail=[],this.force=0,this.initTexture()}initTexture(){this.canvas=document.createElement("canvas"),this.canvas.width=this.canvas.height=this.size;const e=this.canvas.getContext("2d");if(null===e)throw new Error("2D not available");this.ctx=e,this.ctx.fillStyle="black",this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height),this.texture=new n.Texture(this.canvas),this.canvas.id="touchTexture",this.canvas.style.width=this.canvas.style.height=`${this.canvas.width}px`}update(e){this.clear(),this.trail.forEach(((t,r)=>{t.age+=1e3*e,t.age>this.maxAge&&this.trail.splice(r,1)})),this.trail.length||(this.force=0),this.trail.forEach((e=>{this.drawTouch(e)})),this.texture.needsUpdate=!0}clear(){this.ctx.globalCompositeOperation="source-over",this.ctx.fillStyle="black",this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height)}addTouch(e){const t=this.trail[this.trail.length-1];if(t){const r=t.x-e.x,n=t.y-e.y,a=r*r+n*n,o=Math.max(this.minForce,Math.min(1e4*a,1));if(this.force=function(e,t,r=.9){return t*r+e*(1-r)}(o,this.force,this.smoothing),this.interpolate){const e=Math.ceil(a/Math.pow(.5*this.radius/this.interpolate,2));if(e>1)for(let a=1;a<e;a++)this.trail.push({x:t.x-r/e*a,y:t.y-n/e*a,age:0,force:o})}}this.trail.push({x:e.x,y:e.y,age:0,force:this.force})}drawTouch(e){const t={x:e.x*this.size,y:(1-e.y)*this.size};let r=1;r=e.age<.3*this.maxAge?this.ease(e.age/(.3*this.maxAge)):this.ease(1-(e.age-.3*this.maxAge)/(.7*this.maxAge)),r*=e.force,this.ctx.globalCompositeOperation=this.blend;const n=this.size*this.radius*r,a=this.ctx.createRadialGradient(t.x,t.y,Math.max(0,.25*n),t.x,t.y,Math.max(0,n));a.addColorStop(0,`rgba(255, 255, 255, ${this.intensity})`),a.addColorStop(1,"rgba(0, 0, 0, 0.0)"),this.ctx.beginPath(),this.ctx.fillStyle=a,this.ctx.arc(t.x,t.y,Math.max(0,n),0,2*Math.PI),this.ctx.fill()}}function zr(e={}){const{size:r,maxAge:n,radius:o,intensity:i,interpolate:s,smoothing:l,minForce:c,blend:u,ease:d}=e,m=t.useMemo((()=>new wr(e)),[r,n,o,i,s,l,c,u,d]);a.useFrame(((e,t)=>{m.update(t)}));const f=t.useCallback((e=>m.addTouch(e.uv)),[m]);return[m.texture,f]}const br=C.forwardRef((function({children:e,disable:t,disableX:r,disableY:a,disableZ:o,left:i,right:s,top:l,bottom:c,front:u,back:d,onCentered:m,precise:f=!0,cacheKey:p=0,...h},x){const y=C.useRef(null),v=C.useRef(null),g=C.useRef(null);return C.useLayoutEffect((()=>{v.current.matrixWorld.identity();const e=(new n.Box3).setFromObject(g.current,f),p=new n.Vector3,h=new n.Sphere,x=e.max.x-e.min.x,w=e.max.y-e.min.y,z=e.max.z-e.min.z;e.getCenter(p),e.getBoundingSphere(h);const b=l?w/2:c?-w/2:0,E=i?-x/2:s?x/2:0,M=u?z/2:d?-z/2:0;v.current.position.set(t||r?0:-p.x+E,t||a?0:-p.y+b,t||o?0:-p.z+M),void 0!==m&&m({parent:y.current.parent,container:y.current,width:x,height:w,depth:z,boundingBox:e,boundingSphere:h,center:p,verticalAlignment:b,horizontalAlignment:E,depthAlignment:M})}),[p,m,l,i,u,t,r,a,o,f,s,c,d]),C.useImperativeHandle(x,(()=>y.current),[]),C.createElement("group",T.default({ref:y},h),C.createElement("group",{ref:v},C.createElement("group",{ref:g},e)))})),Er=C.forwardRef((({font:e,color:t="#cbcbcb",bevelSize:r=.04,debug:n=!1,children:a,...o},i)=>{const[s,l]=C.useState(0),c=C.useCallback(((e=1)=>l(s+e)),[s]),u=C.useCallback(((e=1)=>l(s-e)),[s]),d=C.useMemo((()=>({incr:c,decr:u})),[c,u]);return C.useImperativeHandle(i,(()=>d),[d]),C.createElement("group",o,C.createElement(C.Suspense,{fallback:null},C.createElement(br,{top:!0,cacheKey:JSON.stringify({counter:s,font:e})},C.createElement(Ee,{bevelEnabled:!0,bevelSize:r,font:e},n?C.createElement("meshNormalMaterial",{wireframe:!0}):C.createElement("meshStandardMaterial",{color:t}),s))),a)})),Mr=(e,t)=>{"updateRanges"in e?e.updateRanges[0]=t:e.updateRange=t};const Sr=new R.Matrix4,Tr=new R.Matrix4,Cr=[],Pr=new R.Mesh;class Rr extends R.Group{constructor(){super(),this.color=new R.Color("white"),this.instance={current:void 0},this.instanceKey={current:void 0}}get geometry(){var e;return null==(e=this.instance.current)?void 0:e.geometry}raycast(e,t){const r=this.instance.current;if(!r)return;if(!r.geometry||!r.material)return;Pr.geometry=r.geometry;const n=r.matrixWorld,a=r.userData.instances.indexOf(this.instanceKey);if(!(-1===a||a>r.count)){r.getMatrixAt(a,Sr),Tr.multiplyMatrices(n,Sr),Pr.matrixWorld=Tr,r.material instanceof R.Material?Pr.material.side=r.material.side:Pr.material.side=r.material[0].side,Pr.raycast(e,Cr);for(let e=0,r=Cr.length;e<r;e++){const r=Cr[e];r.instanceId=a,r.object=this,t.push(r)}Cr.length=0}}}const Dr=C.createContext(null),Fr=new R.Matrix4,kr=new R.Matrix4,_r=new R.Matrix4,Ar=new R.Vector3,Lr=new R.Quaternion,Ir=new R.Vector3,Br=C.forwardRef((({context:e,children:t,...r},n)=>{C.useMemo((()=>a.extend({PositionMesh:Rr})),[]);const o=C.useRef();C.useImperativeHandle(n,(()=>o.current),[]);const{subscribe:i,getParent:s}=C.useContext(e||Dr);return C.useLayoutEffect((()=>i(o)),[]),C.createElement("positionMesh",T.default({instance:s(),instanceKey:o,ref:o},r),t)})),Vr=C.forwardRef((({context:e,children:t,range:r,limit:n=1e3,frames:o=1/0,...i},s)=>{const[{localContext:l,instance:c}]=C.useState((()=>{const e=C.createContext(null);return{localContext:e,instance:C.forwardRef(((t,r)=>C.createElement(Br,T.default({context:e},t,{ref:r}))))}})),u=C.useRef(null);C.useImperativeHandle(s,(()=>u.current),[]);const[d,m]=C.useState([]),[[f,p]]=C.useState((()=>{const e=new Float32Array(16*n);for(let t=0;t<n;t++)_r.identity().toArray(e,16*t);return[e,new Float32Array([...new Array(3*n)].map((()=>1)))]}));C.useEffect((()=>{u.current.instanceMatrix.needsUpdate=!0}));let h=0,x=0;const y=C.useRef([]);C.useLayoutEffect((()=>{y.current=Object.entries(u.current.geometry.attributes).filter((([e,t])=>t.isInstancedBufferAttribute))})),a.useFrame((()=>{if(o===1/0||h<o){u.current.updateMatrix(),u.current.updateMatrixWorld(),Fr.copy(u.current.matrixWorld).invert(),x=Math.min(n,void 0!==r?r:n,d.length),u.current.count=x,Mr(u.current.instanceMatrix,{offset:0,count:16*x}),Mr(u.current.instanceColor,{offset:0,count:3*x});for(let e=0;e<d.length;e++){const t=d[e].current;t.matrixWorld.decompose(Ar,Lr,Ir),kr.compose(Ar,Lr,Ir).premultiply(Fr),kr.toArray(f,16*e),u.current.instanceMatrix.needsUpdate=!0,t.color.toArray(p,3*e),u.current.instanceColor.needsUpdate=!0}h++}}));const v=C.useMemo((()=>({getParent:()=>u,subscribe:e=>(m((t=>[...t,e])),()=>m((t=>t.filter((t=>t.current!==e.current)))))})),[]);return C.createElement("instancedMesh",T.default({userData:{instances:d,limit:n,frames:o},matrixAutoUpdate:!1,ref:u,args:[null,null,0],raycast:()=>null},i),C.createElement("instancedBufferAttribute",{attach:"instanceMatrix",count:f.length/16,array:f,itemSize:16,usage:R.DynamicDrawUsage}),C.createElement("instancedBufferAttribute",{attach:"instanceColor",count:p.length/3,array:p,itemSize:3,usage:R.DynamicDrawUsage}),"function"==typeof t?C.createElement(l.Provider,{value:v},t(c)):e?C.createElement(e.Provider,{value:v},t):C.createElement(Dr.Provider,{value:v},t))})),Ur=C.forwardRef((function({meshes:e,children:t,...r},n){const a=Array.isArray(e);if(!a)for(const t of Object.keys(e))e[t].isMesh||delete e[t];return C.createElement("group",{ref:n},C.createElement(_.default,{components:(a?e:Object.values(e)).map((({geometry:e,material:t})=>C.createElement(Vr,T.default({key:e.uuid,geometry:e,material:t},r))))},(r=>a?t(...r):t(Object.keys(e).filter((t=>e[t].isMesh)).reduce(((e,t,n)=>({...e,[t]:r[n]})),{})))))}));const Or=C.forwardRef((({name:e,defaultValue:t,normalized:r,usage:n=R.DynamicDrawUsage},o)=>{const i=C.useRef(null);C.useImperativeHandle(o,(()=>i.current),[]),C.useLayoutEffect((()=>{const r=i.current.__r3f.parent;r.geometry.attributes[e]=i.current;const n=Array.isArray(t)?t:[t],a=Array.from({length:r.userData.limit},(()=>n)).flat();return i.current.array=new Float32Array(a),i.current.itemSize=n.length,i.current.count=a.length/i.current.itemSize,()=>{delete r.geometry.attributes[e]}}),[e]);let s=0;return a.useFrame((()=>{const t=i.current.__r3f.parent;if(t.userData.frames===1/0||s<t.userData.frames){for(let r=0;r<t.userData.instances.length;r++){const n=t.userData.instances[r].current[e];void 0!==n&&(i.current.set(Array.isArray(n)?n:"function"==typeof n.toArray?n.toArray():[n],r*i.current.itemSize),i.current.needsUpdate=!0)}s++}})),C.createElement("instancedBufferAttribute",{ref:i,usage:n,normalized:r})})),Nr=C.createContext(null);const jr=new R.PlaneGeometry(1,1),Wr=C.forwardRef((({startFrame:e=0,endFrame:t,fps:r=30,frameName:n="",textureDataURL:o,textureImageURL:i,loop:s=!1,numberOfFrames:l=1,autoPlay:c=!0,animationNames:u,onStart:d,onEnd:m,onLoopEnd:f,onFrame:p,play:h,pause:x=!1,flipX:y=!1,alphaTest:v=0,children:g,asSprite:w=!1,offset:z,playBackwards:b=!1,resetOnEnd:E=!1,maxItems:M=1,instanceItems:S=[[0,0,0]],spriteDataset:P,canvasRenderingContext2DSettings:D,roundFramePosition:F=!1,meshProps:k={},..._},A)=>{const L=C.useRef(new R.Group),I=C.useRef(null),B=C.useRef(null),V=C.useRef(null),U=C.useRef(window.performance.now()),O=C.useRef(e),N=C.useRef(n),j=r>0?1e3/r:0,[W,G]=C.useState(new R.Texture),H=C.useRef(0),[$,q]=C.useState(new R.Vector3(1,1,1)),X=y?-1:1,Z=C.useRef(x),Y=C.useRef(z),Q=C.useRef(!1),{spriteObj:K,loadJsonAndTexture:J}=cr(null,null,u,l,void 0,D),ee=C.useRef(n),te=C.useCallback(((e,t)=>{if(null===t)l&&(H.current=l,b&&(O.current=l-1),I.current=t);else{var r,n;I.current=t,I.current&&Array.isArray(I.current.frames)?H.current=I.current.frames.length:I.current&&"object"==typeof I.current&&ee.current?H.current=I.current.frames[ee.current].length:H.current=0,b&&(O.current=H.current-1);const{w:a,h:o}=sr(null!==(r=null==(n=I.current)?void 0:n.frames)&&void 0!==r?r:[],ee.current).sourceSize,i=oe(a,o);q(i),B.current&&(B.current.map=e)}G(e)}),[l,b]),re=C.useCallback((()=>{if(!I.current)return;const{meta:{size:e},frames:t}=I.current,{w:r,h:a}=Array.isArray(t)?t[0].sourceSize:n&&t[n]?t[n][0].sourceSize:{w:0,h:0};B.current&&B.current.map&&(B.current.map.wrapS=B.current.map.wrapT=R.RepeatWrapping,B.current.map.center.set(0,0),B.current.map.repeat.set(1*X/(e.w/r),1/(e.h/a)));const o=1/((e.h-1)/a);B.current&&B.current.map&&(B.current.map.offset.x=0,B.current.map.offset.y=1-o),d&&d({currentFrameName:null!=n?n:"",currentFrame:O.current})}),[X,n,d]),ae=C.useMemo((()=>({current:Y.current,offset:Y.current,imageUrl:i,hasEnded:!1,ref:A})),[i,A]);C.useImperativeHandle(A,(()=>L.current),[]),C.useLayoutEffect((()=>{Y.current=z}),[z]);const oe=(e,t)=>{var r;const n=new R.Vector3,a=t/e;return n.set(1,a,1),null==(r=V.current)||r.scale.copy(n),n};C.useEffect((()=>{var e;P?te(null==P||null==(e=P.spriteTexture)?void 0:e.clone(),P.spriteData):i&&o&&J(i,o)}),[J,P,o,i,te]),C.useEffect((()=>{var e;K&&te(null==K||null==(e=K.spriteTexture)?void 0:e.clone(),null==K?void 0:K.spriteData)}),[K,te]),C.useEffect((()=>{var e;(ae.hasEnded=!1,I.current&&!0===b)?O.current=(null!==(e=I.current.frames.length)&&void 0!==e?e:0)-1:O.current=0}),[b,ae]),C.useLayoutEffect((()=>{re()}),[W,y,re]),C.useEffect((()=>{c&&(Z.current=!1)}),[c]),C.useLayoutEffect((()=>{if(N.current!==n&&n&&(O.current=0,N.current=n,ae.hasEnded=!1,j<=0&&(O.current=t||e||0),I.current)){const{w:e,h:t}=sr(I.current.frames,n).sourceSize,r=oe(e,t);q(r)}}),[n,j,ae,t,e]);const ie=(e,t,r,n)=>{var a=void 0===z?ae.current:z;const o=O.current;let i=0,s=0;oe(e,t);const l=F?Math.round((r.w-1)/e):(r.w-1)/e,c=F?Math.round((r.h-1)/t):(r.h-1)/t;if(!n[o])return;const{frame:{x:u,y:d},sourceSize:{w:m,h:f}}=n[o],p=1/l,h=1/c;if(B.current&&B.current.map&&(i=X>0?p*(u/m):p*(u/m)-B.current.map.repeat.x,s=Math.abs(1-h)-h*(d/f),B.current.map.offset.x=i,B.current.map.offset.y=s),null!=a){let e=Math.floor(a*n.length);e=Math.max(0,Math.min(e,n.length-1)),isNaN(e)&&(e=0),O.current=e}else b?O.current-=1:O.current+=1};return a.useFrame(((r,a)=>{var o,i;null!=(o=I.current)&&o.frames&&null!=(i=B.current)&&i.map&&(Z.current||ae.hasEnded||!c&&!h||((()=>{if(null===(r=I.current)||!("meta"in r)||!("frames"in r))return;var r;const{meta:{size:a},frames:o}=I.current,{w:i,h:l}=sr(o,n).sourceSize,c=Array.isArray(o)?o:n?o[n]:[],u=t||c.length-1;var p=void 0===z?ae.current:z;if(j<=0)return O.current=t||e||0,void ie(i,l,a,c);const h=window.performance.now(),x=h-U.current;if(!(x<=j)){var y=b?O.current<0:O.current>u,v=b?O.current===u:0===O.current,g=b?O.current<0:O.current>=u;if(y){if(O.current=s&&null!=e?e:0,b&&(O.current=u),s?null==f||f({currentFrameName:null!=n?n:"",currentFrame:O.current}):(null==m||m({currentFrameName:null!=n?n:"",currentFrame:O.current}),ae.hasEnded=!E,E&&(Z.current=!0)),!s)return}else v&&(null==d||d({currentFrameName:null!=n?n:"",currentFrame:O.current}));void 0!==p&&g?!1===Q.current&&(null==m||m({currentFrameName:null!=n?n:"",currentFrame:O.current}),Q.current=!0):Q.current=!1,x<=j||(U.current=h-x%j,ie(i,l,a,c))}})(),null==p||p({currentFrameName:N.current,currentFrame:O.current})))})),C.createElement("group",T.default({},_,{ref:L,scale:function(e=new R.Vector3(1,1,1),t=1){return"number"==typeof t?e.multiplyScalar(t):Array.isArray(t)?e.multiply(new R.Vector3(...t)):t instanceof R.Vector3?e.multiply(t):void 0}($,_.scale)}),C.createElement(Nr.Provider,{value:ae},w&&C.createElement(ne,null,C.createElement("mesh",T.default({ref:V,scale:1,geometry:jr},k),C.createElement("meshBasicMaterial",{premultipliedAlpha:!1,toneMapped:!1,side:R.DoubleSide,ref:B,map:W,transparent:!0,alphaTest:null!=v?v:0}))),!w&&C.createElement(Vr,T.default({geometry:jr,limit:null!=M?M:1},k),C.createElement("meshBasicMaterial",{premultipliedAlpha:!1,toneMapped:!1,side:R.DoubleSide,ref:B,map:W,transparent:!0,alphaTest:null!=v?v:0}),(null!=S?S:[0]).map(((e,t)=>C.createElement(Br,T.default({key:t,ref:1===(null==S?void 0:S.length)?V:null,position:e,scale:1},k))))),g))})),Gr=C.forwardRef((({children:e,curve:t},r)=>{const[n]=C.useState((()=>new R.Scene)),[o,i]=C.useState(),s=C.useRef(null);return C.useLayoutEffect((()=>{s.current=new u.Flow(n.children[0]),i(s.current.object3D)}),[e]),C.useEffect((()=>{var e;t&&(null==(e=s.current)||e.updateCurve(0,t))}),[t]),C.useImperativeHandle(r,(()=>s.current)),C.createElement(C.Fragment,null,a.createPortal(e,n),o&&C.createElement("primitive",{object:o}))}));class Hr extends n.MeshPhysicalMaterial{constructor(e={}){super(e),this.setValues(e),this._time={value:0},this._distort={value:.4},this._radius={value:1}}onBeforeCompile(e){e.uniforms.time=this._time,e.uniforms.radius=this._radius,e.uniforms.distort=this._distort,e.vertexShader=`\n      uniform float time;\n      uniform float radius;\n      uniform float distort;\n      #define GLSLIFY 1\nvec3 mod289(vec3 x){return x-floor(x*(1.0/289.0))*289.0;}vec4 mod289(vec4 x){return x-floor(x*(1.0/289.0))*289.0;}vec4 permute(vec4 x){return mod289(((x*34.0)+1.0)*x);}vec4 taylorInvSqrt(vec4 r){return 1.79284291400159-0.85373472095314*r;}float snoise(vec3 v){const vec2 C=vec2(1.0/6.0,1.0/3.0);const vec4 D=vec4(0.0,0.5,1.0,2.0);vec3 i=floor(v+dot(v,C.yyy));vec3 x0=v-i+dot(i,C.xxx);vec3 g=step(x0.yzx,x0.xyz);vec3 l=1.0-g;vec3 i1=min(g.xyz,l.zxy);vec3 i2=max(g.xyz,l.zxy);vec3 x1=x0-i1+C.xxx;vec3 x2=x0-i2+C.yyy;vec3 x3=x0-D.yyy;i=mod289(i);vec4 p=permute(permute(permute(i.z+vec4(0.0,i1.z,i2.z,1.0))+i.y+vec4(0.0,i1.y,i2.y,1.0))+i.x+vec4(0.0,i1.x,i2.x,1.0));float n_=0.142857142857;vec3 ns=n_*D.wyz-D.xzx;vec4 j=p-49.0*floor(p*ns.z*ns.z);vec4 x_=floor(j*ns.z);vec4 y_=floor(j-7.0*x_);vec4 x=x_*ns.x+ns.yyyy;vec4 y=y_*ns.x+ns.yyyy;vec4 h=1.0-abs(x)-abs(y);vec4 b0=vec4(x.xy,y.xy);vec4 b1=vec4(x.zw,y.zw);vec4 s0=floor(b0)*2.0+1.0;vec4 s1=floor(b1)*2.0+1.0;vec4 sh=-step(h,vec4(0.0));vec4 a0=b0.xzyw+s0.xzyw*sh.xxyy;vec4 a1=b1.xzyw+s1.xzyw*sh.zzww;vec3 p0=vec3(a0.xy,h.x);vec3 p1=vec3(a0.zw,h.y);vec3 p2=vec3(a1.xy,h.z);vec3 p3=vec3(a1.zw,h.w);vec4 norm=taylorInvSqrt(vec4(dot(p0,p0),dot(p1,p1),dot(p2,p2),dot(p3,p3)));p0*=norm.x;p1*=norm.y;p2*=norm.z;p3*=norm.w;vec4 m=max(0.6-vec4(dot(x0,x0),dot(x1,x1),dot(x2,x2),dot(x3,x3)),0.0);m=m*m;return 42.0*dot(m*m,vec4(dot(p0,x0),dot(p1,x1),dot(p2,x2),dot(p3,x3)));}\n      ${e.vertexShader}\n    `,e.vertexShader=e.vertexShader.replace("#include <begin_vertex>","\n        float updateTime = time / 50.0;\n        float noise = snoise(vec3(position / 2.0 + updateTime * 5.0));\n        vec3 transformed = vec3(position * (noise * pow(distort, 2.0) + radius));\n        ")}get time(){return this._time.value}set time(e){this._time.value=e}get distort(){return this._distort.value}set distort(e){this._distort.value=e}get radius(){return this._radius.value}set radius(e){this._radius.value=e}}const $r=C.forwardRef((({speed:e=1,...t},r)=>{const[n]=C.useState((()=>new Hr));return a.useFrame((t=>n&&(n.time=t.clock.elapsedTime*e))),C.createElement("primitive",T.default({object:n,ref:r,attach:"material"},t))}));class qr extends n.MeshStandardMaterial{constructor(e={}){super(e),this.setValues(e),this._time={value:0},this._factor={value:1}}onBeforeCompile(e){e.uniforms.time=this._time,e.uniforms.factor=this._factor,e.vertexShader=`\n      uniform float time;\n      uniform float factor;\n      ${e.vertexShader}\n    `,e.vertexShader=e.vertexShader.replace("#include <begin_vertex>","float theta = sin( time + position.y ) / 2.0 * factor;\n        float c = cos( theta );\n        float s = sin( theta );\n        mat3 m = mat3( c, 0, s, 0, 1, 0, -s, 0, c );\n        vec3 transformed = vec3( position ) * m;\n        vNormal = vNormal * m;")}get time(){return this._time.value}set time(e){this._time.value=e}get factor(){return this._factor.value}set factor(e){this._factor.value=e}}const Xr=C.forwardRef((({speed:e=1,...t},r)=>{const[n]=C.useState((()=>new qr));return a.useFrame((t=>n&&(n.time=t.clock.elapsedTime*e))),C.createElement("primitive",T.default({object:n,ref:r,attach:"material"},t))}));class Zr extends R.ShaderMaterial{constructor(e=new R.Vector2){super({uniforms:{inputBuffer:new R.Uniform(null),depthBuffer:new R.Uniform(null),resolution:new R.Uniform(new R.Vector2),texelSize:new R.Uniform(new R.Vector2),halfTexelSize:new R.Uniform(new R.Vector2),kernel:new R.Uniform(0),scale:new R.Uniform(1),cameraNear:new R.Uniform(0),cameraFar:new R.Uniform(1),minDepthThreshold:new R.Uniform(0),maxDepthThreshold:new R.Uniform(1),depthScale:new R.Uniform(0),depthToBlurRatioBias:new R.Uniform(.25)},fragmentShader:`#include <common>\n        #include <dithering_pars_fragment>      \n        uniform sampler2D inputBuffer;\n        uniform sampler2D depthBuffer;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          float depthFactor = 0.0;\n          \n          #ifdef USE_DEPTH\n            vec4 depth = texture2D(depthBuffer, vUv);\n            depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n            depthFactor *= depthScale;\n            depthFactor = max(0.0, min(1.0, depthFactor + 0.25));\n          #endif\n          \n          vec4 sum = texture2D(inputBuffer, mix(vUv0, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv1, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv2, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv3, vUv, depthFactor));\n          gl_FragColor = sum * 0.25 ;\n\n          #include <dithering_fragment>\n          #include <tonemapping_fragment>\n          #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n        }`,vertexShader:"uniform vec2 texelSize;\n        uniform vec2 halfTexelSize;\n        uniform float kernel;\n        uniform float scale;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          vec2 uv = position.xy * 0.5 + 0.5;\n          vUv = uv;\n\n          vec2 dUv = (texelSize * vec2(kernel) + halfTexelSize) * scale;\n          vUv0 = vec2(uv.x - dUv.x, uv.y + dUv.y);\n          vUv1 = vec2(uv.x + dUv.x, uv.y + dUv.y);\n          vUv2 = vec2(uv.x + dUv.x, uv.y - dUv.y);\n          vUv3 = vec2(uv.x - dUv.x, uv.y - dUv.y);\n\n          gl_Position = vec4(position.xy, 1.0, 1.0);\n        }",blending:R.NoBlending,depthWrite:!1,depthTest:!1}),this.toneMapped=!1,this.setTexelSize(e.x,e.y),this.kernel=new Float32Array([0,1,2,2,3])}setTexelSize(e,t){this.uniforms.texelSize.value.set(e,t),this.uniforms.halfTexelSize.value.set(e,t).multiplyScalar(.5)}setResolution(e){this.uniforms.resolution.value.copy(e)}}class Yr{constructor({gl:e,resolution:t,width:r=500,height:a=500,minDepthThreshold:o=0,maxDepthThreshold:i=1,depthScale:s=0,depthToBlurRatioBias:l=.25}){this.renderToScreen=!1,this.renderTargetA=new n.WebGLRenderTarget(t,t,{minFilter:n.LinearFilter,magFilter:n.LinearFilter,stencilBuffer:!1,depthBuffer:!1,type:n.HalfFloatType}),this.renderTargetB=this.renderTargetA.clone(),this.convolutionMaterial=new Zr,this.convolutionMaterial.setTexelSize(1/r,1/a),this.convolutionMaterial.setResolution(new n.Vector2(r,a)),this.scene=new n.Scene,this.camera=new n.Camera,this.convolutionMaterial.uniforms.minDepthThreshold.value=o,this.convolutionMaterial.uniforms.maxDepthThreshold.value=i,this.convolutionMaterial.uniforms.depthScale.value=s,this.convolutionMaterial.uniforms.depthToBlurRatioBias.value=l,this.convolutionMaterial.defines.USE_DEPTH=s>0;const c=new Float32Array([-1,-1,0,3,-1,0,-1,3,0]),u=new Float32Array([0,0,2,0,0,2]),d=new n.BufferGeometry;d.setAttribute("position",new n.BufferAttribute(c,3)),d.setAttribute("uv",new n.BufferAttribute(u,2)),this.screen=new n.Mesh(d,this.convolutionMaterial),this.screen.frustumCulled=!1,this.scene.add(this.screen)}render(e,t,r){const n=this.scene,a=this.camera,o=this.renderTargetA,i=this.renderTargetB;let s=this.convolutionMaterial,l=s.uniforms;l.depthBuffer.value=t.depthTexture;const c=s.kernel;let u,d,m,f=t;for(d=0,m=c.length-1;d<m;++d)u=1&d?i:o,l.kernel.value=c[d],l.inputBuffer.value=f.texture,e.setRenderTarget(u),e.render(n,a),f=u;l.kernel.value=c[d],l.inputBuffer.value=f.texture,e.setRenderTarget(this.renderToScreen?null:r),e.render(n,a)}}class Qr extends n.MeshStandardMaterial{constructor(e={}){super(e),this._tDepth={value:null},this._distortionMap={value:null},this._tDiffuse={value:null},this._tDiffuseBlur={value:null},this._textureMatrix={value:null},this._hasBlur={value:!1},this._mirror={value:0},this._mixBlur={value:0},this._blurStrength={value:.5},this._minDepthThreshold={value:.9},this._maxDepthThreshold={value:1},this._depthScale={value:0},this._depthToBlurRatioBias={value:.25},this._distortion={value:1},this._mixContrast={value:1},this.setValues(e)}onBeforeCompile(e){var t;null!=(t=e.defines)&&t.USE_UV||(e.defines.USE_UV=""),e.uniforms.hasBlur=this._hasBlur,e.uniforms.tDiffuse=this._tDiffuse,e.uniforms.tDepth=this._tDepth,e.uniforms.distortionMap=this._distortionMap,e.uniforms.tDiffuseBlur=this._tDiffuseBlur,e.uniforms.textureMatrix=this._textureMatrix,e.uniforms.mirror=this._mirror,e.uniforms.mixBlur=this._mixBlur,e.uniforms.mixStrength=this._blurStrength,e.uniforms.minDepthThreshold=this._minDepthThreshold,e.uniforms.maxDepthThreshold=this._maxDepthThreshold,e.uniforms.depthScale=this._depthScale,e.uniforms.depthToBlurRatioBias=this._depthToBlurRatioBias,e.uniforms.distortion=this._distortion,e.uniforms.mixContrast=this._mixContrast,e.vertexShader=`\n        uniform mat4 textureMatrix;\n        varying vec4 my_vUv;\n      ${e.vertexShader}`,e.vertexShader=e.vertexShader.replace("#include <project_vertex>","#include <project_vertex>\n        my_vUv = textureMatrix * vec4( position, 1.0 );\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );"),e.fragmentShader=`\n        uniform sampler2D tDiffuse;\n        uniform sampler2D tDiffuseBlur;\n        uniform sampler2D tDepth;\n        uniform sampler2D distortionMap;\n        uniform float distortion;\n        uniform float cameraNear;\n\t\t\t  uniform float cameraFar;\n        uniform bool hasBlur;\n        uniform float mixBlur;\n        uniform float mirror;\n        uniform float mixStrength;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float mixContrast;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec4 my_vUv;\n        ${e.fragmentShader}`,e.fragmentShader=e.fragmentShader.replace("#include <emissivemap_fragment>","#include <emissivemap_fragment>\n\n      float distortionFactor = 0.0;\n      #ifdef USE_DISTORTION\n        distortionFactor = texture2D(distortionMap, vUv).r * distortion;\n      #endif\n\n      vec4 new_vUv = my_vUv;\n      new_vUv.x += distortionFactor;\n      new_vUv.y += distortionFactor;\n\n      vec4 base = texture2DProj(tDiffuse, new_vUv);\n      vec4 blur = texture2DProj(tDiffuseBlur, new_vUv);\n\n      vec4 merge = base;\n\n      #ifdef USE_NORMALMAP\n        vec2 normal_uv = vec2(0.0);\n        vec4 normalColor = texture2D(normalMap, vUv * normalScale);\n        vec3 my_normal = normalize( vec3( normalColor.r * 2.0 - 1.0, normalColor.b,  normalColor.g * 2.0 - 1.0 ) );\n        vec3 coord = new_vUv.xyz / new_vUv.w;\n        normal_uv = coord.xy + coord.z * my_normal.xz * 0.05;\n        vec4 base_normal = texture2D(tDiffuse, normal_uv);\n        vec4 blur_normal = texture2D(tDiffuseBlur, normal_uv);\n        merge = base_normal;\n        blur = blur_normal;\n      #endif\n\n      float depthFactor = 0.0001;\n      float blurFactor = 0.0;\n\n      #ifdef USE_DEPTH\n        vec4 depth = texture2DProj(tDepth, new_vUv);\n        depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n        depthFactor *= depthScale;\n        depthFactor = max(0.0001, min(1.0, depthFactor));\n\n        #ifdef USE_BLUR\n          blur = blur * min(1.0, depthFactor + depthToBlurRatioBias);\n          merge = merge * min(1.0, depthFactor + 0.5);\n        #else\n          merge = merge * depthFactor;\n        #endif\n\n      #endif\n\n      float reflectorRoughnessFactor = roughness;\n      #ifdef USE_ROUGHNESSMAP\n        vec4 reflectorTexelRoughness = texture2D( roughnessMap, vUv );\n        reflectorRoughnessFactor *= reflectorTexelRoughness.g;\n      #endif\n\n      #ifdef USE_BLUR\n        blurFactor = min(1.0, mixBlur * reflectorRoughnessFactor);\n        merge = mix(merge, blur, blurFactor);\n      #endif\n\n      vec4 newMerge = vec4(0.0, 0.0, 0.0, 1.0);\n      newMerge.r = (merge.r - 0.5) * mixContrast + 0.5;\n      newMerge.g = (merge.g - 0.5) * mixContrast + 0.5;\n      newMerge.b = (merge.b - 0.5) * mixContrast + 0.5;\n\n      diffuseColor.rgb = diffuseColor.rgb * ((1.0 - min(1.0, mirror)) + newMerge.rgb * mixStrength);\n      ")}get tDiffuse(){return this._tDiffuse.value}set tDiffuse(e){this._tDiffuse.value=e}get tDepth(){return this._tDepth.value}set tDepth(e){this._tDepth.value=e}get distortionMap(){return this._distortionMap.value}set distortionMap(e){this._distortionMap.value=e}get tDiffuseBlur(){return this._tDiffuseBlur.value}set tDiffuseBlur(e){this._tDiffuseBlur.value=e}get textureMatrix(){return this._textureMatrix.value}set textureMatrix(e){this._textureMatrix.value=e}get hasBlur(){return this._hasBlur.value}set hasBlur(e){this._hasBlur.value=e}get mirror(){return this._mirror.value}set mirror(e){this._mirror.value=e}get mixBlur(){return this._mixBlur.value}set mixBlur(e){this._mixBlur.value=e}get mixStrength(){return this._blurStrength.value}set mixStrength(e){this._blurStrength.value=e}get minDepthThreshold(){return this._minDepthThreshold.value}set minDepthThreshold(e){this._minDepthThreshold.value=e}get maxDepthThreshold(){return this._maxDepthThreshold.value}set maxDepthThreshold(e){this._maxDepthThreshold.value=e}get depthScale(){return this._depthScale.value}set depthScale(e){this._depthScale.value=e}get depthToBlurRatioBias(){return this._depthToBlurRatioBias.value}set depthToBlurRatioBias(e){this._depthToBlurRatioBias.value=e}get distortion(){return this._distortion.value}set distortion(e){this._distortion.value=e}get mixContrast(){return this._mixContrast.value}set mixContrast(e){this._mixContrast.value=e}}const Kr=C.forwardRef((({mixBlur:e=0,mixStrength:t=1,resolution:r=256,blur:o=[0,0],minDepthThreshold:i=.9,maxDepthThreshold:s=1,depthScale:l=0,depthToBlurRatioBias:c=.25,mirror:u=0,distortion:d=1,mixContrast:m=1,distortionMap:f,reflectorOffset:p=0,...h},x)=>{a.extend({MeshReflectorMaterialImpl:Qr});const y=a.useThree((({gl:e})=>e)),v=a.useThree((({camera:e})=>e)),g=a.useThree((({scene:e})=>e)),w=(o=Array.isArray(o)?o:[o,o])[0]+o[1]>0,z=C.useRef(null);C.useImperativeHandle(x,(()=>z.current),[]);const[b]=C.useState((()=>new n.Plane)),[E]=C.useState((()=>new n.Vector3)),[M]=C.useState((()=>new n.Vector3)),[S]=C.useState((()=>new n.Vector3)),[P]=C.useState((()=>new n.Matrix4)),[R]=C.useState((()=>new n.Vector3(0,0,-1))),[D]=C.useState((()=>new n.Vector4)),[F]=C.useState((()=>new n.Vector3)),[k]=C.useState((()=>new n.Vector3)),[_]=C.useState((()=>new n.Vector4)),[A]=C.useState((()=>new n.Matrix4)),[L]=C.useState((()=>new n.PerspectiveCamera)),I=C.useCallback((()=>{var e;const t=z.current.parent||(null==(e=z.current)?void 0:e.__r3f.parent);if(!t)return;if(M.setFromMatrixPosition(t.matrixWorld),S.setFromMatrixPosition(v.matrixWorld),P.extractRotation(t.matrixWorld),E.set(0,0,1),E.applyMatrix4(P),M.addScaledVector(E,p),F.subVectors(M,S),F.dot(E)>0)return;F.reflect(E).negate(),F.add(M),P.extractRotation(v.matrixWorld),R.set(0,0,-1),R.applyMatrix4(P),R.add(S),k.subVectors(M,R),k.reflect(E).negate(),k.add(M),L.position.copy(F),L.up.set(0,1,0),L.up.applyMatrix4(P),L.up.reflect(E),L.lookAt(k),L.far=v.far,L.updateMatrixWorld(),L.projectionMatrix.copy(v.projectionMatrix),A.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),A.multiply(L.projectionMatrix),A.multiply(L.matrixWorldInverse),A.multiply(t.matrixWorld),b.setFromNormalAndCoplanarPoint(E,M),b.applyMatrix4(L.matrixWorldInverse),D.set(b.normal.x,b.normal.y,b.normal.z,b.constant);const r=L.projectionMatrix;_.x=(Math.sign(D.x)+r.elements[8])/r.elements[0],_.y=(Math.sign(D.y)+r.elements[9])/r.elements[5],_.z=-1,_.w=(1+r.elements[10])/r.elements[14],D.multiplyScalar(2/D.dot(_)),r.elements[2]=D.x,r.elements[6]=D.y,r.elements[10]=D.z+1,r.elements[14]=D.w}),[v,p]),[B,V,U,O]=C.useMemo((()=>{const a={minFilter:n.LinearFilter,magFilter:n.LinearFilter,type:n.HalfFloatType},p=new n.WebGLRenderTarget(r,r,a);p.depthBuffer=!0,p.depthTexture=new n.DepthTexture(r,r),p.depthTexture.format=n.DepthFormat,p.depthTexture.type=n.UnsignedShortType;const h=new n.WebGLRenderTarget(r,r,a);return[p,h,new Yr({gl:y,resolution:r,width:o[0],height:o[1],minDepthThreshold:i,maxDepthThreshold:s,depthScale:l,depthToBlurRatioBias:c}),{mirror:u,textureMatrix:A,mixBlur:e,tDiffuse:p.texture,tDepth:p.depthTexture,tDiffuseBlur:h.texture,hasBlur:w,mixStrength:t,minDepthThreshold:i,maxDepthThreshold:s,depthScale:l,depthToBlurRatioBias:c,distortion:d,distortionMap:f,mixContrast:m,"defines-USE_BLUR":w?"":void 0,"defines-USE_DEPTH":l>0?"":void 0,"defines-USE_DISTORTION":f?"":void 0}]}),[y,o,A,r,u,w,e,t,i,s,l,c,d,f,m]);return a.useFrame((()=>{var e;const t=z.current.parent||(null==(e=z.current)?void 0:e.__r3f.parent);if(!t)return;t.visible=!1;const r=y.xr.enabled,n=y.shadowMap.autoUpdate;I(),y.xr.enabled=!1,y.shadowMap.autoUpdate=!1,y.setRenderTarget(B),y.state.buffers.depth.setMask(!0),y.autoClear||y.clear(),y.render(g,L),w&&U.render(y,B,V),y.xr.enabled=r,y.shadowMap.autoUpdate=n,t.visible=!0,y.setRenderTarget(null)})),C.createElement("meshReflectorMaterialImpl",T.default({attach:"material",key:"key"+O["defines-USE_BLUR"]+O["defines-USE_DEPTH"]+O["defines-USE_DISTORTION"],ref:z},O,h))})),Jr=Te({envMap:null,bounces:3,ior:2.4,correctMips:!0,aberrationStrength:.01,fresnel:0,bvh:new w.MeshBVHUniformStruct,color:new R.Color("white"),opacity:1,resolution:new R.Vector2,viewMatrixInverse:new R.Matrix4,projectionMatrixInverse:new R.Matrix4},"\n  uniform mat4 viewMatrixInverse;\n\n  varying vec3 vWorldPosition;\n  varying vec3 vNormal;\n  varying mat4 vModelMatrixInverse;\n\n  #include <color_pars_vertex>\n\n  void main() {\n    #include <color_vertex>\n\n    vec4 transformedNormal = vec4(normal, 0.0);\n    vec4 transformedPosition = vec4(position, 1.0);\n    #ifdef USE_INSTANCING\n      transformedNormal = instanceMatrix * transformedNormal;\n      transformedPosition = instanceMatrix * transformedPosition;\n    #endif\n\n    #ifdef USE_INSTANCING\n      vModelMatrixInverse = inverse(modelMatrix * instanceMatrix);\n    #else\n      vModelMatrixInverse = inverse(modelMatrix);\n    #endif\n\n    vWorldPosition = (modelMatrix * transformedPosition).xyz;\n    vNormal = normalize((viewMatrixInverse * vec4(normalMatrix * transformedNormal.xyz, 0.0)).xyz);\n    gl_Position = projectionMatrix * viewMatrix * modelMatrix * transformedPosition;\n  }",`\n  #define ENVMAP_TYPE_CUBE_UV\n  precision highp isampler2D;\n  precision highp usampler2D;\n  varying vec3 vWorldPosition;\n  varying vec3 vNormal;\n  varying mat4 vModelMatrixInverse;\n\n  #include <color_pars_fragment>\n\n  #ifdef ENVMAP_TYPE_CUBEM\n    uniform samplerCube envMap;\n  #else\n    uniform sampler2D envMap;\n  #endif\n\n  uniform float bounces;\n  ${w.shaderStructs}\n  ${w.shaderIntersectFunction}\n  uniform BVH bvh;\n  uniform float ior;\n  uniform bool correctMips;\n  uniform vec2 resolution;\n  uniform float fresnel;\n  uniform mat4 modelMatrix;\n  uniform mat4 projectionMatrixInverse;\n  uniform mat4 viewMatrixInverse;\n  uniform float aberrationStrength;\n  uniform vec3 color;\n  uniform float opacity;\n\n  float fresnelFunc(vec3 viewDirection, vec3 worldNormal) {\n    return pow( 1.0 + dot( viewDirection, worldNormal), 10.0 );\n  }\n\n  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 normal, float ior, mat4 modelMatrixInverse) {\n    vec3 rayOrigin = ro;\n    vec3 rayDirection = rd;\n    rayDirection = refract(rayDirection, normal, 1.0 / ior);\n    rayOrigin = vWorldPosition + rayDirection * 0.001;\n    rayOrigin = (modelMatrixInverse * vec4(rayOrigin, 1.0)).xyz;\n    rayDirection = normalize((modelMatrixInverse * vec4(rayDirection, 0.0)).xyz);\n    for(float i = 0.0; i < bounces; i++) {\n      uvec4 faceIndices = uvec4( 0u );\n      vec3 faceNormal = vec3( 0.0, 0.0, 1.0 );\n      vec3 barycoord = vec3( 0.0 );\n      float side = 1.0;\n      float dist = 0.0;\n      bvhIntersectFirstHit( bvh, rayOrigin, rayDirection, faceIndices, faceNormal, barycoord, side, dist );\n      vec3 hitPos = rayOrigin + rayDirection * max(dist - 0.001, 0.0);\n      vec3 tempDir = refract(rayDirection, faceNormal, ior);\n      if (length(tempDir) != 0.0) {\n        rayDirection = tempDir;\n        break;\n      }\n      rayDirection = reflect(rayDirection, faceNormal);\n      rayOrigin = hitPos + rayDirection * 0.01;\n    }\n    rayDirection = normalize((modelMatrix * vec4(rayDirection, 0.0)).xyz);\n    return rayDirection;\n  }\n\n  #include <common>\n  #include <cube_uv_reflection_fragment>\n\n  #ifdef ENVMAP_TYPE_CUBEM\n    vec4 textureGradient(samplerCube envMap, vec3 rayDirection, vec3 directionCamPerfect) {\n      return textureGrad(envMap, rayDirection, dFdx(correctMips ? directionCamPerfect: rayDirection), dFdy(correctMips ? directionCamPerfect: rayDirection));\n    }\n  #else\n    vec4 textureGradient(sampler2D envMap, vec3 rayDirection, vec3 directionCamPerfect) {\n      vec2 uvv = equirectUv( rayDirection );\n      vec2 smoothUv = equirectUv( directionCamPerfect );\n      return textureGrad(envMap, uvv, dFdx(correctMips ? smoothUv : uvv), dFdy(correctMips ? smoothUv : uvv));\n    }\n  #endif\n\n  void main() {\n    vec2 uv = gl_FragCoord.xy / resolution;\n    vec3 directionCamPerfect = (projectionMatrixInverse * vec4(uv * 2.0 - 1.0, 0.0, 1.0)).xyz;\n    directionCamPerfect = (viewMatrixInverse * vec4(directionCamPerfect, 0.0)).xyz;\n    directionCamPerfect = normalize(directionCamPerfect);\n    vec3 normal = vNormal;\n    vec3 rayOrigin = cameraPosition;\n    vec3 rayDirection = normalize(vWorldPosition - cameraPosition);\n\n    vec4 diffuseColor = vec4(color, opacity);\n    #include <color_fragment>\n\n    #ifdef CHROMATIC_ABERRATIONS\n      vec3 rayDirectionG = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);\n      #ifdef FAST_CHROMA\n        vec3 rayDirectionR = normalize(rayDirectionG + 1.0 * vec3(aberrationStrength / 2.0));\n        vec3 rayDirectionB = normalize(rayDirectionG - 1.0 * vec3(aberrationStrength / 2.0));\n      #else\n        vec3 rayDirectionR = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 - aberrationStrength), 1.0), vModelMatrixInverse);\n        vec3 rayDirectionB = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 + aberrationStrength), 1.0), vModelMatrixInverse);\n      #endif\n      float finalColorR = textureGradient(envMap, rayDirectionR, directionCamPerfect).r;\n      float finalColorG = textureGradient(envMap, rayDirectionG, directionCamPerfect).g;\n      float finalColorB = textureGradient(envMap, rayDirectionB, directionCamPerfect).b;\n      diffuseColor.rgb *= vec3(finalColorR, finalColorG, finalColorB);\n    #else\n      rayDirection = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);\n      diffuseColor.rgb *= textureGradient(envMap, rayDirection, directionCamPerfect).rgb;\n    #endif\n\n    vec3 viewDirection = normalize(vWorldPosition - cameraPosition);\n    float nFresnel = fresnelFunc(viewDirection, normal) * fresnel;\n    gl_FragColor = vec4(mix(diffuseColor.rgb, vec3(1.0), nFresnel), diffuseColor.a);\n\n    #include <tonemapping_fragment>\n    #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n  }`);const en=Te({},"void main() { }","void main() { gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0); discard;  }");class tn extends R.MeshPhysicalMaterial{constructor(e=6,t=!1){super(),this.uniforms={chromaticAberration:{value:.05},transmission:{value:0},_transmission:{value:1},transmissionMap:{value:null},roughness:{value:0},thickness:{value:0},thicknessMap:{value:null},attenuationDistance:{value:1/0},attenuationColor:{value:new R.Color("white")},anisotropicBlur:{value:.1},time:{value:0},distortion:{value:0},distortionScale:{value:.5},temporalDistortion:{value:0},buffer:{value:null}},this.onBeforeCompile=r=>{r.uniforms={...r.uniforms,...this.uniforms},this.anisotropy>0&&(r.defines.USE_ANISOTROPY=""),t?r.defines.USE_SAMPLER="":r.defines.USE_TRANSMISSION="",r.fragmentShader="\n      uniform float chromaticAberration;         \n      uniform float anisotropicBlur;      \n      uniform float time;\n      uniform float distortion;\n      uniform float distortionScale;\n      uniform float temporalDistortion;\n      uniform sampler2D buffer;\n\n      vec3 random3(vec3 c) {\n        float j = 4096.0*sin(dot(c,vec3(17.0, 59.4, 15.0)));\n        vec3 r;\n        r.z = fract(512.0*j);\n        j *= .125;\n        r.x = fract(512.0*j);\n        j *= .125;\n        r.y = fract(512.0*j);\n        return r-0.5;\n      }\n\n      uint hash( uint x ) {\n        x += ( x << 10u );\n        x ^= ( x >>  6u );\n        x += ( x <<  3u );\n        x ^= ( x >> 11u );\n        x += ( x << 15u );\n        return x;\n      }\n\n      // Compound versions of the hashing algorithm I whipped together.\n      uint hash( uvec2 v ) { return hash( v.x ^ hash(v.y)                         ); }\n      uint hash( uvec3 v ) { return hash( v.x ^ hash(v.y) ^ hash(v.z)             ); }\n      uint hash( uvec4 v ) { return hash( v.x ^ hash(v.y) ^ hash(v.z) ^ hash(v.w) ); }\n\n      // Construct a float with half-open range [0:1] using low 23 bits.\n      // All zeroes yields 0.0, all ones yields the next smallest representable value below 1.0.\n      float floatConstruct( uint m ) {\n        const uint ieeeMantissa = 0x007FFFFFu; // binary32 mantissa bitmask\n        const uint ieeeOne      = 0x3F800000u; // 1.0 in IEEE binary32\n        m &= ieeeMantissa;                     // Keep only mantissa bits (fractional part)\n        m |= ieeeOne;                          // Add fractional part to 1.0\n        float  f = uintBitsToFloat( m );       // Range [1:2]\n        return f - 1.0;                        // Range [0:1]\n      }\n\n      // Pseudo-random value in half-open range [0:1].\n      float randomBase( float x ) { return floatConstruct(hash(floatBitsToUint(x))); }\n      float randomBase( vec2  v ) { return floatConstruct(hash(floatBitsToUint(v))); }\n      float randomBase( vec3  v ) { return floatConstruct(hash(floatBitsToUint(v))); }\n      float randomBase( vec4  v ) { return floatConstruct(hash(floatBitsToUint(v))); }\n      float rand(float seed) {\n        float result = randomBase(vec3(gl_FragCoord.xy, seed));\n        return result;\n      }\n\n      const float F3 =  0.3333333;\n      const float G3 =  0.1666667;\n\n      float snoise(vec3 p) {\n        vec3 s = floor(p + dot(p, vec3(F3)));\n        vec3 x = p - s + dot(s, vec3(G3));\n        vec3 e = step(vec3(0.0), x - x.yzx);\n        vec3 i1 = e*(1.0 - e.zxy);\n        vec3 i2 = 1.0 - e.zxy*(1.0 - e);\n        vec3 x1 = x - i1 + G3;\n        vec3 x2 = x - i2 + 2.0*G3;\n        vec3 x3 = x - 1.0 + 3.0*G3;\n        vec4 w, d;\n        w.x = dot(x, x);\n        w.y = dot(x1, x1);\n        w.z = dot(x2, x2);\n        w.w = dot(x3, x3);\n        w = max(0.6 - w, 0.0);\n        d.x = dot(random3(s), x);\n        d.y = dot(random3(s + i1), x1);\n        d.z = dot(random3(s + i2), x2);\n        d.w = dot(random3(s + 1.0), x3);\n        w *= w;\n        w *= w;\n        d *= w;\n        return dot(d, vec4(52.0));\n      }\n\n      float snoiseFractal(vec3 m) {\n        return 0.5333333* snoise(m)\n              +0.2666667* snoise(2.0*m)\n              +0.1333333* snoise(4.0*m)\n              +0.0666667* snoise(8.0*m);\n      }\n"+r.fragmentShader,r.fragmentShader=r.fragmentShader.replace("#include <transmission_pars_fragment>","\n        #ifdef USE_TRANSMISSION\n          // Transmission code is based on glTF-Sampler-Viewer\n          // https://github.com/KhronosGroup/glTF-Sample-Viewer\n          uniform float _transmission;\n          uniform float thickness;\n          uniform float attenuationDistance;\n          uniform vec3 attenuationColor;\n          #ifdef USE_TRANSMISSIONMAP\n            uniform sampler2D transmissionMap;\n          #endif\n          #ifdef USE_THICKNESSMAP\n            uniform sampler2D thicknessMap;\n          #endif\n          uniform vec2 transmissionSamplerSize;\n          uniform sampler2D transmissionSamplerMap;\n          uniform mat4 modelMatrix;\n          uniform mat4 projectionMatrix;\n          varying vec3 vWorldPosition;\n          vec3 getVolumeTransmissionRay( const in vec3 n, const in vec3 v, const in float thickness, const in float ior, const in mat4 modelMatrix ) {\n            // Direction of refracted light.\n            vec3 refractionVector = refract( - v, normalize( n ), 1.0 / ior );\n            // Compute rotation-independant scaling of the model matrix.\n            vec3 modelScale;\n            modelScale.x = length( vec3( modelMatrix[ 0 ].xyz ) );\n            modelScale.y = length( vec3( modelMatrix[ 1 ].xyz ) );\n            modelScale.z = length( vec3( modelMatrix[ 2 ].xyz ) );\n            // The thickness is specified in local space.\n            return normalize( refractionVector ) * thickness * modelScale;\n          }\n          float applyIorToRoughness( const in float roughness, const in float ior ) {\n            // Scale roughness with IOR so that an IOR of 1.0 results in no microfacet refraction and\n            // an IOR of 1.5 results in the default amount of microfacet refraction.\n            return roughness * clamp( ior * 2.0 - 2.0, 0.0, 1.0 );\n          }\n          vec4 getTransmissionSample( const in vec2 fragCoord, const in float roughness, const in float ior ) {\n            float framebufferLod = log2( transmissionSamplerSize.x ) * applyIorToRoughness( roughness, ior );            \n            #ifdef USE_SAMPLER\n              #ifdef texture2DLodEXT\n                return texture2DLodEXT(transmissionSamplerMap, fragCoord.xy, framebufferLod);\n              #else\n                return texture2D(transmissionSamplerMap, fragCoord.xy, framebufferLod);\n              #endif\n            #else\n              return texture2D(buffer, fragCoord.xy);\n            #endif\n          }\n          vec3 applyVolumeAttenuation( const in vec3 radiance, const in float transmissionDistance, const in vec3 attenuationColor, const in float attenuationDistance ) {\n            if ( isinf( attenuationDistance ) ) {\n              // Attenuation distance is +∞, i.e. the transmitted color is not attenuated at all.\n              return radiance;\n            } else {\n              // Compute light attenuation using Beer's law.\n              vec3 attenuationCoefficient = -log( attenuationColor ) / attenuationDistance;\n              vec3 transmittance = exp( - attenuationCoefficient * transmissionDistance ); // Beer's law\n              return transmittance * radiance;\n            }\n          }\n          vec4 getIBLVolumeRefraction( const in vec3 n, const in vec3 v, const in float roughness, const in vec3 diffuseColor,\n            const in vec3 specularColor, const in float specularF90, const in vec3 position, const in mat4 modelMatrix,\n            const in mat4 viewMatrix, const in mat4 projMatrix, const in float ior, const in float thickness,\n            const in vec3 attenuationColor, const in float attenuationDistance ) {\n            vec3 transmissionRay = getVolumeTransmissionRay( n, v, thickness, ior, modelMatrix );\n            vec3 refractedRayExit = position + transmissionRay;\n            // Project refracted vector on the framebuffer, while mapping to normalized device coordinates.\n            vec4 ndcPos = projMatrix * viewMatrix * vec4( refractedRayExit, 1.0 );\n            vec2 refractionCoords = ndcPos.xy / ndcPos.w;\n            refractionCoords += 1.0;\n            refractionCoords /= 2.0;\n            // Sample framebuffer to get pixel the refracted ray hits.\n            vec4 transmittedLight = getTransmissionSample( refractionCoords, roughness, ior );\n            vec3 attenuatedColor = applyVolumeAttenuation( transmittedLight.rgb, length( transmissionRay ), attenuationColor, attenuationDistance );\n            // Get the specular component.\n            vec3 F = EnvironmentBRDF( n, v, specularColor, specularF90, roughness );\n            return vec4( ( 1.0 - F ) * attenuatedColor * diffuseColor, transmittedLight.a );\n          }\n        #endif\n"),r.fragmentShader=r.fragmentShader.replace("#include <transmission_fragment>",`  \n        // Improve the refraction to use the world pos\n        material.transmission = _transmission;\n        material.transmissionAlpha = 1.0;\n        material.thickness = thickness;\n        material.attenuationDistance = attenuationDistance;\n        material.attenuationColor = attenuationColor;\n        #ifdef USE_TRANSMISSIONMAP\n          material.transmission *= texture2D( transmissionMap, vUv ).r;\n        #endif\n        #ifdef USE_THICKNESSMAP\n          material.thickness *= texture2D( thicknessMap, vUv ).g;\n        #endif\n        \n        vec3 pos = vWorldPosition;\n        float runningSeed = 0.0;\n        vec3 v = normalize( cameraPosition - pos );\n        vec3 n = inverseTransformDirection( normal, viewMatrix );\n        vec3 transmission = vec3(0.0);\n        float transmissionR, transmissionB, transmissionG;\n        float randomCoords = rand(runningSeed++);\n        float thickness_smear = thickness * max(pow(roughnessFactor, 0.33), anisotropicBlur);\n        vec3 distortionNormal = vec3(0.0);\n        vec3 temporalOffset = vec3(time, -time, -time) * temporalDistortion;\n        if (distortion > 0.0) {\n          distortionNormal = distortion * vec3(snoiseFractal(vec3((pos * distortionScale + temporalOffset))), snoiseFractal(vec3(pos.zxy * distortionScale - temporalOffset)), snoiseFractal(vec3(pos.yxz * distortionScale + temporalOffset)));\n        }\n        for (float i = 0.0; i < ${e}.0; i ++) {\n          vec3 sampleNorm = normalize(n + roughnessFactor * roughnessFactor * 2.0 * normalize(vec3(rand(runningSeed++) - 0.5, rand(runningSeed++) - 0.5, rand(runningSeed++) - 0.5)) * pow(rand(runningSeed++), 0.33) + distortionNormal);\n          transmissionR = getIBLVolumeRefraction(\n            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior, material.thickness  + thickness_smear * (i + randomCoords) / float(${e}),\n            material.attenuationColor, material.attenuationDistance\n          ).r;\n          transmissionG = getIBLVolumeRefraction(\n            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior  * (1.0 + chromaticAberration * (i + randomCoords) / float(${e})) , material.thickness + thickness_smear * (i + randomCoords) / float(${e}),\n            material.attenuationColor, material.attenuationDistance\n          ).g;\n          transmissionB = getIBLVolumeRefraction(\n            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,\n            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior * (1.0 + 2.0 * chromaticAberration * (i + randomCoords) / float(${e})), material.thickness + thickness_smear * (i + randomCoords) / float(${e}),\n            material.attenuationColor, material.attenuationDistance\n          ).b;\n          transmission.r += transmissionR;\n          transmission.g += transmissionG;\n          transmission.b += transmissionB;\n        }\n        transmission /= ${e}.0;\n        totalDiffuse = mix( totalDiffuse, transmission.rgb, material.transmission );\n`)},Object.keys(this.uniforms).forEach((e=>Object.defineProperty(this,e,{get:()=>this.uniforms[e].value,set:t=>this.uniforms[e].value=t})))}}const rn=C.forwardRef((({buffer:e,transmissionSampler:t=!1,backside:r=!1,side:n=R.FrontSide,transmission:o=1,thickness:i=0,backsideThickness:s=0,backsideEnvMapIntensity:l=1,samples:c=10,resolution:u,backsideResolution:d,background:m,anisotropy:f,anisotropicBlur:p,...h},x)=>{a.extend({MeshTransmissionMaterial:tn});const y=C.useRef(null),[v]=C.useState((()=>new en)),g=ot(d||u),w=ot(u);let z,b,E,M;return a.useFrame((e=>{y.current.time=e.clock.elapsedTime,y.current.buffer!==w.texture||t||(M=y.current.__r3f.parent,M&&(E=e.gl.toneMapping,z=e.scene.background,b=y.current.envMapIntensity,e.gl.toneMapping=R.NoToneMapping,m&&(e.scene.background=m),M.material=v,r&&(e.gl.setRenderTarget(g),e.gl.render(e.scene,e.camera),M.material=y.current,M.material.buffer=g.texture,M.material.thickness=s,M.material.side=R.BackSide,M.material.envMapIntensity=l),e.gl.setRenderTarget(w),e.gl.render(e.scene,e.camera),M.material=y.current,M.material.thickness=i,M.material.side=n,M.material.buffer=w.texture,M.material.envMapIntensity=b,e.scene.background=z,e.gl.setRenderTarget(null),e.gl.toneMapping=E))})),C.useImperativeHandle(x,(()=>y.current),[]),C.createElement("meshTransmissionMaterial",T.default({args:[c,t],ref:y},h,{buffer:e||w.texture,_transmission:o,anisotropicBlur:null!=p?p:f,transmission:t?o:0,thickness:i,side:n}))})),nn=C.forwardRef(((e,t)=>(a.extend({DiscardMaterialImpl:en}),C.createElement("discardMaterialImpl",T.default({ref:t},e)))));const an=Re>=154?"opaque_fragment":"output_fragment";class on extends R.PointsMaterial{constructor(e){super(e),this.onBeforeCompile=(e,t)=>{const{isWebGL2:r}=t.capabilities;e.fragmentShader=e.fragmentShader.replace(`#include <${an}>`,`\n        ${r?`#include <${an}>`:`#extension GL_OES_standard_derivatives : enable\n#include <${an}>`}\n      vec2 cxy = 2.0 * gl_PointCoord - 1.0;\n      float r = dot(cxy, cxy);\n      float delta = fwidth(r);     \n      float mask = 1.0 - smoothstep(1.0 - delta, 1.0 + delta, r);\n      gl_FragColor = vec4(gl_FragColor.rgb, mask * gl_FragColor.a );\n      #include <tonemapping_fragment>\n      #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n      `)}}}const sn=C.forwardRef(((e,t)=>{const[r]=C.useState((()=>new on(null)));return C.createElement("primitive",T.default({},e,{object:r,ref:t,attach:"material"}))}));function ln(e,t,r){t.traverse((t=>{t.material&&(e.properties.remove(t.material),null==t.material.dispose||t.material.dispose())})),e.info.programs.length=0,e.compile(t,r)}function cn(e,t){const r=e+"Geometry";return C.forwardRef((({args:e,children:n,...a},o)=>{const i=C.useRef(null);return C.useImperativeHandle(o,(()=>i.current)),C.useLayoutEffect((()=>{null==t||t(i.current)})),C.createElement("mesh",T.default({ref:i},a),C.createElement(r,{attach:"geometry",args:e}),n)}))}const un=cn("box"),dn=cn("circle"),mn=cn("cone"),fn=cn("cylinder"),pn=cn("sphere"),hn=cn("plane"),xn=cn("tube"),yn=cn("torus"),vn=cn("torusKnot"),gn=cn("tetrahedron"),wn=cn("ring"),zn=cn("polyhedron"),bn=cn("icosahedron"),En=cn("octahedron"),Mn=cn("dodecahedron"),Sn=cn("extrude"),Tn=cn("lathe"),Cn=cn("capsule"),Pn=cn("shape",(({geometry:e})=>{const t=e.attributes.position,r=(new R.Box3).setFromBufferAttribute(t),n=new R.Vector3;r.getSize(n);const a=[];let o=0,i=0,s=0,l=0;for(let e=0;e<t.count;e++)o=t.getX(e),i=t.getY(e),s=(o-r.min.x)/n.x,l=(i-r.min.y)/n.y,a.push(s,l);e.setAttribute("uv",new R.Float32BufferAttribute(a,2))})),Rn=1e-5;const Dn=C.forwardRef((function({args:[e=1,t=1,r=1]=[],radius:a=.05,steps:o=1,smoothness:i=4,bevelSegments:s=4,creaseAngle:l=.4,children:c,...d},m){const f=C.useMemo((()=>function(e,t,r){const a=new n.Shape,o=r-Rn;return a.absarc(Rn,Rn,Rn,-Math.PI/2,-Math.PI,!0),a.absarc(Rn,t-2*o,Rn,Math.PI,Math.PI/2,!0),a.absarc(e-2*o,t-2*o,Rn,Math.PI/2,0,!0),a.absarc(e-2*o,Rn,Rn,0,-Math.PI/2,!0),a}(e,t,a)),[e,t,a]),p=C.useMemo((()=>({depth:r-2*a,bevelEnabled:!0,bevelSegments:2*s,steps:o,bevelSize:a-Rn,bevelThickness:a,curveSegments:i})),[r,a,i]),h=C.useRef(null);return C.useLayoutEffect((()=>{h.current&&(h.current.center(),u.toCreasedNormals(h.current,l))}),[f,p]),C.createElement("mesh",T.default({ref:m},d),C.createElement("extrudeGeometry",{ref:h,args:[f,p]}),c)}));function Fn(){const e=new R.BufferGeometry,t=new Float32Array([-1,-1,3,-1,-1,3]);return e.boundingSphere=new R.Sphere,e.boundingSphere.set(new R.Vector3,1/0),e.setAttribute("position",new R.BufferAttribute(t,2)),e}const kn=C.forwardRef((function({children:e,...t},r){const n=C.useMemo(Fn,[]);return C.createElement("mesh",T.default({ref:r,geometry:n,frustumCulled:!1},t),e)})),_n=C.forwardRef((({children:e,width:t,height:r,depth:n,box3:a,precise:o=!0,...i},s)=>{const l=C.useRef(null),c=C.useRef(null),u=C.useRef(null);return C.useLayoutEffect((()=>{c.current.matrixWorld.identity();let e=a||(new R.Box3).setFromObject(u.current,o);const i=e.max.x-e.min.x,s=e.max.y-e.min.y,l=e.max.z-e.min.z;let d=Math.max(i,s,l);t&&(d=i),r&&(d=s),n&&(d=l),c.current.scale.setScalar(1/d)}),[t,r,n,a,o]),C.useImperativeHandle(s,(()=>l.current),[]),C.createElement("group",T.default({ref:l},i),C.createElement("group",{ref:c},C.createElement("group",{ref:u},e)))}));var An=function(e){return e[e.NONE=0]="NONE",e[e.START=1]="START",e[e.ACTIVE=2]="ACTIVE",e}(An||{});const Ln=e=>e&&e.isOrthographicCamera,In=e=>1-Math.exp(-5*e)+.007*e,Bn=C.createContext(null);function Vn({children:e,maxDuration:t=1,margin:r=1.2,observe:n,fit:o,clip:i,interpolateFunc:s=In,onFit:l}){const c=C.useRef(null),{camera:u,size:d,invalidate:m}=a.useThree(),f=a.useThree((e=>e.controls)),p=C.useRef(l);p.current=l;const h=C.useRef({camPos:new R.Vector3,camRot:new R.Quaternion,camZoom:1}),x=C.useRef({camPos:void 0,camRot:void 0,camZoom:void 0,camUp:void 0,target:void 0}),y=C.useRef(An.NONE),v=C.useRef(0),[g]=C.useState((()=>new R.Box3)),w=C.useMemo((()=>{function e(){const e=g.getSize(new R.Vector3),t=g.getCenter(new R.Vector3),n=Math.max(e.x,e.y,e.z),a=Ln(u)?4*n:n/(2*Math.atan(Math.PI*u.fov/360)),o=Ln(u)?4*n:a/u.aspect,i=r*Math.max(a,o);return{box:g,size:e,center:t,distance:i}}return{getSize:e,refresh(e){if((t=e)&&t.isBox3)g.copy(e);else{const t=e||c.current;if(!t)return this;t.updateWorldMatrix(!0,!0),g.setFromObject(t)}var t;if(g.isEmpty()){const e=u.position.length()||10;g.setFromCenterAndSize(new R.Vector3,new R.Vector3(e,e,e))}return h.current.camPos.copy(u.position),h.current.camRot.copy(u.quaternion),Ln(u)&&(h.current.camZoom=u.zoom),x.current.camPos=void 0,x.current.camRot=void 0,x.current.camZoom=void 0,x.current.camUp=void 0,x.current.target=void 0,this},reset(){const{center:t,distance:r}=e(),n=u.position.clone().sub(t).normalize();x.current.camPos=t.clone().addScaledVector(n,r),x.current.target=t.clone();const a=(new R.Matrix4).lookAt(x.current.camPos,x.current.target,u.up);return x.current.camRot=(new R.Quaternion).setFromRotationMatrix(a),y.current=An.START,v.current=0,this},moveTo(e){return x.current.camPos=Array.isArray(e)?new R.Vector3(...e):e.clone(),y.current=An.START,v.current=0,this},lookAt({target:e,up:t}){x.current.target=Array.isArray(e)?new R.Vector3(...e):e.clone(),x.current.camUp=t?Array.isArray(t)?new R.Vector3(...t):t.clone():u.up.clone();const r=(new R.Matrix4).lookAt(x.current.camPos||u.position,x.current.target,x.current.camUp);return x.current.camRot=(new R.Quaternion).setFromRotationMatrix(r),y.current=An.START,v.current=0,this},to({position:e,target:t}){return this.moveTo(e).lookAt({target:t})},fit(){if(!Ln(u))return this.reset();let e=0,t=0;const n=[new R.Vector3(g.min.x,g.min.y,g.min.z),new R.Vector3(g.min.x,g.max.y,g.min.z),new R.Vector3(g.min.x,g.min.y,g.max.z),new R.Vector3(g.min.x,g.max.y,g.max.z),new R.Vector3(g.max.x,g.max.y,g.max.z),new R.Vector3(g.max.x,g.max.y,g.min.z),new R.Vector3(g.max.x,g.min.y,g.max.z),new R.Vector3(g.max.x,g.min.y,g.min.z)],a=x.current.camPos||u.position,o=x.current.target||(null==f?void 0:f.target),i=x.current.camUp||u.up,s=o?(new R.Matrix4).lookAt(a,o,i).setPosition(a).invert():u.matrixWorldInverse;for(const r of n)r.applyMatrix4(s),e=Math.max(e,Math.abs(r.y)),t=Math.max(t,Math.abs(r.x));e*=2,t*=2;const l=(u.top-u.bottom)/e,c=(u.right-u.left)/t;return x.current.camZoom=Math.min(l,c)/r,y.current=An.START,v.current=0,p.current&&p.current(this.getSize()),this},clip(){const{distance:t}=e();return u.near=t/100,u.far=100*t,u.updateProjectionMatrix(),f&&(f.maxDistance=10*t,f.update()),m(),this}}}),[g,u,f,r,m]);C.useLayoutEffect((()=>{if(f){const e=()=>{if(f&&x.current.target&&y.current!==An.NONE){const e=(new R.Vector3).setFromMatrixColumn(u.matrix,2),t=h.current.camPos.distanceTo(f.target),r=(x.current.camPos||h.current.camPos).distanceTo(x.current.target),n=(1-v.current)*t+v.current*r;f.target.copy(u.position).addScaledVector(e,-n),f.update()}y.current=An.NONE};return f.addEventListener("start",e),()=>f.removeEventListener("start",e)}}),[f]);const z=C.useRef(0);return C.useLayoutEffect((()=>{(n||0==z.current++)&&(w.refresh(),o&&w.reset().fit(),i&&w.clip())}),[d,i,o,n,u,f]),a.useFrame(((e,r)=>{if(y.current===An.START)y.current=An.ACTIVE,m();else if(y.current===An.ACTIVE){if(v.current+=r/t,v.current>=1)x.current.camPos&&u.position.copy(x.current.camPos),x.current.camRot&&u.quaternion.copy(x.current.camRot),x.current.camUp&&u.up.copy(x.current.camUp),x.current.camZoom&&Ln(u)&&(u.zoom=x.current.camZoom),u.updateMatrixWorld(),u.updateProjectionMatrix(),f&&x.current.target&&(f.target.copy(x.current.target),f.update()),y.current=An.NONE;else{const e=s(v.current);x.current.camPos&&u.position.lerpVectors(h.current.camPos,x.current.camPos,e),x.current.camRot&&u.quaternion.slerpQuaternions(h.current.camRot,x.current.camRot,e),x.current.camUp&&u.up.set(0,1,0).applyQuaternion(u.quaternion),x.current.camZoom&&Ln(u)&&(u.zoom=(1-e)*h.current.camZoom+e*x.current.camZoom),u.updateMatrixWorld(),u.updateProjectionMatrix()}m()}})),C.createElement("group",{ref:c},C.createElement(Bn.Provider,{value:w},e))}function Un(){return C.useContext(Bn)}const On=C.forwardRef((({intensity:e=1,decay:t,decayRate:r=.65,maxYaw:n=.1,maxPitch:o=.1,maxRoll:i=.1,yawFrequency:s=.1,pitchFrequency:l=.1,rollFrequency:c=.1},d)=>{const m=a.useThree((e=>e.camera)),f=a.useThree((e=>e.controls)),p=C.useRef(e),h=C.useRef(m.rotation.clone()),[x]=C.useState((()=>new u.SimplexNoise)),[y]=C.useState((()=>new u.SimplexNoise)),[v]=C.useState((()=>new u.SimplexNoise)),g=()=>{(p.current<0||p.current>1)&&(p.current=p.current<0?0:1)};return C.useImperativeHandle(d,(()=>({getIntensity:()=>p.current,setIntensity:e=>{p.current=e,g()}})),[]),C.useEffect((()=>{if(f){const e=()=>{h.current=m.rotation.clone()};return f.addEventListener("change",e),e(),()=>{f.removeEventListener("change",e)}}}),[m,f]),a.useFrame(((e,a)=>{const u=Math.pow(p.current,2),d=n*u*x.noise(e.clock.elapsedTime*s,1),f=o*u*y.noise(e.clock.elapsedTime*l,1),w=i*u*v.noise(e.clock.elapsedTime*c,1);m.rotation.set(h.current.x+f,h.current.y+d,h.current.z+w),t&&p.current>0&&(p.current-=r*a,g())})),null})),Nn=C.forwardRef((({children:e,enabled:t=!0,speed:r=1,rotationIntensity:n=1,floatIntensity:o=1,floatingRange:i=[-.1,.1],autoInvalidate:s=!1,...l},c)=>{const u=C.useRef(null);C.useImperativeHandle(c,(()=>u.current),[]);const d=C.useRef(1e4*Math.random());return a.useFrame((e=>{var a,l;if(!t||0===r)return;s&&e.invalidate();const c=d.current+e.clock.elapsedTime;u.current.rotation.x=Math.cos(c/4*r)/8*n,u.current.rotation.y=Math.sin(c/4*r)/8*n,u.current.rotation.z=Math.sin(c/4*r)/20*n;let m=Math.sin(c/4*r)/10;m=R.MathUtils.mapLinear(m,-.1,.1,null!==(a=null==i?void 0:i[0])&&void 0!==a?a:-.1,null!==(l=null==i?void 0:i[1])&&void 0!==l?l:.1),u.current.position.y=m*o,u.current.updateMatrix()})),C.createElement("group",l,C.createElement("group",{ref:u,matrixAutoUpdate:!1},e))})),jn={apartment:"lebombo_1k.hdr",city:"potsdamer_platz_1k.hdr",dawn:"kiara_1_dawn_1k.hdr",forest:"forest_slope_1k.hdr",lobby:"st_fagans_interior_1k.hdr",night:"dikhololo_night_1k.hdr",park:"rooitou_park_1k.hdr",studio:"studio_small_03_1k.hdr",sunset:"venice_sunset_1k.hdr",warehouse:"empty_warehouse_01_1k.hdr"},Wn="https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/",Gn=e=>Array.isArray(e),Hn=["/px.png","/nx.png","/py.png","/ny.png","/pz.png","/nz.png"];function $n({files:e=Hn,path:r="",preset:o,encoding:i,extensions:s}={}){let l=null,c=!1;o&&(Zn(o),e=jn[o],r=Wn),c=Gn(e);const{extension:u,isCubemap:d}=Yn(e);if(l=Qn(u),!l)throw new Error("useEnvironment: Unrecognized file extension: "+e);const m=a.useThree((e=>e.gl));t.useLayoutEffect((()=>{"webp"!==u&&"jpg"!==u&&"jpeg"!==u||m.domElement.addEventListener("webglcontextlost",(function(){a.useLoader.clear(l,c?[e]:e)}),{once:!0})}),[e,m.domElement]);const f=a.useLoader(l,c?[e]:e,(e=>{"webp"!==u&&"jpg"!==u&&"jpeg"!==u||e.setRenderer(m),null==e.setPath||e.setPath(r),s&&s(e)}));let p=c?f[0]:f;var h;"jpg"!==u&&"jpeg"!==u&&"webp"!==u||(p=null==(h=p.renderTarget)?void 0:h.texture);return p.mapping=d?n.CubeReflectionMapping:n.EquirectangularReflectionMapping,"colorSpace"in p?p.colorSpace=(null!=i?i:d)?"srgb":"srgb-linear":p.encoding=(null!=i?i:d)?3001:3e3,p}const qn={files:Hn,path:"",preset:void 0,extensions:void 0};$n.preload=e=>{const t={...qn,...e};let{files:r,path:n=""}=t;const{preset:o,extensions:i}=t;o&&(Zn(o),r=jn[o],n=Wn);const{extension:s}=Yn(r);if("webp"===s||"jpg"===s||"jpeg"===s)throw new Error("useEnvironment: Preloading gainmaps is not supported");const l=Qn(s);if(!l)throw new Error("useEnvironment: Unrecognized file extension: "+r);a.useLoader.preload(l,Gn(r)?[r]:r,(e=>{null==e.setPath||e.setPath(n),i&&i(e)}))};const Xn={files:Hn,preset:void 0};function Zn(e){if(!(e in jn))throw new Error("Preset must be one of: "+Object.keys(jn).join(", "))}function Yn(e){var t;const r=Gn(e)&&6===e.length,n=Gn(e)&&3===e.length&&e.some((e=>e.endsWith("json"))),a=Gn(e)?e[0]:e;return{extension:r?"cube":n?"webp":a.startsWith("data:application/exr")?"exr":a.startsWith("data:application/hdr")?"hdr":a.startsWith("data:image/jpeg")?"jpg":null==(t=a.split(".").pop())||null==(t=t.split("?"))||null==(t=t.shift())?void 0:t.toLowerCase(),isCubemap:r,isGainmap:n}}function Qn(e){return"cube"===e?n.CubeTextureLoader:"hdr"===e?u.RGBELoader:"exr"===e?u.EXRLoader:"jpg"===e||"jpeg"===e?b.HDRJPGLoader:"webp"===e?b.GainMapLoader:null}$n.clear=e=>{const t={...Xn,...e};let{files:r}=t;const{preset:n}=t;n&&(Zn(n),r=jn[n]);const{extension:o}=Yn(r),i=Qn(o);if(!i)throw new Error("useEnvironment: Unrecognized file extension: "+r);a.useLoader.clear(i,Gn(r)?[r]:r)};function Kn(e,t,r,n,o={}){var i,s,l,c;o={backgroundBlurriness:0,backgroundIntensity:1,backgroundRotation:[0,0,0],environmentIntensity:1,environmentRotation:[0,0,0],...o};const u=(e=>{return(t=e).current&&t.current.isScene?e.current:e;var t})(t||r),d=u.background,m=u.environment,f={backgroundBlurriness:u.backgroundBlurriness,backgroundIntensity:u.backgroundIntensity,backgroundRotation:null!==(i=null==(s=u.backgroundRotation)||null==s.clone?void 0:s.clone())&&void 0!==i?i:[0,0,0],environmentIntensity:u.environmentIntensity,environmentRotation:null!==(l=null==(c=u.environmentRotation)||null==c.clone?void 0:c.clone())&&void 0!==l?l:[0,0,0]};return"only"!==e&&(u.environment=n),e&&(u.background=n),a.applyProps(u,o),()=>{"only"!==e&&(u.environment=m),e&&(u.background=d),a.applyProps(u,f)}}function Jn({scene:e,background:t=!1,map:r,...n}){const o=a.useThree((e=>e.scene));return C.useLayoutEffect((()=>{if(r)return Kn(t,e,o,r,n)})),null}function ea({background:e=!1,scene:t,blur:r,backgroundBlurriness:n,backgroundIntensity:o,backgroundRotation:i,environmentIntensity:s,environmentRotation:l,...c}){const u=$n(c),d=a.useThree((e=>e.scene));return C.useLayoutEffect((()=>Kn(e,t,d,u,{backgroundBlurriness:null!=r?r:n,backgroundIntensity:o,backgroundRotation:i,environmentIntensity:s,environmentRotation:l}))),C.useEffect((()=>()=>{u.dispose()}),[u]),null}function ta({children:e,near:t=.1,far:r=1e3,resolution:o=256,frames:i=1,map:s,background:l=!1,blur:c,backgroundBlurriness:u,backgroundIntensity:d,backgroundRotation:m,environmentIntensity:f,environmentRotation:p,scene:h,files:x,path:y,preset:v,extensions:g}){const w=a.useThree((e=>e.gl)),z=a.useThree((e=>e.scene)),b=C.useRef(null),[E]=C.useState((()=>new n.Scene)),M=C.useMemo((()=>{const e=new n.WebGLCubeRenderTarget(o);return e.texture.type=n.HalfFloatType,e}),[o]);C.useEffect((()=>()=>{M.dispose()}),[M]),C.useLayoutEffect((()=>{if(1===i){const e=w.autoClear;w.autoClear=!0,b.current.update(w,E),w.autoClear=e}return Kn(l,h,z,M.texture,{backgroundBlurriness:null!=c?c:u,backgroundIntensity:d,backgroundRotation:m,environmentIntensity:f,environmentRotation:p})}),[e,E,M.texture,h,z,l,i,w]);let S=1;return a.useFrame((()=>{if(i===1/0||S<i){const e=w.autoClear;w.autoClear=!0,b.current.update(w,E),w.autoClear=e,S++}})),C.createElement(C.Fragment,null,a.createPortal(C.createElement(C.Fragment,null,e,C.createElement("cubeCamera",{ref:b,args:[t,r,M]}),x||v?C.createElement(ea,{background:!0,files:x,preset:v,path:y,extensions:g}):s?C.createElement(Jn,{background:!0,map:s,extensions:g}):null),E))}function ra(e){var t,r,n,o;const i=$n(e),s=e.map||i;C.useMemo((()=>a.extend({GroundProjectedEnvImpl:u.GroundProjectedEnv})),[]),C.useEffect((()=>()=>{i.dispose()}),[i]);const l=C.useMemo((()=>[s]),[s]),c=null==(t=e.ground)?void 0:t.height,d=null==(r=e.ground)?void 0:r.radius,m=null!==(n=null==(o=e.ground)?void 0:o.scale)&&void 0!==n?n:1e3;return C.createElement(C.Fragment,null,C.createElement(Jn,T.default({},e,{map:s})),C.createElement("groundProjectedEnvImpl",{args:l,scale:m,height:c,radius:d}))}function na(e){return e.ground?C.createElement(ra,e):e.map?C.createElement(Jn,e):e.children?C.createElement(ta,e):C.createElement(ea,e)}const aa=C.forwardRef((({scale:e=10,frames:t=1/0,opacity:r=1,width:n=1,height:o=1,blur:i=1,near:s=0,far:l=10,resolution:c=512,smooth:d=!0,color:m="#000000",depthWrite:f=!1,renderOrder:p,...h},x)=>{const y=C.useRef(null),v=a.useThree((e=>e.scene)),g=a.useThree((e=>e.gl)),w=C.useRef(null);n*=Array.isArray(e)?e[0]:e||1,o*=Array.isArray(e)?e[1]:e||1;const[z,b,E,M,S,P,D]=C.useMemo((()=>{const e=new R.WebGLRenderTarget(c,c),t=new R.WebGLRenderTarget(c,c);t.texture.generateMipmaps=e.texture.generateMipmaps=!1;const r=new R.PlaneGeometry(n,o).rotateX(Math.PI/2),a=new R.Mesh(r),i=new R.MeshDepthMaterial;i.depthTest=i.depthWrite=!1,i.onBeforeCompile=e=>{e.uniforms={...e.uniforms,ucolor:{value:new R.Color(m)}},e.fragmentShader=e.fragmentShader.replace("void main() {","uniform vec3 ucolor;\n           void main() {\n          "),e.fragmentShader=e.fragmentShader.replace("vec4( vec3( 1.0 - fragCoordZ ), opacity );","vec4( ucolor * fragCoordZ * 2.0, ( 1.0 - fragCoordZ ) * 1.0 );")};const s=new R.ShaderMaterial(u.HorizontalBlurShader),l=new R.ShaderMaterial(u.VerticalBlurShader);return l.depthTest=s.depthTest=!1,[e,r,i,a,s,l,t]}),[c,n,o,e,m]),F=e=>{M.visible=!0,M.material=S,S.uniforms.tDiffuse.value=z.texture,S.uniforms.h.value=1*e/256,g.setRenderTarget(D),g.render(M,w.current),M.material=P,P.uniforms.tDiffuse.value=D.texture,P.uniforms.v.value=1*e/256,g.setRenderTarget(z),g.render(M,w.current),M.visible=!1};let k,_,A=0;return a.useFrame((()=>{w.current&&(t===1/0||A<t)&&(A++,k=v.background,_=v.overrideMaterial,y.current.visible=!1,v.background=null,v.overrideMaterial=E,g.setRenderTarget(z),g.render(v,w.current),F(i),d&&F(.4*i),g.setRenderTarget(null),y.current.visible=!0,v.overrideMaterial=_,v.background=k)})),C.useImperativeHandle(x,(()=>y.current),[]),C.createElement("group",T.default({"rotation-x":Math.PI/2},h,{ref:y}),C.createElement("mesh",{renderOrder:p,geometry:b,scale:[1,-1,1],rotation:[-Math.PI/2,0,0]},C.createElement("meshBasicMaterial",{transparent:!0,map:z.texture,opacity:r,depthWrite:f})),C.createElement("orthographicCamera",{ref:w,args:[-n/2,n/2,o/2,-o/2,s,l]}))}));const oa=C.createContext(null),ia=Te({color:new R.Color,blend:2,alphaTest:.75,opacity:0,map:null},"varying vec2 vUv;\n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vUv = uv;\n   }",`varying vec2 vUv;\n   uniform sampler2D map;\n   uniform vec3 color;\n   uniform float opacity;\n   uniform float alphaTest;\n   uniform float blend;\n   void main() {\n     vec4 sampledDiffuseColor = texture2D(map, vUv);\n     gl_FragColor = vec4(color * sampledDiffuseColor.r * blend, max(0.0, (1.0 - (sampledDiffuseColor.r + sampledDiffuseColor.g + sampledDiffuseColor.b) / alphaTest)) * opacity);\n     #include <tonemapping_fragment>\n     #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n   }`),sa=C.forwardRef((({children:e,temporal:t,frames:r=40,limit:n=1/0,blend:o=20,scale:i=10,opacity:s=1,alphaTest:l=.75,color:c="black",colorBlend:u=2,resolution:d=1024,toneMapped:m=!0,...f},p)=>{a.extend({SoftShadowMaterial:ia});const h=a.useThree((e=>e.gl)),x=a.useThree((e=>e.scene)),y=a.useThree((e=>e.camera)),v=a.useThree((e=>e.invalidate)),g=C.useRef(null),w=C.useRef(null),[z]=C.useState((()=>new ca(h,x,d)));C.useLayoutEffect((()=>{z.configure(g.current)}),[]);const b=C.useMemo((()=>({lights:new Map,temporal:!!t,frames:Math.max(2,r),blend:Math.max(2,r===1/0?o:r),count:0,getMesh:()=>g.current,reset:()=>{z.clear();const e=g.current.material;e.opacity=0,e.alphaTest=0,b.count=0},update:(e=1)=>{const t=g.current.material;b.temporal?(t.opacity=Math.min(s,t.opacity+s/b.blend),t.alphaTest=Math.min(l,t.alphaTest+l/b.blend)):(t.opacity=s,t.alphaTest=l),w.current.visible=!0,z.prepare();for(let t=0;t<e;t++)b.lights.forEach((e=>e.update())),z.update(y,b.blend);w.current.visible=!1,z.finish()}})),[z,y,x,t,r,o,s,l]);return C.useLayoutEffect((()=>{b.reset(),b.temporal||b.frames===1/0||b.update(b.blend)})),C.useImperativeHandle(p,(()=>b),[b]),a.useFrame((()=>{(b.temporal||b.frames===1/0)&&b.count<b.frames&&b.count<n&&(v(),b.update(),b.count++)})),C.createElement("group",f,C.createElement("group",{traverse:()=>null,ref:w},C.createElement(oa.Provider,{value:b},e)),C.createElement("mesh",{receiveShadow:!0,ref:g,scale:i,rotation:[-Math.PI/2,0,0]},C.createElement("planeGeometry",null),C.createElement("softShadowMaterial",{transparent:!0,depthWrite:!1,toneMapped:m,color:c,blend:u,map:z.progressiveLightMap2.texture})))})),la=C.forwardRef((({castShadow:e=!0,bias:t=.001,mapSize:r=512,size:n=5,near:a=.5,far:o=500,frames:i=1,position:s=[0,0,0],radius:l=1,amount:c=8,intensity:u=(Re>=155?Math.PI:1),ambient:d=.5,...m},f)=>{const p=C.useRef(null),h=new R.Vector3(...s).length(),x=C.useContext(oa),y=C.useCallback((()=>{let e;if(p.current)for(let t=0;t<p.current.children.length;t++)if(e=p.current.children[t],Math.random()>d)e.position.set(s[0]+R.MathUtils.randFloatSpread(l),s[1]+R.MathUtils.randFloatSpread(l),s[2]+R.MathUtils.randFloatSpread(l));else{let t=Math.acos(2*Math.random()-1)-Math.PI/2,r=2*Math.PI*Math.random();e.position.set(Math.cos(t)*Math.cos(r)*h,Math.abs(Math.cos(t)*Math.sin(r)*h),Math.sin(t)*h)}}),[l,d,h,...s]),v=C.useMemo((()=>({update:y})),[y]);return C.useImperativeHandle(f,(()=>v),[v]),C.useLayoutEffect((()=>{var e;const t=p.current;return x&&(null==(e=x.lights)||e.set(t.uuid,v)),()=>{var e;null==x||null==(e=x.lights)||e.delete(t.uuid)}}),[x,v]),C.createElement("group",T.default({ref:p},m),Array.from({length:c},((i,s)=>C.createElement("directionalLight",{key:s,castShadow:e,"shadow-bias":t,"shadow-mapSize":[r,r],intensity:u/c},C.createElement("orthographicCamera",{attach:"shadow-camera",args:[-n,n,n,-n,a,o]})))))}));class ca{constructor(e,t,r=1024){this.renderer=e,this.res=r,this.scene=t,this.buffer1Active=!1,this.lights=[],this.meshes=[],this.object=null,this.clearColor=new R.Color,this.clearAlpha=0;const n={type:R.HalfFloatType,magFilter:R.NearestFilter,minFilter:R.NearestFilter};this.progressiveLightMap1=new R.WebGLRenderTarget(this.res,this.res,n),this.progressiveLightMap2=new R.WebGLRenderTarget(this.res,this.res,n),this.discardMat=new en,this.targetMat=new R.MeshLambertMaterial({fog:!1}),this.previousShadowMap={value:this.progressiveLightMap1.texture},this.averagingWindow={value:100},this.targetMat.onBeforeCompile=e=>{e.vertexShader="varying vec2 vUv;\n"+e.vertexShader.slice(0,-1)+"vUv = uv; gl_Position = vec4((uv - 0.5) * 2.0, 1.0, 1.0); }";const t=e.fragmentShader.indexOf("void main() {");e.fragmentShader="varying vec2 vUv;\n"+e.fragmentShader.slice(0,t)+"uniform sampler2D previousShadowMap;\n\tuniform float averagingWindow;\n"+e.fragmentShader.slice(t-1,-1)+"\nvec3 texelOld = texture2D(previousShadowMap, vUv).rgb;\n        gl_FragColor.rgb = mix(texelOld, gl_FragColor.rgb, 1.0/ averagingWindow);\n      }",e.uniforms.previousShadowMap=this.previousShadowMap,e.uniforms.averagingWindow=this.averagingWindow}}clear(){this.renderer.getClearColor(this.clearColor),this.clearAlpha=this.renderer.getClearAlpha(),this.renderer.setClearColor("black",1),this.renderer.setRenderTarget(this.progressiveLightMap1),this.renderer.clear(),this.renderer.setRenderTarget(this.progressiveLightMap2),this.renderer.clear(),this.renderer.setRenderTarget(null),this.renderer.setClearColor(this.clearColor,this.clearAlpha),this.lights=[],this.meshes=[],this.scene.traverse((e=>{!function(e){return!!e.geometry}(e)?function(e){return e.isLight}(e)&&this.lights.push({object:e,intensity:e.intensity}):this.meshes.push({object:e,material:e.material})}))}prepare(){this.lights.forEach((e=>e.object.intensity=0)),this.meshes.forEach((e=>e.object.material=this.discardMat))}finish(){this.lights.forEach((e=>e.object.intensity=e.intensity)),this.meshes.forEach((e=>e.object.material=e.material))}configure(e){this.object=e}update(e,t=100){if(!this.object)return;this.averagingWindow.value=t,this.object.material=this.targetMat;const r=this.buffer1Active?this.progressiveLightMap1:this.progressiveLightMap2,n=this.buffer1Active?this.progressiveLightMap2:this.progressiveLightMap1,a=this.scene.background;this.scene.background=null,this.renderer.setRenderTarget(r),this.previousShadowMap.value=n.texture,this.buffer1Active=!this.buffer1Active,this.renderer.render(this.scene,e),this.renderer.setRenderTarget(null),this.scene.background=a}}const ua={rembrandt:{main:[1,2,1],fill:[-2,-.5,-2]},portrait:{main:[-1,2,.5],fill:[-1,.5,-1.5]},upfront:{main:[0,2,1],fill:[-1,.5,-1.5]},soft:{main:[-2,4,4],fill:[-1,.5,-1.5]}};function da({radius:e,adjustCamera:t}){const r=Un();return C.useEffect((()=>{t&&r.refresh().clip().fit()}),[e,t]),null}const ma=e=>0===e?0:Math.pow(2,10*e-10);const fa=C.forwardRef((({fog:e=!1,renderOrder:t,depthWrite:r=!1,colorStop:a=0,color:o="black",opacity:i=.5,...s},l)=>{const c=C.useMemo((()=>{const e=document.createElement("canvas");e.width=128,e.height=128;const t=e.getContext("2d"),r=t.createRadialGradient(e.width/2,e.height/2,0,e.width/2,e.height/2,e.width/2);return r.addColorStop(a,new n.Color(o).getStyle()),r.addColorStop(1,"rgba(0,0,0,0)"),t.fillStyle=r,t.fillRect(0,0,e.width,e.height),e}),[o,a]);return C.createElement("mesh",T.default({renderOrder:t,ref:l,"rotation-x":-Math.PI/2},s),C.createElement("planeGeometry",null),C.createElement("meshBasicMaterial",{transparent:!0,opacity:i,fog:e,depthWrite:r,side:n.DoubleSide},C.createElement("canvasTexture",{attach:"map",args:[c]})))}));function pa(e=R.FrontSide){const t={value:new R.Matrix4};return Object.assign(new R.MeshNormalMaterial({side:e}),{viewMatrix:t,onBeforeCompile:e=>{e.uniforms.viewMatrix=t,e.fragmentShader="vec3 inverseTransformDirection( in vec3 dir, in mat4 matrix ) {\n           return normalize( ( vec4( dir, 0.0 ) * matrix ).xyz );\n         }\n"+e.fragmentShader.replace("#include <normal_fragment_maps>","#include <normal_fragment_maps>\n           normal = inverseTransformDirection( normal, viewMatrix );\n")}})}const ha=Te({causticsTexture:null,causticsTextureB:null,color:new R.Color,lightProjMatrix:new R.Matrix4,lightViewMatrix:new R.Matrix4},"varying vec3 vWorldPosition;\n   void main() {\n     gl_Position = projectionMatrix * viewMatrix * modelMatrix * vec4(position, 1.);\n     vec4 worldPosition = modelMatrix * vec4(position, 1.);\n     vWorldPosition = worldPosition.xyz;\n   }",`varying vec3 vWorldPosition;\n  uniform vec3 color;\n  uniform sampler2D causticsTexture;\n  uniform sampler2D causticsTextureB;\n  uniform mat4 lightProjMatrix;\n  uniform mat4 lightViewMatrix;\n   void main() {\n    // Apply caustics\n    vec4 lightSpacePos = lightProjMatrix * lightViewMatrix * vec4(vWorldPosition, 1.0);\n    lightSpacePos.xyz /= lightSpacePos.w;\n    lightSpacePos.xyz = lightSpacePos.xyz * 0.5 + 0.5;\n    vec3 front = texture2D(causticsTexture, lightSpacePos.xy).rgb;\n    vec3 back = texture2D(causticsTextureB, lightSpacePos.xy).rgb;\n    gl_FragColor = vec4((front + back) * color, 1.0);\n    #include <tonemapping_fragment>\n    #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n   }`),xa=Te({cameraMatrixWorld:new R.Matrix4,cameraProjectionMatrixInv:new R.Matrix4,normalTexture:null,depthTexture:null,lightDir:new R.Vector3(0,1,0),lightPlaneNormal:new R.Vector3(0,1,0),lightPlaneConstant:0,near:.1,far:100,modelMatrix:new R.Matrix4,worldRadius:1/40,ior:1.1,bounces:0,resolution:1024,size:10,intensity:.5},"\n  varying vec2 vUv;\n  void main() {\n      vUv = uv;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n  }","\n  uniform mat4 cameraMatrixWorld;\n  uniform mat4 cameraProjectionMatrixInv;\n  uniform vec3 lightDir;\n  uniform vec3 lightPlaneNormal;\n  uniform float lightPlaneConstant;\n  uniform float near;\n  uniform float far;\n  uniform float time;\n  uniform float worldRadius;\n  uniform float resolution;\n  uniform float size;\n  uniform float intensity;\n  uniform float ior;\n  precision highp isampler2D;\n  precision highp usampler2D;\n  uniform sampler2D normalTexture;\n  uniform sampler2D depthTexture;\n  uniform float bounces;\n  varying vec2 vUv;\n  vec3 WorldPosFromDepth(float depth, vec2 coord) {\n    float z = depth * 2.0 - 1.0;\n    vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n    vec4 viewSpacePosition = cameraProjectionMatrixInv * clipSpacePosition;\n    // Perspective division\n    viewSpacePosition /= viewSpacePosition.w;\n    vec4 worldSpacePosition = cameraMatrixWorld * viewSpacePosition;\n    return worldSpacePosition.xyz;\n  }\n  float sdPlane( vec3 p, vec3 n, float h ) {\n    // n must be normalized\n    return dot(p,n) + h;\n  }\n  float planeIntersect( vec3 ro, vec3 rd, vec4 p ) {\n    return -(dot(ro,p.xyz)+p.w)/dot(rd,p.xyz);\n  }\n  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 pos, vec3 normal, float ior, out vec3 rayOrigin, out vec3 rayDirection) {\n    rayOrigin = ro;\n    rayDirection = rd;\n    rayDirection = refract(rayDirection, normal, 1.0 / ior);\n    rayOrigin = pos + rayDirection * 0.1;\n    return rayDirection;\n  }\n  void main() {\n    // Each sample consists of random offset in the x and y direction\n    float caustic = 0.0;\n    float causticTexelSize = (1.0 / resolution) * size * 2.0;\n    float texelsNeeded = worldRadius / causticTexelSize;\n    float sampleRadius = texelsNeeded / resolution;\n    float sum = 0.0;\n    if (texture2D(depthTexture, vUv).x == 1.0) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n      return;\n    }\n    vec2 offset1 = vec2(-0.5, -0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset2 = vec2(-0.5, 0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset3 = vec2(0.5, 0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 offset4 = vec2(0.5, -0.5);//vec2(rand() - 0.5, rand() - 0.5);\n    vec2 uv1 = vUv + offset1 * sampleRadius;\n    vec2 uv2 = vUv + offset2 * sampleRadius;\n    vec2 uv3 = vUv + offset3 * sampleRadius;\n    vec2 uv4 = vUv + offset4 * sampleRadius;\n    vec3 normal1 = texture2D(normalTexture, uv1, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal2 = texture2D(normalTexture, uv2, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal3 = texture2D(normalTexture, uv3, -10.0).rgb * 2.0 - 1.0;\n    vec3 normal4 = texture2D(normalTexture, uv4, -10.0).rgb * 2.0 - 1.0;\n    float depth1 = texture2D(depthTexture, uv1, -10.0).x;\n    float depth2 = texture2D(depthTexture, uv2, -10.0).x;\n    float depth3 = texture2D(depthTexture, uv3, -10.0).x;\n    float depth4 = texture2D(depthTexture, uv4, -10.0).x;\n    // Sanity check the depths\n    if (depth1 == 1.0 || depth2 == 1.0 || depth3 == 1.0 || depth4 == 1.0) {\n      gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\n      return;\n    }\n    vec3 pos1 = WorldPosFromDepth(depth1, uv1);\n    vec3 pos2 = WorldPosFromDepth(depth2, uv2);\n    vec3 pos3 = WorldPosFromDepth(depth3, uv3);\n    vec3 pos4 = WorldPosFromDepth(depth4, uv4);\n    vec3 originPos1 = WorldPosFromDepth(0.0, uv1);\n    vec3 originPos2 = WorldPosFromDepth(0.0, uv2);\n    vec3 originPos3 = WorldPosFromDepth(0.0, uv3);\n    vec3 originPos4 = WorldPosFromDepth(0.0, uv4);\n    vec3 endPos1, endPos2, endPos3, endPos4;\n    vec3 endDir1, endDir2, endDir3, endDir4;\n    totalInternalReflection(originPos1, lightDir, pos1, normal1, ior, endPos1, endDir1);\n    totalInternalReflection(originPos2, lightDir, pos2, normal2, ior, endPos2, endDir2);\n    totalInternalReflection(originPos3, lightDir, pos3, normal3, ior, endPos3, endDir3);\n    totalInternalReflection(originPos4, lightDir, pos4, normal4, ior, endPos4, endDir4);\n    float lightPosArea = length(cross(originPos2 - originPos1, originPos3 - originPos1)) + length(cross(originPos3 - originPos1, originPos4 - originPos1));\n    float t1 = planeIntersect(endPos1, endDir1, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t2 = planeIntersect(endPos2, endDir2, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t3 = planeIntersect(endPos3, endDir3, vec4(lightPlaneNormal, lightPlaneConstant));\n    float t4 = planeIntersect(endPos4, endDir4, vec4(lightPlaneNormal, lightPlaneConstant));\n    vec3 finalPos1 = endPos1 + endDir1 * t1;\n    vec3 finalPos2 = endPos2 + endDir2 * t2;\n    vec3 finalPos3 = endPos3 + endDir3 * t3;\n    vec3 finalPos4 = endPos4 + endDir4 * t4;\n    float finalArea = length(cross(finalPos2 - finalPos1, finalPos3 - finalPos1)) + length(cross(finalPos3 - finalPos1, finalPos4 - finalPos1));\n    caustic += intensity * (lightPosArea / finalArea);\n    // Calculate the area of the triangle in light spaces\n    gl_FragColor = vec4(vec3(max(caustic, 0.0)), 1.0);\n  }"),ya={depth:!0,minFilter:R.LinearFilter,magFilter:R.LinearFilter,type:R.UnsignedByteType},va={minFilter:R.LinearMipmapLinearFilter,magFilter:R.LinearFilter,type:R.FloatType,generateMipmaps:!0},ga=C.forwardRef((({debug:e,children:t,frames:r=1,ior:n=1.1,color:o="white",causticsOnly:i=!1,backside:s=!1,backsideIOR:l=1.1,worldRadius:c=.3125,intensity:d=.05,resolution:m=2024,lightSource:f=[5,5,5],...p},h)=>{a.extend({CausticsProjectionMaterial:ha});const x=C.useRef(null),y=C.useRef(null),v=C.useRef(null),g=C.useRef(null),w=a.useThree((e=>e.gl)),z=ur(e&&y,R.CameraHelper),b=ot(m,m,ya),E=ot(m,m,ya),M=ot(m,m,va),S=ot(m,m,va),[P]=C.useState((()=>pa())),[D]=C.useState((()=>pa(R.BackSide))),[F]=C.useState((()=>new xa)),[k]=C.useState((()=>new u.FullScreenQuad(F)));C.useLayoutEffect((()=>{x.current.updateWorldMatrix(!1,!0)}));let _=0;const A=new R.Vector3,L=new R.Frustum,I=new R.Matrix4,B=new R.Plane,V=new R.Vector3,U=new R.Vector3,O=new R.Box3,N=new R.Vector3,j=[],W=[],G=[],H=[],$=new R.Vector3;for(let e=0;e<8;e++)j.push(new R.Vector3),W.push(new R.Vector3),G.push(new R.Vector3),H.push(new R.Vector3);return a.useFrame((()=>{if(r===1/0||_++<r){var t,a;Array.isArray(f)?V.fromArray(f).normalize():V.copy(x.current.worldToLocal(f.current.getWorldPosition(A)).normalize()),U.copy(V).multiplyScalar(-1),null==(t=v.current.parent)||t.matrixWorld.identity(),O.setFromObject(v.current,!0),j[0].set(O.min.x,O.min.y,O.min.z),j[1].set(O.min.x,O.min.y,O.max.z),j[2].set(O.min.x,O.max.y,O.min.z),j[3].set(O.min.x,O.max.y,O.max.z),j[4].set(O.max.x,O.min.y,O.min.z),j[5].set(O.max.x,O.min.y,O.max.z),j[6].set(O.max.x,O.max.y,O.min.z),j[7].set(O.max.x,O.max.y,O.max.z);for(let e=0;e<8;e++)W[e].copy(j[e]);O.getCenter(N),j.map((e=>e.sub(N)));const r=B.set(U,0);j.map(((e,t)=>r.projectPoint(e,G[t])));const o=G.reduce(((e,t)=>e.add(t)),A.set(0,0,0)).divideScalar(G.length),u=G.map((e=>e.distanceTo(o))).reduce(((e,t)=>Math.max(e,t))),p=j.map((e=>e.dot(V))).reduce(((e,t)=>Math.max(e,t)));y.current.position.copy($.copy(V).multiplyScalar(p).add(N)),y.current.lookAt(v.current.localToWorld(N));const h=I.lookAt(y.current.position,N,A.set(0,1,0));y.current.left=-u,y.current.right=u,y.current.top=u,y.current.bottom=-u;const T=A.set(0,u,0).applyMatrix4(h),C=(y.current.position.y+T.y)/V.y;y.current.near=.1,y.current.far=C,y.current.updateProjectionMatrix(),y.current.updateMatrixWorld();const R=W.map(((e,t)=>e.add(H[t].copy(V).multiplyScalar(-e.y/V.y)))),_=R.reduce(((e,t)=>e.add(t)),A.set(0,0,0)).divideScalar(R.length),q=2*R.map((e=>Math.hypot(e.x-_.x,e.z-_.z))).reduce(((e,t)=>Math.max(e,t)));g.current.scale.setScalar(q),g.current.position.copy(_),e&&(null==(a=z.current)||a.update()),D.viewMatrix.value=P.viewMatrix.value=y.current.matrixWorldInverse;const X=L.setFromProjectionMatrix(I.multiplyMatrices(y.current.projectionMatrix,y.current.matrixWorldInverse)).planes[4];F.cameraMatrixWorld=y.current.matrixWorld,F.cameraProjectionMatrixInv=y.current.projectionMatrixInverse,F.lightDir=U,F.lightPlaneNormal=X.normal,F.lightPlaneConstant=X.constant,F.near=y.current.near,F.far=y.current.far,F.resolution=m,F.size=u,F.intensity=d,F.worldRadius=c,v.current.visible=!0,w.setRenderTarget(b),w.clear(),v.current.overrideMaterial=P,w.render(v.current,y.current),w.setRenderTarget(E),w.clear(),s&&(v.current.overrideMaterial=D,w.render(v.current,y.current)),v.current.overrideMaterial=null,F.ior=n,g.current.material.lightProjMatrix=y.current.projectionMatrix,g.current.material.lightViewMatrix=y.current.matrixWorldInverse,F.normalTexture=b.texture,F.depthTexture=b.depthTexture,w.setRenderTarget(M),w.clear(),k.render(w),F.ior=l,F.normalTexture=E.texture,F.depthTexture=E.depthTexture,w.setRenderTarget(S),w.clear(),s&&k.render(w),w.setRenderTarget(null),i&&(v.current.visible=!1)}})),C.useImperativeHandle(h,(()=>x.current),[]),C.createElement("group",T.default({ref:x},p),C.createElement("scene",{ref:v},C.createElement("orthographicCamera",{ref:y,up:[0,1,0]}),t),C.createElement("mesh",{renderOrder:2,ref:g,"rotation-x":-Math.PI/2},C.createElement("planeGeometry",null),C.createElement("causticsProjectionMaterial",{transparent:!0,color:o,causticsTexture:M.texture,causticsTextureB:S.texture,blending:R.CustomBlending,blendSrc:R.OneFactor,blendDst:R.SrcAlphaFactor,depthWrite:!1}),e&&C.createElement(Le,null,C.createElement("lineBasicMaterial",{color:"#ffff00",toneMapped:!1}))))})),wa=C.forwardRef((({mixBlur:e=0,mixStrength:t=.5,resolution:r=256,blur:o=[0,0],args:i=[1,1],minDepthThreshold:s=.9,maxDepthThreshold:l=1,depthScale:c=0,depthToBlurRatioBias:u=.25,mirror:d=0,children:m,debug:f=0,distortion:p=1,mixContrast:h=1,distortionMap:x,...y},v)=>{a.extend({MeshReflectorMaterial:Qr}),C.useEffect((()=>{console.warn("Reflector has been deprecated and will be removed next major. Replace it with <MeshReflectorMaterial />!")}),[]);const g=a.useThree((({gl:e})=>e)),w=a.useThree((({camera:e})=>e)),z=a.useThree((({scene:e})=>e)),b=(o=Array.isArray(o)?o:[o,o])[0]+o[1]>0,E=C.useRef(null);C.useImperativeHandle(v,(()=>E.current),[]);const[M]=C.useState((()=>new n.Plane)),[S]=C.useState((()=>new n.Vector3)),[P]=C.useState((()=>new n.Vector3)),[R]=C.useState((()=>new n.Vector3)),[D]=C.useState((()=>new n.Matrix4)),[F]=C.useState((()=>new n.Vector3(0,0,-1))),[k]=C.useState((()=>new n.Vector4)),[_]=C.useState((()=>new n.Vector3)),[A]=C.useState((()=>new n.Vector3)),[L]=C.useState((()=>new n.Vector4)),[I]=C.useState((()=>new n.Matrix4)),[B]=C.useState((()=>new n.PerspectiveCamera)),V=C.useCallback((()=>{if(P.setFromMatrixPosition(E.current.matrixWorld),R.setFromMatrixPosition(w.matrixWorld),D.extractRotation(E.current.matrixWorld),S.set(0,0,1),S.applyMatrix4(D),_.subVectors(P,R),_.dot(S)>0)return;_.reflect(S).negate(),_.add(P),D.extractRotation(w.matrixWorld),F.set(0,0,-1),F.applyMatrix4(D),F.add(R),A.subVectors(P,F),A.reflect(S).negate(),A.add(P),B.position.copy(_),B.up.set(0,1,0),B.up.applyMatrix4(D),B.up.reflect(S),B.lookAt(A),B.far=w.far,B.updateMatrixWorld(),B.projectionMatrix.copy(w.projectionMatrix),I.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),I.multiply(B.projectionMatrix),I.multiply(B.matrixWorldInverse),I.multiply(E.current.matrixWorld),M.setFromNormalAndCoplanarPoint(S,P),M.applyMatrix4(B.matrixWorldInverse),k.set(M.normal.x,M.normal.y,M.normal.z,M.constant);const e=B.projectionMatrix;L.x=(Math.sign(k.x)+e.elements[8])/e.elements[0],L.y=(Math.sign(k.y)+e.elements[9])/e.elements[5],L.z=-1,L.w=(1+e.elements[10])/e.elements[14],k.multiplyScalar(2/k.dot(L)),e.elements[2]=k.x,e.elements[6]=k.y,e.elements[10]=k.z+1,e.elements[14]=k.w}),[]),[U,O,N,j]=C.useMemo((()=>{const a={type:n.HalfFloatType,minFilter:n.LinearFilter,magFilter:n.LinearFilter},i=new n.WebGLRenderTarget(r,r,a);i.depthBuffer=!0,i.depthTexture=new n.DepthTexture(r,r),i.depthTexture.format=n.DepthFormat,i.depthTexture.type=n.UnsignedShortType;const m=new n.WebGLRenderTarget(r,r,a);return[i,m,new Yr({gl:g,resolution:r,width:o[0],height:o[1],minDepthThreshold:s,maxDepthThreshold:l,depthScale:c,depthToBlurRatioBias:u}),{mirror:d,textureMatrix:I,mixBlur:e,tDiffuse:i.texture,tDepth:i.depthTexture,tDiffuseBlur:m.texture,hasBlur:b,mixStrength:t,minDepthThreshold:s,maxDepthThreshold:l,depthScale:c,depthToBlurRatioBias:u,transparent:!0,debug:f,distortion:p,distortionMap:x,mixContrast:h,"defines-USE_BLUR":b?"":void 0,"defines-USE_DEPTH":c>0?"":void 0,"defines-USE_DISTORTION":x?"":void 0}]}),[g,o,I,r,d,b,e,t,s,l,c,u,f,p,x,h]);return a.useFrame((()=>{if(null==E||!E.current)return;E.current.visible=!1;const e=g.xr.enabled,t=g.shadowMap.autoUpdate;V(),g.xr.enabled=!1,g.shadowMap.autoUpdate=!1,g.setRenderTarget(U),g.state.buffers.depth.setMask(!0),g.autoClear||g.clear(),g.render(z,B),b&&N.render(g,U,O),g.xr.enabled=e,g.shadowMap.autoUpdate=t,E.current.visible=!0,g.setRenderTarget(null)})),C.createElement("mesh",T.default({ref:E},y),C.createElement("planeGeometry",{args:i}),m?m("meshReflectorMaterial",j):C.createElement("meshReflectorMaterial",j))}));class za extends R.ShaderMaterial{constructor(){super({uniforms:{depth:{value:null},opacity:{value:1},attenuation:{value:2.5},anglePower:{value:12},spotPosition:{value:new R.Vector3(0,0,0)},lightColor:{value:new R.Color("white")},cameraNear:{value:0},cameraFar:{value:1},resolution:{value:new R.Vector2(0,0)}},transparent:!0,depthWrite:!1,vertexShader:"\n        varying vec3 vNormal;\n        varying float vViewZ;\n        varying float vIntensity;\n        uniform vec3 spotPosition;\n        uniform float attenuation;\n\n        #include <common>\n        #include <logdepthbuf_pars_vertex>\n\n        void main() {\n          // compute intensity\n          vNormal = normalize(normalMatrix * normal);\n          vec4 worldPosition = modelMatrix * vec4(position, 1);\n          vec4 viewPosition = viewMatrix * worldPosition;\n          vViewZ = viewPosition.z;\n\n          vIntensity = 1.0 - saturate(distance(worldPosition.xyz, spotPosition) / attenuation);\n\n          gl_Position = projectionMatrix * viewPosition;\n\n          #include <logdepthbuf_vertex>\n        }\n      ",fragmentShader:`\n        varying vec3 vNormal;\n        varying float vViewZ;\n        varying float vIntensity;\n\n        uniform vec3 lightColor;\n        uniform float anglePower;\n        uniform sampler2D depth;\n        uniform vec2 resolution;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float opacity;\n\n        #include <packing>\n        #include <logdepthbuf_pars_fragment>\n\n        float readDepth(sampler2D depthSampler, vec2 uv) {\n          float fragCoordZ = texture(depthSampler, uv).r;\n\n          // https://github.com/mrdoob/three.js/issues/23072\n          #ifdef USE_LOGDEPTHBUF\n            float viewZ = 1.0 - exp2(fragCoordZ * log(cameraFar + 1.0) / log(2.0));\n          #else\n            float viewZ = perspectiveDepthToViewZ(fragCoordZ, cameraNear, cameraFar);\n          #endif\n\n          return viewZ;\n        }\n\n        void main() {\n          #include <logdepthbuf_fragment>\n\n          vec3 normal = vec3(vNormal.x, vNormal.y, abs(vNormal.z));\n          float angleIntensity = pow(dot(normal, vec3(0, 0, 1)), anglePower);\n          float intensity = vIntensity * angleIntensity;\n\n          // fades when z is close to sampled depth, meaning the cone is intersecting existing geometry\n          bool isSoft = resolution[0] > 0.0 && resolution[1] > 0.0;\n          if (isSoft) {\n            vec2 uv = gl_FragCoord.xy / resolution;\n            intensity *= smoothstep(0.0, 1.0, vViewZ - readDepth(depth, uv));\n          }\n\n          gl_FragColor = vec4(lightColor, intensity * opacity);\n\n          #include <tonemapping_fragment>\n          #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n        }\n      `})}}function ba({opacity:e=1,radiusTop:t,radiusBottom:r,depthBuffer:o,color:i="white",distance:s=5,angle:l=.15,attenuation:c=5,anglePower:u=5}){const d=C.useRef(null),m=a.useThree((e=>e.size)),f=a.useThree((e=>e.camera)),p=a.useThree((e=>e.viewport.dpr)),[h]=C.useState((()=>new za)),[x]=C.useState((()=>new n.Vector3));t=void 0===t?.1:t,r=void 0===r?7*l:r,a.useFrame((()=>{h.uniforms.spotPosition.value.copy(d.current.getWorldPosition(x)),d.current.lookAt(d.current.parent.target.getWorldPosition(x))}));const y=C.useMemo((()=>{const e=new n.CylinderGeometry(t,r,s,128,64,!0);return e.applyMatrix4((new n.Matrix4).makeTranslation(0,-s/2,0)),e.applyMatrix4((new n.Matrix4).makeRotationX(-Math.PI/2)),e}),[s,t,r]);return C.createElement(C.Fragment,null,C.createElement("mesh",{ref:d,geometry:y,raycast:()=>null},C.createElement("primitive",{object:h,attach:"material","uniforms-opacity-value":e,"uniforms-lightColor-value":i,"uniforms-attenuation-value":c,"uniforms-anglePower-value":u,"uniforms-depth-value":o,"uniforms-cameraNear-value":f.near,"uniforms-cameraFar-value":f.far,"uniforms-resolution-value":o?[m.width*p,m.height*p]:[0,0]})))}function Ea(e,t,r,o,i){const[[s,l]]=C.useState((()=>[new n.Vector3,new n.Vector3]));C.useLayoutEffect((()=>{if(!(null==(t=e.current)?void 0:t.isSpotLight))throw new Error("SpotlightShadow must be a child of a SpotLight");var t;e.current.shadow.mapSize.set(r,o),e.current.shadow.needsUpdate=!0}),[e,r,o]),a.useFrame((()=>{if(!e.current)return;const r=e.current.position,n=e.current.target.position;l.copy(n).sub(r);var a=l.length();l.normalize().multiplyScalar(a*i),s.copy(r).add(l),t.current.position.copy(s),t.current.lookAt(e.current.target.position)}))}function Ma({distance:e=.4,alphaTest:t=.5,map:r,shader:o="#define GLSLIFY 1\nvarying vec2 vUv;uniform sampler2D uShadowMap;uniform float uTime;void main(){vec3 color=texture2D(uShadowMap,vUv).xyz;gl_FragColor=vec4(color,1.);}",width:i=512,height:s=512,scale:l=1,children:c,...d}){const m=C.useRef(null),f=d.spotlightRef,p=d.debug;Ea(f,m,i,s,e);const h=C.useMemo((()=>new n.WebGLRenderTarget(i,s,{format:n.RGBAFormat,stencilBuffer:!1})),[i,s]),x=C.useRef({uShadowMap:{value:r},uTime:{value:0}});C.useEffect((()=>{x.current.uShadowMap.value=r}),[r]);const y=C.useMemo((()=>new u.FullScreenQuad(new n.ShaderMaterial({uniforms:x.current,vertexShader:"\n          varying vec2 vUv;\n\n          void main() {\n            vUv = uv;\n            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n          }\n          ",fragmentShader:o}))),[o]);return C.useEffect((()=>()=>{y.material.dispose(),y.dispose()}),[y]),C.useEffect((()=>()=>h.dispose()),[h]),a.useFrame((({gl:e},t)=>{x.current.uTime.value+=t,e.setRenderTarget(h),y.render(e),e.setRenderTarget(null)})),C.createElement(C.Fragment,null,C.createElement("mesh",{ref:m,scale:l,castShadow:!0},C.createElement("planeGeometry",null),C.createElement("meshBasicMaterial",{transparent:!0,side:n.DoubleSide,alphaTest:t,alphaMap:h.texture,"alphaMap-wrapS":n.RepeatWrapping,"alphaMap-wrapT":n.RepeatWrapping,opacity:p?1:0},c)))}function Sa({distance:e=.4,alphaTest:t=.5,map:r,width:a=512,height:o=512,scale:i,children:s,...l}){const c=C.useRef(null),u=l.spotlightRef,d=l.debug;return Ea(u,c,a,o,e),C.createElement(C.Fragment,null,C.createElement("mesh",{ref:c,scale:i,castShadow:!0},C.createElement("planeGeometry",null),C.createElement("meshBasicMaterial",{transparent:!0,side:n.DoubleSide,alphaTest:t,alphaMap:r,"alphaMap-wrapS":n.RepeatWrapping,"alphaMap-wrapT":n.RepeatWrapping,opacity:d?1:0},s)))}const Ta=C.forwardRef((({opacity:e=1,radiusTop:t,radiusBottom:r,depthBuffer:n,color:a="white",distance:o=5,angle:i=.15,attenuation:s=5,anglePower:l=5,volumetric:c=!0,debug:u=!1,children:d,...m},f)=>{const p=C.useRef(null);return C.useImperativeHandle(f,(()=>p.current),[]),C.createElement("group",null,u&&p.current&&C.createElement("spotLightHelper",{args:[p.current]}),C.createElement("spotLight",T.default({ref:p,angle:i,color:a,distance:o,castShadow:!0},m),c&&C.createElement(ba,{debug:u,opacity:e,radiusTop:t,radiusBottom:r,depthBuffer:n,color:a,distance:o,angle:i,attenuation:s,anglePower:l})),d&&C.cloneElement(d,{spotlightRef:p,debug:u}))})),Ca=C.forwardRef((({light:e,args:t,map:r,toneMapped:n=!1,color:o="white",form:i="rect",intensity:s=1,scale:l=1,target:c=[0,0,0],children:u,...d},m)=>{const f=C.useRef(null);return C.useImperativeHandle(m,(()=>f.current),[]),C.useLayoutEffect((()=>{u||d.material||(a.applyProps(f.current.material,{color:o}),f.current.material.color.multiplyScalar(s))}),[o,s,u,d.material]),C.useLayoutEffect((()=>{d.rotation||f.current.quaternion.identity(),c&&!d.rotation&&("boolean"==typeof c?f.current.lookAt(0,0,0):f.current.lookAt(Array.isArray(c)?new R.Vector3(...c):c))}),[c,d.rotation]),l=Array.isArray(l)&&2===l.length?[l[0],l[1],1]:l,C.createElement("mesh",T.default({ref:f,scale:l},d),"circle"===i?C.createElement("ringGeometry",{args:t||[0,.5,64]}):"ring"===i?C.createElement("ringGeometry",{args:t||[.25,.5,64]}):"rect"===i||"plane"===i?C.createElement("planeGeometry",{args:t||[1,1]}):"box"===i?C.createElement("boxGeometry",{args:t||[1,1,1]}):C.createElement(i,{args:t}),u||C.createElement("meshBasicMaterial",{toneMapped:n,map:r,side:R.DoubleSide}),e&&C.createElement("pointLight",T.default({castShadow:!0},e)))}));function Pa(e,t,r=new n.Vector3){const a=Math.PI*(e-.5),o=2*Math.PI*(t-.5);return r.x=Math.cos(o),r.y=Math.sin(a),r.z=Math.sin(o),r}const Ra=C.forwardRef((({inclination:e=.6,azimuth:t=.1,distance:r=1e3,mieCoefficient:a=.005,mieDirectionalG:o=.8,rayleigh:i=.5,turbidity:s=10,sunPosition:l=Pa(e,t),...c},d)=>{const m=C.useMemo((()=>(new n.Vector3).setScalar(r)),[r]),[f]=C.useState((()=>new u.Sky));return C.createElement("primitive",T.default({object:f,ref:d,"material-uniforms-mieCoefficient-value":a,"material-uniforms-mieDirectionalG-value":o,"material-uniforms-rayleigh-value":i,"material-uniforms-sunPosition-value":l,"material-uniforms-turbidity-value":s,scale:m},c))}));class Da extends n.ShaderMaterial{constructor(){super({uniforms:{time:{value:0},fade:{value:1}},vertexShader:"\n      uniform float time;\n      attribute float size;\n      varying vec3 vColor;\n      void main() {\n        vColor = color;\n        vec4 mvPosition = modelViewMatrix * vec4(position, 0.5);\n        gl_PointSize = size * (30.0 / -mvPosition.z) * (3.0 + sin(time + 100.0));\n        gl_Position = projectionMatrix * mvPosition;\n      }",fragmentShader:`\n      uniform sampler2D pointTexture;\n      uniform float fade;\n      varying vec3 vColor;\n      void main() {\n        float opacity = 1.0;\n        if (fade == 1.0) {\n          float d = distance(gl_PointCoord, vec2(0.5, 0.5));\n          opacity = 1.0 / (1.0 + exp(16.0 * (d - 0.25)));\n        }\n        gl_FragColor = vec4(vColor, opacity);\n\n        #include <tonemapping_fragment>\n\t      #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n      }`})}}const Fa=e=>(new n.Vector3).setFromSpherical(new n.Spherical(e,Math.acos(1-2*Math.random()),2*Math.random()*Math.PI)),ka=C.forwardRef((({radius:e=100,depth:t=50,count:r=5e3,saturation:o=0,factor:i=4,fade:s=!1,speed:l=1},c)=>{const u=C.useRef(),[d,m,f]=C.useMemo((()=>{const a=[],s=[],l=Array.from({length:r},(()=>(.5+.5*Math.random())*i)),c=new n.Color;let u=e+t;const d=t/r;for(let e=0;e<r;e++)u-=d*Math.random(),a.push(...Fa(u).toArray()),c.setHSL(e/r,o,.9),s.push(c.r,c.g,c.b);return[new Float32Array(a),new Float32Array(s),new Float32Array(l)]}),[r,t,i,e,o]);a.useFrame((e=>u.current&&(u.current.uniforms.time.value=e.clock.elapsedTime*l)));const[p]=C.useState((()=>new Da));return C.createElement("points",{ref:c},C.createElement("bufferGeometry",null,C.createElement("bufferAttribute",{attach:"attributes-position",args:[d,3]}),C.createElement("bufferAttribute",{attach:"attributes-color",args:[m,3]}),C.createElement("bufferAttribute",{attach:"attributes-size",args:[f,1]})),C.createElement("primitive",{ref:u,object:p,attach:"material",blending:n.AdditiveBlending,"uniforms-fade-value":s,depthWrite:!1,transparent:!0,vertexColors:!0}))})),_a=new n.Matrix4,Aa=new n.Vector3,La=new n.Quaternion,Ia=new n.Vector3,Ba=new n.Quaternion,Va=new n.Vector3,Ua=C.createContext(null),Oa=C.forwardRef((({children:e,material:t=n.MeshLambertMaterial,texture:r="https://rawcdn.githack.com/pmndrs/drei-assets/9225a9f1fbd449d9411125c2f419b843d0308c9f/cloud.png",range:o,limit:i=200,frustumCulled:s,...l},c)=>{var u,d;const m=C.useMemo((()=>class extends t{constructor(){super();const e=parseInt(n.REVISION.replace(/\D+/g,""))>=154?"opaque_fragment":"output_fragment";this.onBeforeCompile=t=>{t.vertexShader="attribute float cloudOpacity;\n               varying float vOpacity;\n              "+t.vertexShader.replace("#include <fog_vertex>","#include <fog_vertex>\n                 vOpacity = cloudOpacity;\n                "),t.fragmentShader="varying float vOpacity;\n              "+t.fragmentShader.replace(`#include <${e}>`,`#include <${e}>\n                 gl_FragColor = vec4(outgoingLight, diffuseColor.a * vOpacity);\n                `)}}}),[t]);a.extend({CloudMaterial:m});const f=C.useRef(null),p=C.useRef([]),h=C.useMemo((()=>new Float32Array(Array.from({length:i},(()=>1)))),[i]),x=C.useMemo((()=>new Float32Array(Array.from({length:i},(()=>[1,1,1])).flat())),[i]),y=Pe(r);let v,g=0,w=0;const z=new n.Quaternion,b=new n.Vector3(0,0,1),E=new n.Vector3;a.useFrame(((e,t)=>{for(g=e.clock.elapsedTime,_a.copy(f.current.matrixWorld).invert(),e.camera.matrixWorld.decompose(Ia,Ba,Va),w=0;w<p.current.length;w++)v=p.current[w],v.ref.current.matrixWorld.decompose(Aa,La,Va),Aa.add(E.copy(v.position).applyQuaternion(La).multiply(Va)),La.copy(Ba).multiply(z.setFromAxisAngle(b,v.rotation+=t*v.rotationFactor)),Va.multiplyScalar(v.volume+(1+Math.sin(g*v.density*v.speed))/2*v.growth),v.matrix.compose(Aa,La,Va).premultiply(_a),v.dist=Aa.distanceTo(Ia);for(p.current.sort(((e,t)=>t.dist-e.dist)),w=0;w<p.current.length;w++)v=p.current[w],h[w]=v.opacity*(v.dist<v.fade-1?v.dist/v.fade:1),f.current.setMatrixAt(w,v.matrix),f.current.setColorAt(w,v.color);f.current.geometry.attributes.cloudOpacity.needsUpdate=!0,f.current.instanceMatrix.needsUpdate=!0,f.current.instanceColor&&(f.current.instanceColor.needsUpdate=!0)})),C.useLayoutEffect((()=>{const e=Math.min(i,void 0!==o?o:i,p.current.length);f.current.count=e,Mr(f.current.instanceMatrix,{offset:0,count:16*e}),f.current.instanceColor&&Mr(f.current.instanceColor,{offset:0,count:3*e}),Mr(f.current.geometry.attributes.cloudOpacity,{offset:0,count:e})}));let M=[null!==(u=y.image.width)&&void 0!==u?u:1,null!==(d=y.image.height)&&void 0!==d?d:1];const S=Math.max(M[0],M[1]);return M=[M[0]/S,M[1]/S],C.createElement("group",T.default({ref:c},l),C.createElement(Ua.Provider,{value:p},e,C.createElement("instancedMesh",{matrixAutoUpdate:!1,ref:f,args:[null,null,i],frustumCulled:s},C.createElement("instancedBufferAttribute",{usage:n.DynamicDrawUsage,attach:"instanceColor",args:[x,3]}),C.createElement("planeGeometry",{args:[...M]},C.createElement("instancedBufferAttribute",{usage:n.DynamicDrawUsage,attach:"attributes-cloudOpacity",args:[h,1]})),C.createElement("cloudMaterial",{key:t.name,map:y,transparent:!0,depthWrite:!1}))))})),Na=C.forwardRef((({opacity:e=1,speed:t=0,bounds:r=[5,1,1],segments:o=20,color:i="#ffffff",fade:s=10,volume:l=6,smallestVolume:c=.25,distribute:u=null,growth:d=4,concentrate:m="inside",seed:f=Math.random(),...p},h)=>{function x(){const e=1e4*Math.sin(f++);return e-Math.floor(e)}const y=C.useContext(Ua),v=C.useRef(null),g=C.useId(),w=C.useMemo((()=>[...new Array(o)].map(((e,t)=>({segments:o,bounds:new n.Vector3(1,1,1),position:new n.Vector3,uuid:g,index:t,ref:v,dist:0,matrix:new n.Matrix4,color:new n.Color,rotation:t*(Math.PI/o)})))),[o,g]);return C.useLayoutEffect((()=>{w.forEach(((n,f)=>{a.applyProps(n,{volume:l,color:i,speed:t,growth:d,opacity:e,fade:s,bounds:r,density:Math.max(.5,x()),rotationFactor:Math.max(.2,.5*x())*t});const p=null==u?void 0:u(n,f);var h;(p||o>1)&&n.position.copy(n.bounds).multiply(null!==(h=null==p?void 0:p.point)&&void 0!==h?h:{x:2*x()-1,y:2*x()-1,z:2*x()-1});const y=Math.abs(n.position.x),v=Math.abs(n.position.y),g=Math.abs(n.position.z),w=Math.max(y,v,g);n.length=1,y===w&&(n.length-=y/n.bounds.x),v===w&&(n.length-=v/n.bounds.y),g===w&&(n.length-=g/n.bounds.z),n.volume=(void 0!==(null==p?void 0:p.volume)?p.volume:Math.max(Math.max(0,c),"random"===m?x():"inside"===m?n.length:1-n.length))*l}))}),[m,r,s,i,e,d,l,f,o,t]),C.useLayoutEffect((()=>{const e=w;return y.current=[...y.current,...e],()=>{y.current=y.current.filter((e=>e.uuid!==g))}}),[w]),C.useImperativeHandle(h,(()=>v.current),[]),C.createElement("group",T.default({ref:v},p))})),ja=C.forwardRef(((e,t)=>C.useContext(Ua)?C.createElement(Na,T.default({ref:t},e)):C.createElement(Oa,null,C.createElement(Na,T.default({ref:t},e)))));class Wa extends R.ShaderMaterial{constructor(){super({uniforms:{time:{value:0},pixelRatio:{value:1}},vertexShader:"\n        uniform float pixelRatio;\n        uniform float time;\n        attribute float size;  \n        attribute float speed;  \n        attribute float opacity;\n        attribute vec3 noise;\n        attribute vec3 color;\n        varying vec3 vColor;\n        varying float vOpacity;\n\n        void main() {\n          vec4 modelPosition = modelMatrix * vec4(position, 1.0);\n          modelPosition.y += sin(time * speed + modelPosition.x * noise.x * 100.0) * 0.2;\n          modelPosition.z += cos(time * speed + modelPosition.x * noise.y * 100.0) * 0.2;\n          modelPosition.x += cos(time * speed + modelPosition.x * noise.z * 100.0) * 0.2;\n          vec4 viewPosition = viewMatrix * modelPosition;\n          vec4 projectionPostion = projectionMatrix * viewPosition;\n          gl_Position = projectionPostion;\n          gl_PointSize = size * 25. * pixelRatio;\n          gl_PointSize *= (1.0 / - viewPosition.z);\n          vColor = color;\n          vOpacity = opacity;\n        }\n      ",fragmentShader:`\n        varying vec3 vColor;\n        varying float vOpacity;\n        void main() {\n          float distanceToCenter = distance(gl_PointCoord, vec2(0.5));\n          float strength = 0.05 / distanceToCenter - 0.1;\n          gl_FragColor = vec4(vColor, strength * vOpacity);\n          #include <tonemapping_fragment>\n          #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n        }\n      `})}get time(){return this.uniforms.time.value}set time(e){this.uniforms.time.value=e}get pixelRatio(){return this.uniforms.pixelRatio.value}set pixelRatio(e){this.uniforms.pixelRatio.value=e}}const Ga=e=>e&&e.constructor===Float32Array,Ha=e=>e instanceof R.Vector2||e instanceof R.Vector3||e instanceof R.Vector4,$a=e=>Array.isArray(e)?e:Ha(e)?e.toArray():[e,e,e];function qa(e,t,r){return C.useMemo((()=>{if(void 0!==t){if(Ga(t))return t;if(t instanceof R.Color){const r=Array.from({length:3*e},(()=>(e=>[e.r,e.g,e.b])(t))).flat();return Float32Array.from(r)}if(Ha(t)||Array.isArray(t)){const r=Array.from({length:3*e},(()=>$a(t))).flat();return Float32Array.from(r)}return Float32Array.from({length:e},(()=>t))}return Float32Array.from({length:e},r)}),[t])}const Xa=C.forwardRef((({noise:e=1,count:t=100,speed:r=1,opacity:n=1,scale:o=1,size:i,color:s,children:l,...c},u)=>{C.useMemo((()=>a.extend({SparklesImplMaterial:Wa})),[]);const d=C.useRef(null),m=a.useThree((e=>e.viewport.dpr)),f=$a(o),p=C.useMemo((()=>Float32Array.from(Array.from({length:t},(()=>f.map(R.MathUtils.randFloatSpread))).flat())),[t,...f]),h=qa(t,i,Math.random),x=qa(t,n),y=qa(t,r),v=qa(3*t,e),g=qa(void 0===s?3*t:t,Ga(s)?s:new R.Color(s),(()=>1));return a.useFrame((e=>{d.current&&d.current.material&&(d.current.material.time=e.clock.elapsedTime)})),C.useImperativeHandle(u,(()=>d.current),[]),C.createElement("points",T.default({key:`particle-${t}-${JSON.stringify(o)}`},c,{ref:d}),C.createElement("bufferGeometry",null,C.createElement("bufferAttribute",{attach:"attributes-position",args:[p,3]}),C.createElement("bufferAttribute",{attach:"attributes-size",args:[h,1]}),C.createElement("bufferAttribute",{attach:"attributes-opacity",args:[x,1]}),C.createElement("bufferAttribute",{attach:"attributes-speed",args:[y,1]}),C.createElement("bufferAttribute",{attach:"attributes-color",args:[g,3]}),C.createElement("bufferAttribute",{attach:"attributes-noise",args:[v,3]})),l||C.createElement("sparklesImplMaterial",{transparent:!0,pixelRatio:m,depthWrite:!1}))}));function Za(e=0,t=1024,r){const n=f.suspend((()=>fetch("https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/matcaps.json").then((e=>e.json()))),["matcapList"]),a=n[0],o=C.useMemo((()=>Object.keys(n).length),[]),i=`${C.useMemo((()=>"string"==typeof e?e:"number"==typeof e?n[e]:null),[e])||a}${function(e){switch(e){case 64:return"-64px";case 128:return"-128px";case 256:return"-256px";case 512:return"-512px";default:return""}}(t)}.png`,s=`https://rawcdn.githack.com/emmelleppi/matcaps/9b36ccaaf0a24881a39062d05566c9e92be4aa0d/${t}/${i}`;return[Pe(s,r),s,o]}function Ya(e=0,t={},r){const{repeat:a=[1,1],anisotropy:o=1,offset:i=[0,0]}=t,s=f.suspend((()=>fetch("https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/normals/normals.json").then((e=>e.json()))),["normalsList"]),l=C.useMemo((()=>Object.keys(s).length),[]),c=s[0],u=`https://rawcdn.githack.com/pmndrs/drei-assets/7a3104997e1576f83472829815b00880d88b32fb/normals/${s[e]||c}`,d=Pe(u,r);return C.useLayoutEffect((()=>{d&&(d.wrapS=d.wrapT=n.RepeatWrapping,d.repeat=new n.Vector2(a[0],a[1]),d.offset=new n.Vector2(i[0],i[1]),d.anisotropy=o)}),[d,o,a,i]),[d,u,l]}const Qa={uniforms:{strokeOpacity:1,fillOpacity:.25,fillMix:0,thickness:.05,colorBackfaces:!1,dashInvert:!0,dash:!1,dashRepeats:4,dashLength:.5,squeeze:!1,squeezeMin:.2,squeezeMax:1,stroke:new R.Color("#ff0000"),backfaceStroke:new R.Color("#0000ff"),fill:new R.Color("#00ff00")},vertex:"\n\t  attribute vec3 barycentric;\n\t\n\t\tvarying vec3 v_edges_Barycentric;\n\t\tvarying vec3 v_edges_Position;\n\n\t\tvoid initWireframe() {\n\t\t\tv_edges_Barycentric = barycentric;\n\t\t\tv_edges_Position = position.xyz;\n\t\t}\n\t  ",fragment:"\n\t\t#ifndef PI\n\t  \t#define PI 3.1415926535897932384626433832795\n\t\t#endif\n  \n\t  varying vec3 v_edges_Barycentric;\n\t  varying vec3 v_edges_Position;\n  \n\t  uniform float strokeOpacity;\n\t  uniform float fillOpacity;\n\t  uniform float fillMix;\n\t  uniform float thickness;\n\t  uniform bool colorBackfaces;\n  \n\t  // Dash\n\t  uniform bool dashInvert;\n\t  uniform bool dash;\n\t  uniform bool dashOnly;\n\t  uniform float dashRepeats;\n\t  uniform float dashLength;\n  \n\t  // Squeeze\n\t  uniform bool squeeze;\n\t  uniform float squeezeMin;\n\t  uniform float squeezeMax;\n  \n\t  // Colors\n\t  uniform vec3 stroke;\n\t  uniform vec3 backfaceStroke;\n\t  uniform vec3 fill;\n  \n\t  // This is like\n\t  float wireframe_aastep(float threshold, float dist) {\n\t\t  float afwidth = fwidth(dist) * 0.5;\n\t\t  return smoothstep(threshold - afwidth, threshold + afwidth, dist);\n\t  }\n  \n\t  float wireframe_map(float value, float min1, float max1, float min2, float max2) {\n\t\t  return min2 + (value - min1) * (max2 - min2) / (max1 - min1);\n\t  }\n  \n\t  float getWireframe() {\n\t\t\tvec3 barycentric = v_edges_Barycentric;\n\t\t\n\t\t\t// Distance from center of each triangle to its edges.\n\t\t\tfloat d = min(min(barycentric.x, barycentric.y), barycentric.z);\n\n\t\t\t// for dashed rendering, we can use this to get the 0 .. 1 value of the line length\n\t\t\tfloat positionAlong = max(barycentric.x, barycentric.y);\n\t\t\tif (barycentric.y < barycentric.x && barycentric.y < barycentric.z) {\n\t\t\t\tpositionAlong = 1.0 - positionAlong;\n\t\t\t}\n\n\t\t\t// the thickness of the stroke\n\t\t\tfloat computedThickness = wireframe_map(thickness, 0.0, 1.0, 0.0, 0.34);\n\n\t\t\t// if we want to shrink the thickness toward the center of the line segment\n\t\t\tif (squeeze) {\n\t\t\t\tcomputedThickness *= mix(squeezeMin, squeezeMax, (1.0 - sin(positionAlong * PI)));\n\t\t\t}\n\n\t\t\t// Create dash pattern\n\t\t\tif (dash) {\n\t\t\t\t// here we offset the stroke position depending on whether it\n\t\t\t\t// should overlap or not\n\t\t\t\tfloat offset = 1.0 / dashRepeats * dashLength / 2.0;\n\t\t\t\tif (!dashInvert) {\n\t\t\t\t\toffset += 1.0 / dashRepeats / 2.0;\n\t\t\t\t}\n\n\t\t\t\t// if we should animate the dash or not\n\t\t\t\t// if (dashAnimate) {\n\t\t\t\t// \toffset += time * 0.22;\n\t\t\t\t// }\n\n\t\t\t\t// create the repeating dash pattern\n\t\t\t\tfloat pattern = fract((positionAlong + offset) * dashRepeats);\n\t\t\t\tcomputedThickness *= 1.0 - wireframe_aastep(dashLength, pattern);\n\t\t\t}\n\n\t\t\t// compute the anti-aliased stroke edge  \n\t\t\tfloat edge = 1.0 - wireframe_aastep(computedThickness, d);\n\n\t\t\treturn edge;\n\t  }\n\t  "},Ka=Te(Qa.uniforms,Qa.vertex+"\n  \tvoid main() {\n\t\tinitWireframe();\n\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n\t}\n  ",Qa.fragment+"\n  void main () {\n\t\t// Compute color\n\n\t\tfloat edge = getWireframe();\n\t\tvec4 colorStroke = vec4(stroke, edge);\n\n\t\t#ifdef FLIP_SIDED\n\t\t\tcolorStroke.rgb = backfaceStroke;\n\t\t#endif\n    \n\t\tvec4 colorFill = vec4(fill, fillOpacity);\n\t\tvec4 outColor = mix(colorFill, colorStroke, edge * strokeOpacity);\n\n\t\tgl_FragColor = outColor;\n\t}\n  ");function Ja(e){return void 0!==(null==e?void 0:e.current)}function eo(e){return"WireframeGeometry"===e.type}function to(e){const t=null!=(r=e)&&r.current?e.current:e;var r;if(function(e){return!(null==e||!e.isBufferGeometry)}(t))return t;{if(eo(t))throw new Error("Wireframe: WireframeGeometry is not supported.");const e=t.parent;if(function(e){return!(null==e||!e.geometry)}(e)){if(eo(e.geometry))throw new Error("Wireframe: WireframeGeometry is not supported.");return e.geometry}}}function ro(e,t){if(e.index){console.warn("Wireframe: Requires non-indexed geometry, converting to non-indexed geometry.");const t=e.toNonIndexed();e.copy(t),e.setIndex(null)}const r=function(e,t){const r=e.getAttribute("position").count,n=[];for(let e=0;e<r;e++){const r=t?1:0;e%2==0?n.push(0,0,1,0,1,0,1,0,r):n.push(0,1,0,0,0,1,1,0,r)}return new R.BufferAttribute(Float32Array.from(n),3)}(e,t);e.setAttribute("barycentric",r)}function no({geometry:e,simplify:t=!1,...r}){a.extend({MeshWireframeMaterial:Ka});const[n,o]=C.useState(null);C.useLayoutEffect((()=>{const r=to(e);if(!r)throw new Error("Wireframe: geometry prop must be a BufferGeometry or a ref to a BufferGeometry.");ro(r,t),Ja(e)&&o(r)}),[t,e]);const i=Ja(e)?n:e;return C.createElement(C.Fragment,null,i&&C.createElement("mesh",{geometry:i},C.createElement("meshWireframeMaterial",T.default({attach:"material",transparent:!0,side:R.DoubleSide,polygonOffset:!0,polygonOffsetFactor:-4},r,{extensions:{derivatives:!0,fragDepth:!1,drawBuffers:!1,shaderTextureLOD:!1}}))))}function ao({simplify:e=!1,...t}){const r=C.useRef(null),n=C.useMemo((()=>function(){const e={};for(const t in Qa.uniforms)e[t]={value:Qa.uniforms[t]};return e}()),[Qa.uniforms]);return function(e,t){C.useEffect((()=>{var r;e.fillOpacity.value=null!==(r=t.fillOpacity)&&void 0!==r?r:e.fillOpacity.value}),[t.fillOpacity]),C.useEffect((()=>{var r;e.fillMix.value=null!==(r=t.fillMix)&&void 0!==r?r:e.fillMix.value}),[t.fillMix]),C.useEffect((()=>{var r;e.strokeOpacity.value=null!==(r=t.strokeOpacity)&&void 0!==r?r:e.strokeOpacity.value}),[t.strokeOpacity]),C.useEffect((()=>{var r;e.thickness.value=null!==(r=t.thickness)&&void 0!==r?r:e.thickness.value}),[t.thickness]),C.useEffect((()=>{e.colorBackfaces.value=!!t.colorBackfaces}),[t.colorBackfaces]),C.useEffect((()=>{e.dash.value=!!t.dash}),[t.dash]),C.useEffect((()=>{e.dashInvert.value=!!t.dashInvert}),[t.dashInvert]),C.useEffect((()=>{var r;e.dashRepeats.value=null!==(r=t.dashRepeats)&&void 0!==r?r:e.dashRepeats.value}),[t.dashRepeats]),C.useEffect((()=>{var r;e.dashLength.value=null!==(r=t.dashLength)&&void 0!==r?r:e.dashLength.value}),[t.dashLength]),C.useEffect((()=>{e.squeeze.value=!!t.squeeze}),[t.squeeze]),C.useEffect((()=>{var r;e.squeezeMin.value=null!==(r=t.squeezeMin)&&void 0!==r?r:e.squeezeMin.value}),[t.squeezeMin]),C.useEffect((()=>{var r;e.squeezeMax.value=null!==(r=t.squeezeMax)&&void 0!==r?r:e.squeezeMax.value}),[t.squeezeMax]),C.useEffect((()=>{e.stroke.value=t.stroke?new R.Color(t.stroke):e.stroke.value}),[t.stroke]),C.useEffect((()=>{e.fill.value=t.fill?new R.Color(t.fill):e.fill.value}),[t.fill]),C.useEffect((()=>{e.backfaceStroke.value=t.backfaceStroke?new R.Color(t.backfaceStroke):e.backfaceStroke.value}),[t.backfaceStroke])}(n,t),C.useLayoutEffect((()=>{const t=to(r);if(!t)throw new Error("Wireframe: Must be a child of a Mesh, Line or Points object or specify a geometry prop.");const n=t.clone();return ro(t,e),()=>{t.copy(n),n.dispose()}}),[e]),C.useLayoutEffect((()=>{const e=r.current.parent,t=e.material.clone();return function(e,t){e.onBeforeCompile=e=>{e.uniforms={...e.uniforms,...t},e.vertexShader=e.vertexShader.replace("void main() {",`\n\t\t  ${Qa.vertex}\n\t\t  void main() {\n\t\t\tinitWireframe();\n\t\t`),e.fragmentShader=e.fragmentShader.replace("void main() {",`\n\t\t  ${Qa.fragment}\n\t\t  void main() {\n\t\t`),e.fragmentShader=e.fragmentShader.replace("#include <color_fragment>","\n\t\t  #include <color_fragment>\n\t\t\t  float edge = getWireframe();\n\t\t  vec4 colorStroke = vec4(stroke, edge);\n\t\t  #ifdef FLIP_SIDED\n\t\t\tcolorStroke.rgb = backfaceStroke;\n\t\t  #endif\n\t\t  vec4 colorFill = vec4(mix(diffuseColor.rgb, fill, fillMix), mix(diffuseColor.a, fillOpacity, fillMix));\n\t\t  vec4 outColor = mix(colorFill, colorStroke, edge * strokeOpacity);\n\n\t\t  diffuseColor.rgb = outColor.rgb;\n\t\t  diffuseColor.a *= outColor.a;\n\t\t")},e.side=R.DoubleSide,e.transparent=!0}(e.material,n),()=>{e.material.dispose(),e.material=t}}),[]),C.createElement("object3D",{ref:r})}const oo=new R.Matrix4,io=new R.Ray,so=new R.Sphere,lo=new R.Vector3;class co extends R.Group{constructor(){super(),this.size=0,this.color=new R.Color("white"),this.instance={current:void 0},this.instanceKey={current:void 0}}get geometry(){var e;return null==(e=this.instance.current)?void 0:e.geometry}raycast(e,t){var r,n;const a=this.instance.current;if(!a||!a.geometry)return;const o=a.userData.instances.indexOf(this.instanceKey);if(-1===o||o>a.geometry.drawRange.count)return;const i=null!==(r=null==(n=e.params.Points)?void 0:n.threshold)&&void 0!==r?r:1;if(so.set(this.getWorldPosition(lo),i),!1===e.ray.intersectsSphere(so))return;oo.copy(a.matrixWorld).invert(),io.copy(e.ray).applyMatrix4(oo);const s=i/((this.scale.x+this.scale.y+this.scale.z)/3),l=s*s,c=io.distanceSqToPoint(this.position);if(c<l){const r=new R.Vector3;io.closestPointToPoint(this.position,r),r.applyMatrix4(this.matrixWorld);const n=e.ray.origin.distanceTo(r);if(n<e.near||n>e.far)return;t.push({distance:n,distanceToRay:Math.sqrt(c),point:r,index:o,face:null,object:this})}}}let uo,mo;const fo=C.createContext(null),po=new R.Matrix4,ho=new R.Vector3,xo=C.forwardRef((({children:e,range:t,limit:r=1e3,...n},o)=>{const i=C.useRef(null);C.useImperativeHandle(o,(()=>i.current),[]);const[s,l]=C.useState([]),[[c,u,d]]=C.useState((()=>[new Float32Array(3*r),Float32Array.from({length:3*r},(()=>1)),Float32Array.from({length:r},(()=>1))]));C.useEffect((()=>{i.current.geometry.attributes.position.needsUpdate=!0})),a.useFrame((()=>{for(i.current.updateMatrix(),i.current.updateMatrixWorld(),po.copy(i.current.matrixWorld).invert(),i.current.geometry.drawRange.count=Math.min(r,void 0!==t?t:r,s.length),uo=0;uo<s.length;uo++)mo=s[uo].current,mo.getWorldPosition(ho).applyMatrix4(po),ho.toArray(c,3*uo),i.current.geometry.attributes.position.needsUpdate=!0,mo.matrixWorldNeedsUpdate=!0,mo.color.toArray(u,3*uo),i.current.geometry.attributes.color.needsUpdate=!0,d.set([mo.size],uo),i.current.geometry.attributes.size.needsUpdate=!0}));const m=C.useMemo((()=>({getParent:()=>i,subscribe:e=>(l((t=>[...t,e])),()=>l((t=>t.filter((t=>t.current!==e.current)))))})),[]);return C.createElement("points",T.default({userData:{instances:s},matrixAutoUpdate:!1,ref:i,raycast:()=>null},n),C.createElement("bufferGeometry",null,C.createElement("bufferAttribute",{attach:"attributes-position",count:c.length/3,array:c,itemSize:3,usage:R.DynamicDrawUsage}),C.createElement("bufferAttribute",{attach:"attributes-color",count:u.length/3,array:u,itemSize:3,usage:R.DynamicDrawUsage}),C.createElement("bufferAttribute",{attach:"attributes-size",count:d.length,array:d,itemSize:1,usage:R.DynamicDrawUsage})),C.createElement(fo.Provider,{value:m},e))})),yo=C.forwardRef((({children:e,...t},r)=>{C.useMemo((()=>a.extend({PositionPoint:co})),[]);const n=C.useRef(null);C.useImperativeHandle(r,(()=>n.current),[]);const{subscribe:o,getParent:i}=C.useContext(fo);return C.useLayoutEffect((()=>o(n)),[]),C.createElement("positionPoint",T.default({instance:i(),instanceKey:n,ref:n},t),e)})),vo=C.forwardRef((({children:e,positions:t,colors:r,sizes:n,stride:o=3,...i},s)=>{const l=C.useRef(null);return C.useImperativeHandle(s,(()=>l.current),[]),a.useFrame((()=>{const e=l.current.geometry.attributes;e.position.needsUpdate=!0,r&&(e.color.needsUpdate=!0),n&&(e.size.needsUpdate=!0)})),C.createElement("points",T.default({ref:l},i),C.createElement("bufferGeometry",null,C.createElement("bufferAttribute",{attach:"attributes-position",count:t.length/o,array:t,itemSize:o,usage:R.DynamicDrawUsage}),r&&C.createElement("bufferAttribute",{attach:"attributes-color",count:r.length/o,array:r,itemSize:3,usage:R.DynamicDrawUsage}),n&&C.createElement("bufferAttribute",{attach:"attributes-size",count:n.length/o,array:n,itemSize:1,usage:R.DynamicDrawUsage})),e)})),go=C.forwardRef(((e,t)=>e.positions instanceof Float32Array?C.createElement(vo,T.default({},e,{ref:t})):C.createElement(xo,T.default({},e,{ref:t})))),wo=C.createContext(null),zo=C.forwardRef(((e,t)=>{C.useMemo((()=>a.extend({SegmentObject:bo})),[]);const{limit:r=1e3,lineWidth:n=1,children:o,...i}=e,[s,l]=C.useState([]),[c]=C.useState((()=>new u.Line2)),[d]=C.useState((()=>new u.LineMaterial)),[m]=C.useState((()=>new u.LineSegmentsGeometry)),[f]=C.useState((()=>new R.Vector2(512,512))),[p]=C.useState((()=>Array(6*r).fill(0))),[h]=C.useState((()=>Array(6*r).fill(0))),x=C.useMemo((()=>({subscribe:e=>(l((t=>[...t,e])),()=>l((t=>t.filter((t=>t.current!==e.current)))))})),[]);return a.useFrame((()=>{for(let t=0;t<r;t++){var e;const r=null==(e=s[t])?void 0:e.current;r&&(p[6*t+0]=r.start.x,p[6*t+1]=r.start.y,p[6*t+2]=r.start.z,p[6*t+3]=r.end.x,p[6*t+4]=r.end.y,p[6*t+5]=r.end.z,h[6*t+0]=r.color.r,h[6*t+1]=r.color.g,h[6*t+2]=r.color.b,h[6*t+3]=r.color.r,h[6*t+4]=r.color.g,h[6*t+5]=r.color.b)}m.setColors(h),m.setPositions(p),c.computeLineDistances()})),C.createElement("primitive",{object:c,ref:t},C.createElement("primitive",{object:m,attach:"geometry"}),C.createElement("primitive",T.default({object:d,attach:"material",vertexColors:!0,resolution:f,linewidth:n},i)),C.createElement(wo.Provider,{value:x},o))}));class bo{constructor(){this.color=new R.Color("white"),this.start=new R.Vector3(0,0,0),this.end=new R.Vector3(0,0,0)}}const Eo=e=>e instanceof R.Vector3?e:new R.Vector3(..."number"==typeof e?[e,e,e]:e),Mo=C.forwardRef((({color:e,start:t,end:r},n)=>{const a=C.useContext(wo);if(!a)throw"Segment must used inside Segments component.";const o=C.useRef(null);return C.useImperativeHandle(n,(()=>o.current),[]),C.useLayoutEffect((()=>a.subscribe(o)),[]),C.createElement("segmentObject",{ref:o,color:e,start:Eo(t),end:Eo(r)})})),So=C.forwardRef((({children:e,hysteresis:t=0,distances:r,...n},o)=>{const i=C.useRef(null);return C.useImperativeHandle(o,(()=>i.current),[]),C.useLayoutEffect((()=>{const{current:e}=i;e.levels.length=0,e.children.forEach(((n,a)=>e.levels.push({object:n,hysteresis:t,distance:r[a]})))})),a.useFrame((e=>{var t;return null==(t=i.current)?void 0:t.update(e.camera)})),C.createElement("lOD",T.default({ref:i},n),e)}));const To=new n.Matrix4,Co=new n.Ray,Po=new n.Sphere,Ro=new n.Vector3;const Do=t.createContext(null);const Fo=C.forwardRef((({children:e,compute:t,width:r,height:n,samples:o=8,renderPriority:i=0,eventPriority:s=0,frames:l=1/0,stencilBuffer:c=!1,depthBuffer:u=!0,generateMipmaps:d=!1,...m},f)=>{const{size:p,viewport:h}=a.useThree(),x=ot((r||p.width)*h.dpr,(n||p.height)*h.dpr,{samples:o,stencilBuffer:c,depthBuffer:u,generateMipmaps:d}),[y]=C.useState((()=>new R.Scene)),v=C.useCallback(((e,t,r)=>{var n,a;let o=null==(n=x.texture)?void 0:n.__r3f.parent;for(;o&&!(o instanceof R.Object3D);)o=o.__r3f.parent;if(!o)return!1;r.raycaster.camera||r.events.compute(e,r,null==(a=r.previousRoot)?void 0:a.getState());const[i]=r.raycaster.intersectObject(o);if(!i)return!1;const s=i.uv;if(!s)return!1;t.raycaster.setFromCamera(t.pointer.set(2*s.x-1,2*s.y-1),t.camera)}),[]);return C.useImperativeHandle(f,(()=>x.texture),[x]),C.createElement(C.Fragment,null,a.createPortal(C.createElement(ko,{renderPriority:i,frames:l,fbo:x},e,C.createElement("group",{onPointerOver:()=>null})),y,{events:{compute:t||v,priority:s}}),C.createElement("primitive",T.default({object:x.texture},m)))}));function ko({frames:e,renderPriority:t,children:r,fbo:n}){let o,i,s,l,c=0;return a.useFrame((t=>{(e===1/0||c<e)&&(o=t.gl.autoClear,i=t.gl.xr.enabled,s=t.gl.getRenderTarget(),l=t.gl.xr.isPresenting,t.gl.autoClear=!0,t.gl.xr.enabled=!1,t.gl.xr.isPresenting=!1,t.gl.setRenderTarget(n),t.gl.render(t.scene,t.camera),t.gl.setRenderTarget(s),t.gl.autoClear=o,t.gl.xr.enabled=i,t.gl.xr.isPresenting=l,c++)}),t),C.createElement(C.Fragment,null,r)}const _o=C.forwardRef((({children:e,compute:t,renderPriority:r=-1,eventPriority:n=0,frames:o=1/0,stencilBuffer:i=!1,depthBuffer:s=!0,generateMipmaps:l=!1,resolution:c=896,near:u=.1,far:d=1e3,flip:m=!1,position:f,rotation:p,scale:h,quaternion:x,matrix:y,matrixAutoUpdate:v,...g},w)=>{const{size:z,viewport:b}=a.useThree(),E=C.useRef(null),M=C.useMemo((()=>{const e=new R.WebGLCubeRenderTarget(Math.max((c||z.width)*b.dpr,(c||z.height)*b.dpr),{stencilBuffer:i,depthBuffer:s,generateMipmaps:l});return e.texture.isRenderTargetTexture=!m,e.texture.flipY=!0,e.texture.type=R.HalfFloatType,e}),[c,m]);C.useEffect((()=>()=>M.dispose()),[M]);const[S]=C.useState((()=>new R.Scene));return C.useImperativeHandle(w,(()=>({scene:S,fbo:M,camera:E.current})),[M]),C.createElement(C.Fragment,null,a.createPortal(C.createElement(Ao,{renderPriority:r,frames:o,camera:E},e,C.createElement("group",{onPointerOver:()=>null})),S,{events:{compute:t,priority:n}}),C.createElement("primitive",T.default({object:M.texture},g)),C.createElement("cubeCamera",{ref:E,args:[u,d,M],position:f,rotation:p,scale:h,quaternion:x,matrix:y,matrixAutoUpdate:v}))}));function Ao({frames:e,renderPriority:t,children:r,camera:n}){let o=0;return a.useFrame((t=>{(e===1/0||o<e)&&(n.current.update(t.gl,t.scene),o++)}),t),C.createElement(C.Fragment,null,r)}const Lo=C.forwardRef((({id:e=1,colorWrite:t=!1,depthWrite:r=!1,...n},a)=>{const o=C.useRef(null),i=C.useMemo((()=>({colorWrite:t,depthWrite:r,stencilWrite:!0,stencilRef:e,stencilFunc:R.AlwaysStencilFunc,stencilFail:R.ReplaceStencilOp,stencilZFail:R.ReplaceStencilOp,stencilZPass:R.ReplaceStencilOp})),[e,t,r]);return C.useLayoutEffect((()=>{Object.assign(o.current.material,i)})),C.useImperativeHandle(a,(()=>o.current),[]),C.createElement("mesh",T.default({ref:o,renderOrder:-e},n))}));function Io({api:e}){const t=new R.Vector3,r=new R.Quaternion,n=new R.Vector3,o=new R.Euler(0,Math.PI,0);return a.useFrame((a=>{a.camera.matrixWorld.decompose(t,r,n),e.current.camera.position.copy(t),e.current.camera.quaternion.setFromEuler(o).premultiply(r)})),null}const Bo=Te({blur:0,map:null,sdf:null,blend:0,size:0,resolution:new R.Vector2},"varying vec2 vUv;\n   void main() {\n     gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n     vUv = uv;\n   }",`uniform sampler2D sdf;\n   uniform sampler2D map;\n   uniform float blur;\n   uniform float size;\n   uniform float time;\n   uniform vec2 resolution;\n   varying vec2 vUv;\n   #include <packing>\n   void main() {\n     vec2 uv = gl_FragCoord.xy / resolution.xy;\n     vec4 t = texture2D(map, uv);\n     float k = blur;\n     float d = texture2D(sdf, vUv).r/size;\n     float alpha = 1.0 - smoothstep(0.0, 1.0, clamp(d/k + 1.0, 0.0, 1.0));\n     gl_FragColor = vec4(t.rgb, blur == 0.0 ? t.a : t.a * alpha);\n     #include <tonemapping_fragment>\n     #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n   }`),Vo=C.forwardRef((({children:e,events:t,blur:r=0,eventPriority:n=0,renderPriority:o=0,worldUnits:i=!1,resolution:s=512,...l},c)=>{a.extend({PortalMaterialImpl:Bo});const u=C.useRef(null),{scene:d,gl:m,size:f,viewport:p,setEvents:h}=a.useThree(),x=ot(s,s),[y,v]=C.useState(0);a.useFrame((()=>{const e=u.current.blend>0?Math.max(1,o):0;y!==e&&v(e)})),C.useEffect((()=>{void 0!==t&&h({enabled:!t})}),[t]);const[g,w]=C.useState(!0),z=xr(w);C.useLayoutEffect((()=>{var e;z.current=null==(e=u.current)?void 0:e.__r3f.parent}),[]),C.useLayoutEffect((()=>{if(z.current&&r&&null===u.current.sdf){const e=new R.Mesh(z.current.geometry,new R.MeshBasicMaterial),t=(new R.Box3).setFromBufferAttribute(e.geometry.attributes.position),r=new R.OrthographicCamera(t.min.x*(1+2/s),t.max.x*(1+2/s),t.max.y*(1+2/s),t.min.y*(1+2/s),.1,1e3);r.position.set(0,0,1),r.lookAt(0,0,0),m.setRenderTarget(x),m.render(e,r);const n=Oo(s,s,m)(x.texture),a=new Float32Array(s*s);m.readRenderTargetPixels(n,0,0,s,s,a);let o=1/0;for(let e=0;e<a.length;e++)a[e]<o&&(o=a[e]);o=-o,u.current.size=o,u.current.sdf=n.texture,m.setRenderTarget(null)}}),[s,r]),C.useImperativeHandle(c,(()=>u.current));const b=C.useCallback(((e,t,r)=>{var n;if(!z.current)return!1;if(t.pointer.set(e.offsetX/t.size.width*2-1,-e.offsetY/t.size.height*2+1),t.raycaster.setFromCamera(t.pointer,t.camera),0===(null==(n=u.current)?void 0:n.blend)){const[e]=t.raycaster.intersectObject(z.current);if(!e)return t.raycaster.camera=void 0,!1}}),[]);return C.createElement("portalMaterialImpl",T.default({ref:u,blur:r,blend:0,resolution:[f.width*p.dpr,f.height*p.dpr],attach:"material"},l),C.createElement(Fo,{attach:"map",frames:g?1/0:0,eventPriority:n,renderPriority:o,compute:b},e,C.createElement(Uo,{events:t,rootScene:d,priority:y,material:u,worldUnits:i})))}));function Uo({events:e,rootScene:t,material:r,priority:n,worldUnits:o}){const i=a.useThree((e=>e.scene)),s=a.useThree((e=>e.setEvents)),l=ot(),c=ot();C.useLayoutEffect((()=>{i.matrixAutoUpdate=!1}),[]),C.useEffect((()=>{void 0!==e&&s({enabled:e})}),[e]);const[d,m]=C.useMemo((()=>{const e={value:0};return[new u.FullScreenQuad(new R.ShaderMaterial({uniforms:{a:{value:l.texture},b:{value:c.texture},blend:e},vertexShader:"\n          varying vec2 vUv;\n          void main() {\n            vUv = uv;\n            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n          }",fragmentShader:`\n          uniform sampler2D a;\n          uniform sampler2D b;\n          uniform float blend;\n          varying vec2 vUv;\n          #include <packing>\n          void main() {\n            vec4 ta = texture2D(a, vUv);\n            vec4 tb = texture2D(b, vUv);\n            gl_FragColor = mix(tb, ta, blend);\n            #include <tonemapping_fragment>\n            #include <${Re>=154?"colorspace_fragment":"encodings_fragment"}>\n          }`})),e]}),[]);return a.useFrame((e=>{var a;let s=null==r||null==(a=r.current)?void 0:a.__r3f.parent;if(s){var u,f,p,h;if(o)i.matrixWorld.identity();else n&&1===(null==(u=r.current)?void 0:u.blend)&&s.updateWorldMatrix(!0,!1),i.matrixWorld.copy(s.matrixWorld);if(n)(null==(f=r.current)?void 0:f.blend)>0&&(null==(p=r.current)?void 0:p.blend)<1?(m.value=r.current.blend,e.gl.setRenderTarget(l),e.gl.render(i,e.camera),e.gl.setRenderTarget(c),e.gl.render(t,e.camera),e.gl.setRenderTarget(null),d.render(e.gl)):1===(null==(h=r.current)?void 0:h.blend)&&e.gl.render(i,e.camera)}}),n),C.createElement(C.Fragment,null)}const Oo=(e,t,r)=>{let n=new R.WebGLRenderTarget(e,t,{minFilter:R.LinearMipmapLinearFilter,magFilter:R.LinearFilter,type:R.FloatType,format:R.RedFormat,generateMipmaps:!0}),a=new R.WebGLRenderTarget(e,t,{minFilter:R.NearestFilter,magFilter:R.NearestFilter}),o=new R.WebGLRenderTarget(e,t,{minFilter:R.NearestFilter,magFilter:R.NearestFilter}),i=new R.WebGLRenderTarget(e,t,{minFilter:R.NearestFilter,magFilter:R.NearestFilter}),s=new R.WebGLRenderTarget(e,t,{minFilter:R.NearestFilter,magFilter:R.NearestFilter}),l=new R.WebGLRenderTarget(e,t,{minFilter:R.NearestFilter,magFilter:R.NearestFilter,type:R.FloatType,format:R.RedFormat}),c=new R.WebGLRenderTarget(e,t,{minFilter:R.NearestFilter,magFilter:R.NearestFilter,type:R.FloatType,format:R.RedFormat});const d=new u.FullScreenQuad(new R.ShaderMaterial({uniforms:{tex:{value:null}},vertexShader:"\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }",fragmentShader:"\n        uniform sampler2D tex;\n        varying vec2 vUv;\n        #include <packing>\n        void main() {\n          gl_FragColor = pack2HalfToRGBA(vUv * (round(texture2D(tex, vUv).x)));\n        }"})),m=new u.FullScreenQuad(new R.ShaderMaterial({uniforms:{tex:{value:null}},vertexShader:"\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }",fragmentShader:"\n        uniform sampler2D tex;\n        varying vec2 vUv;\n        #include <packing>\n        void main() {\n          gl_FragColor = pack2HalfToRGBA(vUv * (1.0 - round(texture2D(tex, vUv).x)));\n        }"})),f=new u.FullScreenQuad(new R.ShaderMaterial({uniforms:{tex:{value:null},offset:{value:0},level:{value:0},maxSteps:{value:0}},vertexShader:"\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }",fragmentShader:`\n        varying vec2 vUv;\n        uniform sampler2D tex;\n        uniform float offset;\n        uniform float level;\n        uniform float maxSteps;\n        #include <packing>\n        void main() {\n          float closestDist = 9999999.9;\n          vec2 closestPos = vec2(0.0);\n          for (float x = -1.0; x <= 1.0; x += 1.0) {\n            for (float y = -1.0; y <= 1.0; y += 1.0) {\n              vec2 voffset = vUv;\n              voffset += vec2(x, y) * vec2(${1/e}, ${1/t}) * offset;\n              vec2 pos = unpackRGBATo2Half(texture2D(tex, voffset));\n              float dist = distance(pos.xy, vUv);\n              if(pos.x != 0.0 && pos.y != 0.0 && dist < closestDist) {\n                closestDist = dist;\n                closestPos = pos;\n              }\n            }\n          }\n          gl_FragColor = pack2HalfToRGBA(closestPos);\n        }`})),p=new u.FullScreenQuad(new R.ShaderMaterial({uniforms:{tex:{value:null},size:{value:new R.Vector2(e,t)}},vertexShader:"\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }",fragmentShader:"\n        varying vec2 vUv;\n        uniform sampler2D tex;\n        uniform vec2 size;\n        #include <packing>\n        void main() {\n          gl_FragColor = vec4(distance(size * unpackRGBATo2Half(texture2D(tex, vUv)), size * vUv), 0.0, 0.0, 0.0);\n        }"})),h=new u.FullScreenQuad(new R.ShaderMaterial({uniforms:{inside:{value:c.texture},outside:{value:l.texture},tex:{value:null}},vertexShader:"\n        varying vec2 vUv;\n        void main() {\n          vUv = uv;\n          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }",fragmentShader:"\n        varying vec2 vUv;\n        uniform sampler2D inside;\n        uniform sampler2D outside;\n        uniform sampler2D tex;\n        #include <packing>\n        void main() {\n          float i = texture2D(inside, vUv).x;\n          float o =texture2D(outside, vUv).x;\n          if (texture2D(tex, vUv).x == 0.0) {\n            gl_FragColor = vec4(o, 0.0, 0.0, 0.0);\n          } else {\n            gl_FragColor = vec4(-i, 0.0, 0.0, 0.0);\n          }\n        }"}));return u=>{let x=n;u.minFilter=R.NearestFilter,u.magFilter=R.NearestFilter,d.material.uniforms.tex.value=u,r.setRenderTarget(a),d.render(r);const y=Math.ceil(Math.log(Math.max(e,t))/Math.log(2));let v=a,g=null;for(let e=0;e<y;e++){const t=Math.pow(2,y-e-1);g=v===a?i:a,f.material.uniforms.level.value=e,f.material.uniforms.maxSteps.value=y,f.material.uniforms.offset.value=t,f.material.uniforms.tex.value=v.texture,r.setRenderTarget(g),f.render(r),v=g}r.setRenderTarget(l),p.material.uniforms.tex.value=g.texture,p.render(r),m.material.uniforms.tex.value=u,r.setRenderTarget(o),m.render(r),v=o;for(let e=0;e<y;e++){const t=Math.pow(2,y-e-1);g=v===o?s:o,f.material.uniforms.level.value=e,f.material.uniforms.maxSteps.value=y,f.material.uniforms.offset.value=t,f.material.uniforms.tex.value=v.texture,r.setRenderTarget(g),f.render(r),v=g}return r.setRenderTarget(c),p.material.uniforms.tex.value=g.texture,p.render(r),r.setRenderTarget(x),h.material.uniforms.tex.value=u,h.render(r),r.setRenderTarget(null),x}},No=new R.Color,jo=A.default();function Wo(e){return"top"in e}function Go(e,t){const{right:r,top:n,left:a,bottom:o,width:i,height:s}=t,l=t.bottom<0||n>e.height||r<0||t.left>e.width;if(Wo(e)){const t=e.top+e.height-o;return{position:{width:i,height:s,left:a-e.left,top:n,bottom:t,right:r},isOffscreen:l}}return{position:{width:i,height:s,top:n,left:a,bottom:e.height-o,right:r},isOffscreen:l}}function Ho(e,{left:t,bottom:r,width:n,height:a}){let o;const i=n/a;var s;return(s=e.camera)&&s.isOrthographicCamera?e.camera.manual?e.camera.updateProjectionMatrix():e.camera.left===n/-2&&e.camera.right===n/2&&e.camera.top===a/2&&e.camera.bottom===a/-2||(Object.assign(e.camera,{left:n/-2,right:n/2,top:a/2,bottom:a/-2}),e.camera.updateProjectionMatrix()):e.camera.aspect!==i&&(e.camera.aspect=i,e.camera.updateProjectionMatrix()),o=e.gl.autoClear,e.gl.autoClear=!1,e.gl.setViewport(t,r,n,a),e.gl.setScissor(t,r,n,a),e.gl.setScissorTest(!0),o}function $o(e,t){e.gl.setScissorTest(!1),e.gl.autoClear=t}function qo(e){e.gl.getClearColor(No),e.gl.setClearColor(No,e.gl.getClearAlpha()),e.gl.clear(!0,!0)}function Xo({visible:e=!0,canvasSize:t,scene:r,index:n,children:o,frames:i,rect:s,track:l}){const c=a.useThree(),[u,d]=C.useState(!1);let m=0;return a.useFrame((n=>{var a;(i===1/0||m<=i)&&(l&&(s.current=null==(a=l.current)?void 0:a.getBoundingClientRect()),m++);if(s.current){const{position:a,isOffscreen:i}=Go(t,s.current);if(u!==i&&d(i),e&&!u&&s.current){const e=Ho(n,a);n.gl.render(o?n.scene:r,n.camera),$o(n,e)}}}),n),C.useLayoutEffect((()=>{const r=s.current;if(r&&(!e||!u)){const{position:e}=Go(t,r),n=Ho(c,e);qo(c),$o(c,n)}}),[e,u]),C.useEffect((()=>{if(!l)return;const e=s.current,r=c.get().events.connected;return c.setEvents({connected:l.current}),()=>{if(e){const{position:r}=Go(t,e),n=Ho(c,r);qo(c),$o(c,n)}c.setEvents({connected:r})}}),[l]),C.useEffect((()=>{Wo(t)||console.warn("Detected @react-three/fiber canvas size does not include position information. <View /> may not work as expected. Upgrade to @react-three/fiber ^8.1.0 for support.\n See https://github.com/pmndrs/drei/issues/944")}),[]),C.createElement(C.Fragment,null,o,C.createElement("group",{onPointerOver:()=>null}))}const Zo=C.forwardRef((({track:e,visible:t=!0,index:r=1,id:n,style:o,className:i,frames:s=1/0,children:l,...c},u)=>{var d,m,f,p;const h=C.useRef(null),{size:x,scene:y}=a.useThree(),[v]=C.useState((()=>new R.Scene)),[g,w]=C.useReducer((()=>!0),!1),z=C.useCallback(((t,r)=>{if(h.current&&e&&e.current&&t.target===e.current){const{width:e,height:n,left:a,top:o}=h.current,i=t.clientX-a,s=t.clientY-o;r.pointer.set(i/e*2-1,-s/n*2+1),r.raycaster.setFromCamera(r.pointer,r.camera)}}),[h,e]);return C.useEffect((()=>{var t;e&&(h.current=null==(t=e.current)?void 0:t.getBoundingClientRect()),w()}),[e]),C.createElement("group",T.default({ref:u},c),g&&a.createPortal(C.createElement(Xo,{visible:t,canvasSize:x,frames:s,scene:y,track:e,rect:h,index:r},l),v,{events:{compute:z,priority:r},size:{width:null==(d=h.current)?void 0:d.width,height:null==(m=h.current)?void 0:m.height,top:null==(f=h.current)?void 0:f.top,left:null==(p=h.current)?void 0:p.left}}))})),Yo=C.forwardRef((({as:e="div",id:t,visible:r,className:n,style:a,index:o=1,track:i,frames:s=1/0,children:l,...c},u)=>{const d=C.useId(),m=C.useRef(null);return C.useImperativeHandle(u,(()=>m.current)),C.createElement(C.Fragment,null,C.createElement(e,T.default({ref:m,id:t,className:n,style:a},c)),C.createElement(jo.In,null,C.createElement(Zo,{visible:r,key:d,track:m,frames:s,index:o},l)))})),Qo=(()=>{const e=C.forwardRef(((e,t)=>C.useContext(a.context)?C.createElement(Zo,T.default({ref:t},e)):C.createElement(Yo,T.default({ref:t},e))));return e.Port=()=>C.createElement(jo.Out,null),e})(),Ko=C.createContext(null),Jo=new R.Vector3,ei=new R.Vector3,ti=new R.Vector3(0,1,0),ri=new R.Matrix4,ni=({direction:e,axis:t})=>{const{translation:r,translationLimits:n,annotations:o,annotationsClass:i,depthTest:s,scale:l,lineWidth:c,fixed:u,axisColors:d,hoveredColor:m,opacity:f,onDragStart:p,onDrag:h,onDragEnd:x,userData:y}=C.useContext(Ko),v=a.useThree((e=>e.controls)),g=C.useRef(null),w=C.useRef(null),z=C.useRef(null),b=C.useRef(0),[E,M]=C.useState(!1),S=C.useCallback((n=>{o&&(g.current.innerText=`${r.current[t].toFixed(2)}`,g.current.style.display="block"),n.stopPropagation();const a=(new R.Matrix4).extractRotation(w.current.matrixWorld),i=n.point.clone(),s=(new R.Vector3).setFromMatrixPosition(w.current.matrixWorld),l=e.clone().applyMatrix4(a).normalize();z.current={clickPoint:i,dir:l},b.current=r.current[t],p({component:"Arrow",axis:t,origin:s,directions:[l]}),v&&(v.enabled=!1),n.target.setPointerCapture(n.pointerId)}),[o,e,v,p,r,t]),T=C.useCallback((e=>{if(e.stopPropagation(),E||M(!0),z.current){const{clickPoint:a,dir:i}=z.current,[s,l]=(null==n?void 0:n[t])||[void 0,void 0];let c=((e,t,r,n)=>{const a=t.dot(t),o=t.dot(e)-t.dot(r),i=t.dot(n);return 0===i?-o/a:(Jo.copy(n).multiplyScalar(a/i).sub(t),ei.copy(n).multiplyScalar(o/i).add(r).sub(e),-Jo.dot(ei)/Jo.dot(Jo))})(a,i,e.ray.origin,e.ray.direction);void 0!==s&&(c=Math.max(c,s-b.current)),void 0!==l&&(c=Math.min(c,l-b.current)),r.current[t]=b.current+c,o&&(g.current.innerText=`${r.current[t].toFixed(2)}`),ri.makeTranslation(i.x*c,i.y*c,i.z*c),h(ri)}}),[o,h,E,r,n,t]),P=C.useCallback((e=>{o&&(g.current.style.display="none"),e.stopPropagation(),z.current=null,x(),v&&(v.enabled=!0),e.target.releasePointerCapture(e.pointerId)}),[o,v,x]),D=C.useCallback((e=>{e.stopPropagation(),M(!1)}),[]),{cylinderLength:F,coneWidth:k,coneLength:_,matrixL:A}=C.useMemo((()=>{const t=u?c/l*1.6:l/20,r=u?.2:l/5,n=u?1-r:l-r,a=(new R.Quaternion).setFromUnitVectors(ti,e.clone().normalize());return{cylinderLength:n,coneWidth:t,coneLength:r,matrixL:(new R.Matrix4).makeRotationFromQuaternion(a)}}),[e,l,c,u]),L=E?m:d[t];return C.createElement("group",{ref:w},C.createElement("group",{matrix:A,matrixAutoUpdate:!1,onPointerDown:S,onPointerMove:T,onPointerUp:P,onPointerOut:D},o&&C.createElement(H,{position:[0,-_,0]},C.createElement("div",{style:{display:"none",background:"#151520",color:"white",padding:"6px 8px",borderRadius:7,whiteSpace:"nowrap"},className:i,ref:g})),C.createElement("mesh",{visible:!1,position:[0,(F+_)/2,0],userData:y},C.createElement("cylinderGeometry",{args:[1.4*k,1.4*k,F+_,8,1]})),C.createElement(me,{transparent:!0,raycast:()=>null,depthTest:s,points:[0,0,0,0,F,0],lineWidth:c,side:R.DoubleSide,color:L,opacity:f,polygonOffset:!0,renderOrder:1,polygonOffsetFactor:-10,fog:!1}),C.createElement("mesh",{raycast:()=>null,position:[0,F+_/2,0],renderOrder:500},C.createElement("coneGeometry",{args:[k,_,24,1]}),C.createElement("meshBasicMaterial",{transparent:!0,depthTest:s,color:L,opacity:f,polygonOffset:!0,polygonOffsetFactor:-10,fog:!1}))))},ai=new R.Vector3,oi=new R.Vector3,ii=e=>180*e/Math.PI,si=e=>{let t=((e,t)=>{let r=Math.floor(e/t);return r=r<0?r+1:r,e-r*t})(e,2*Math.PI);return Math.abs(t)<1e-6?0:(t<0&&(t+=2*Math.PI),t)},li=new R.Matrix4,ci=new R.Vector3,ui=new R.Ray,di=new R.Vector3,mi=({dir1:e,dir2:t,axis:r})=>{const{rotationLimits:n,annotations:o,annotationsClass:i,depthTest:s,scale:l,lineWidth:c,fixed:u,axisColors:d,hoveredColor:m,opacity:f,onDragStart:p,onDrag:h,onDragEnd:x,userData:y}=C.useContext(Ko),v=a.useThree((e=>e.controls)),g=C.useRef(null),w=C.useRef(null),z=C.useRef(0),b=C.useRef(0),E=C.useRef(null),[M,S]=C.useState(!1),T=C.useCallback((e=>{o&&(g.current.innerText=`${ii(b.current).toFixed(0)}º`,g.current.style.display="block"),e.stopPropagation();const t=e.point.clone(),n=(new R.Vector3).setFromMatrixPosition(w.current.matrixWorld),a=(new R.Vector3).setFromMatrixColumn(w.current.matrixWorld,0).normalize(),i=(new R.Vector3).setFromMatrixColumn(w.current.matrixWorld,1).normalize(),s=(new R.Vector3).setFromMatrixColumn(w.current.matrixWorld,2).normalize(),l=(new R.Plane).setFromNormalAndCoplanarPoint(s,n);E.current={clickPoint:t,origin:n,e1:a,e2:i,normal:s,plane:l},p({component:"Rotator",axis:r,origin:n,directions:[a,i,s]}),v&&(v.enabled=!1),e.target.setPointerCapture(e.pointerId)}),[o,v,p,r]),P=C.useCallback((e=>{if(e.stopPropagation(),M||S(!0),E.current){const{clickPoint:t,origin:a,e1:i,e2:s,normal:l,plane:c}=E.current,[u,d]=(null==n?void 0:n[r])||[void 0,void 0];ui.copy(e.ray),ui.intersectPlane(c,di),ui.direction.negate(),ui.intersectPlane(c,di);let m=((e,t,r,n,a)=>{ai.copy(e).sub(r),oi.copy(t).sub(r);const o=n.dot(n),i=a.dot(a),s=ai.dot(n)/o,l=ai.dot(a)/i,c=oi.dot(n)/o,u=oi.dot(a)/i,d=Math.atan2(l,s);return Math.atan2(u,c)-d})(t,di,a,i,s),f=ii(m);e.shiftKey&&(f=10*Math.round(f/10),m=(e=>e*Math.PI/180)(f)),void 0!==u&&void 0!==d&&d-u<2*Math.PI?(m=si(m),m=m>Math.PI?m-2*Math.PI:m,m=R.MathUtils.clamp(m,u-z.current,d-z.current),b.current=z.current+m):(b.current=si(z.current+m),b.current=b.current>Math.PI?b.current-2*Math.PI:b.current),o&&(f=ii(b.current),g.current.innerText=`${f.toFixed(0)}º`),li.makeRotationAxis(l,m),ci.copy(a).applyMatrix4(li).sub(a).negate(),li.setPosition(ci),h(li)}}),[o,h,M,n,r]),D=C.useCallback((e=>{o&&(g.current.style.display="none"),e.stopPropagation(),z.current=b.current,E.current=null,x(),v&&(v.enabled=!0),e.target.releasePointerCapture(e.pointerId)}),[o,v,x]),F=C.useCallback((e=>{e.stopPropagation(),S(!1)}),[]),k=C.useMemo((()=>{const r=e.clone().normalize(),n=t.clone().normalize();return(new R.Matrix4).makeBasis(r,n,r.clone().cross(n))}),[e,t]),_=u?.65:.65*l,A=C.useMemo((()=>{const e=[];for(let t=0;t<=32;t++){const r=t*(Math.PI/2)/32;e.push(new R.Vector3(Math.cos(r)*_,Math.sin(r)*_,0))}return e}),[_]);return C.createElement("group",{ref:w,onPointerDown:T,onPointerMove:P,onPointerUp:D,onPointerOut:F,matrix:k,matrixAutoUpdate:!1},o&&C.createElement(H,{position:[_,_,0]},C.createElement("div",{style:{display:"none",background:"#151520",color:"white",padding:"6px 8px",borderRadius:7,whiteSpace:"nowrap"},className:i,ref:g})),C.createElement(me,{points:A,lineWidth:4*c,visible:!1,userData:y}),C.createElement(me,{transparent:!0,raycast:()=>null,depthTest:s,points:A,lineWidth:c,side:R.DoubleSide,color:M?m:d[r],opacity:f,polygonOffset:!0,polygonOffsetFactor:-10,fog:!1}))},fi=new R.Ray,pi=new R.Vector3,hi=new R.Matrix4,xi=({dir1:e,dir2:t,axis:r})=>{const{translation:n,translationLimits:o,annotations:i,annotationsClass:s,depthTest:l,scale:c,lineWidth:u,fixed:d,axisColors:m,hoveredColor:f,opacity:p,onDragStart:h,onDrag:x,onDragEnd:y,userData:v}=C.useContext(Ko),g=a.useThree((e=>e.controls)),w=C.useRef(null),z=C.useRef(null),b=C.useRef(null),E=C.useRef(0),M=C.useRef(0),[S,T]=C.useState(!1),P=C.useCallback((e=>{i&&(w.current.innerText=`${n.current[(r+1)%3].toFixed(2)}, ${n.current[(r+2)%3].toFixed(2)}`,w.current.style.display="block"),e.stopPropagation();const t=e.point.clone(),a=(new R.Vector3).setFromMatrixPosition(z.current.matrixWorld),o=(new R.Vector3).setFromMatrixColumn(z.current.matrixWorld,0).normalize(),s=(new R.Vector3).setFromMatrixColumn(z.current.matrixWorld,1).normalize(),l=(new R.Vector3).setFromMatrixColumn(z.current.matrixWorld,2).normalize(),c=(new R.Plane).setFromNormalAndCoplanarPoint(l,a);b.current={clickPoint:t,e1:o,e2:s,plane:c},E.current=n.current[(r+1)%3],M.current=n.current[(r+2)%3],h({component:"Slider",axis:r,origin:a,directions:[o,s,l]}),g&&(g.enabled=!1),e.target.setPointerCapture(e.pointerId)}),[i,g,h,r]),D=C.useCallback((e=>{if(e.stopPropagation(),S||T(!0),b.current){const{clickPoint:t,e1:a,e2:s,plane:l}=b.current,[c,u]=(null==o?void 0:o[(r+1)%3])||[void 0,void 0],[d,m]=(null==o?void 0:o[(r+2)%3])||[void 0,void 0];fi.copy(e.ray),fi.intersectPlane(l,pi),fi.direction.negate(),fi.intersectPlane(l,pi),pi.sub(t);let[f,p]=((e,t,r)=>{const n=Math.abs(e.x)>=Math.abs(e.y)&&Math.abs(e.x)>=Math.abs(e.z)?0:Math.abs(e.y)>=Math.abs(e.x)&&Math.abs(e.y)>=Math.abs(e.z)?1:2,a=[0,1,2].sort(((e,r)=>Math.abs(t.getComponent(r))-Math.abs(t.getComponent(e)))),o=n===a[0]?a[1]:a[0],i=e.getComponent(n),s=e.getComponent(o),l=t.getComponent(n),c=t.getComponent(o),u=r.getComponent(n),d=(r.getComponent(o)-u*(s/i))/(c-l*(s/i));return[(u-d*l)/i,d]})(a,s,pi);void 0!==c&&(f=Math.max(f,c-E.current)),void 0!==u&&(f=Math.min(f,u-E.current)),void 0!==d&&(p=Math.max(p,d-M.current)),void 0!==m&&(p=Math.min(p,m-M.current)),n.current[(r+1)%3]=E.current+f,n.current[(r+2)%3]=M.current+p,i&&(w.current.innerText=`${n.current[(r+1)%3].toFixed(2)}, ${n.current[(r+2)%3].toFixed(2)}`),hi.makeTranslation(f*a.x+p*s.x,f*a.y+p*s.y,f*a.z+p*s.z),x(hi)}}),[i,x,S,n,o,r]),F=C.useCallback((e=>{i&&(w.current.style.display="none"),e.stopPropagation(),b.current=null,y(),g&&(g.enabled=!0),e.target.releasePointerCapture(e.pointerId)}),[i,g,y]),k=C.useCallback((e=>{e.stopPropagation(),T(!1)}),[]),_=C.useMemo((()=>{const r=e.clone().normalize(),n=t.clone().normalize();return(new R.Matrix4).makeBasis(r,n,r.clone().cross(n))}),[e,t]),A=d?1/7:c/7,L=d?.225:.225*c,I=S?f:m[r],B=C.useMemo((()=>[new R.Vector3(0,0,0),new R.Vector3(0,L,0),new R.Vector3(L,L,0),new R.Vector3(L,0,0),new R.Vector3(0,0,0)]),[L]);return C.createElement("group",{ref:z,matrix:_,matrixAutoUpdate:!1},i&&C.createElement(H,{position:[0,0,0]},C.createElement("div",{style:{display:"none",background:"#151520",color:"white",padding:"6px 8px",borderRadius:7,whiteSpace:"nowrap"},className:s,ref:w})),C.createElement("group",{position:[1.7*A,1.7*A,0]},C.createElement("mesh",{visible:!0,onPointerDown:P,onPointerMove:D,onPointerUp:F,onPointerOut:k,scale:L,userData:v},C.createElement("planeGeometry",null),C.createElement("meshBasicMaterial",{transparent:!0,depthTest:l,color:I,polygonOffset:!0,polygonOffsetFactor:-10,side:R.DoubleSide,fog:!1})),C.createElement(me,{position:[-L/2,-L/2,0],transparent:!0,depthTest:l,points:B,lineWidth:u,color:I,opacity:p,polygonOffset:!0,polygonOffsetFactor:-10,userData:v,fog:!1})))},yi=new R.Vector3,vi=new R.Vector3,gi=new R.Vector3(0,1,0),wi=new R.Vector3,zi=new R.Matrix4,bi=({direction:e,axis:t})=>{const{scaleLimits:r,annotations:n,annotationsClass:o,depthTest:i,scale:s,lineWidth:l,fixed:c,axisColors:u,hoveredColor:d,opacity:m,onDragStart:f,onDrag:p,onDragEnd:h,userData:x}=C.useContext(Ko),y=a.useThree((e=>e.size)),v=a.useThree((e=>e.controls)),g=C.useRef(null),w=C.useRef(null),z=C.useRef(null),b=C.useRef(1),E=C.useRef(1),M=C.useRef(null),[S,T]=C.useState(!1),P=c?1.2:1.2*s,D=C.useCallback((r=>{n&&(g.current.innerText=`${E.current.toFixed(2)}`,g.current.style.display="block"),r.stopPropagation();const a=(new R.Matrix4).extractRotation(w.current.matrixWorld),o=r.point.clone(),i=(new R.Vector3).setFromMatrixPosition(w.current.matrixWorld),l=e.clone().applyMatrix4(a).normalize(),u=w.current.matrixWorld.clone(),d=u.clone().invert(),m=c?1/ce(w.current.getWorldPosition(yi),s,r.camera,y):1;M.current={clickPoint:o,dir:l,mPLG:u,mPLGInv:d,offsetMultiplier:m},f({component:"Sphere",axis:t,origin:i,directions:[l]}),v&&(v.enabled=!1),r.target.setPointerCapture(r.pointerId)}),[n,v,e,f,t,c,s,y]),F=C.useCallback((e=>{if(e.stopPropagation(),S||T(!0),M.current){const{clickPoint:a,dir:o,mPLG:i,mPLGInv:l,offsetMultiplier:u}=M.current,[d,m]=(null==r?void 0:r[t])||[1e-5,void 0],f=((e,t,r,n)=>{const a=t.dot(t),o=t.dot(e)-t.dot(r),i=t.dot(n);return 0===i?-o/a:(yi.copy(n).multiplyScalar(a/i).sub(t),vi.copy(n).multiplyScalar(o/i).add(r).sub(e),-yi.dot(vi)/yi.dot(yi))})(a,o,e.ray.origin,e.ray.direction),h=f*u,x=c?h:h/s;let y=Math.pow(2,.2*x);e.shiftKey&&(y=Math.round(10*y)/10),y=Math.max(y,d/b.current),void 0!==m&&(y=Math.min(y,m/b.current)),E.current=b.current*y,z.current.position.set(0,P+h,0),n&&(g.current.innerText=`${E.current.toFixed(2)}`),wi.set(1,1,1),wi.setComponent(t,y),zi.makeScale(wi.x,wi.y,wi.z).premultiply(i).multiply(l),p(zi)}}),[n,P,p,S,r,t]),k=C.useCallback((e=>{n&&(g.current.style.display="none"),e.stopPropagation(),b.current=E.current,M.current=null,z.current.position.set(0,P,0),h(),v&&(v.enabled=!0),e.target.releasePointerCapture(e.pointerId)}),[n,v,h,P]),_=C.useCallback((e=>{e.stopPropagation(),T(!1)}),[]),{radius:A,matrixL:L}=C.useMemo((()=>{const t=c?l/s*1.8:s/22.5,r=(new R.Quaternion).setFromUnitVectors(gi,e.clone().normalize());return{radius:t,matrixL:(new R.Matrix4).makeRotationFromQuaternion(r)}}),[e,s,l,c]),I=S?d:u[t];return C.createElement("group",{ref:w},C.createElement("group",{matrix:L,matrixAutoUpdate:!1,onPointerDown:D,onPointerMove:F,onPointerUp:k,onPointerOut:_},n&&C.createElement(H,{position:[0,P/2,0]},C.createElement("div",{style:{display:"none",background:"#151520",color:"white",padding:"6px 8px",borderRadius:7,whiteSpace:"nowrap"},className:o,ref:g})),C.createElement("mesh",{ref:z,position:[0,P,0],renderOrder:500,userData:x},C.createElement("sphereGeometry",{args:[A,12,12]}),C.createElement("meshBasicMaterial",{transparent:!0,depthTest:i,color:I,opacity:m,polygonOffset:!0,polygonOffsetFactor:-10}))))},Ei=new R.Matrix4,Mi=new R.Matrix4,Si=new R.Matrix4,Ti=new R.Matrix4,Ci=new R.Matrix4,Pi=new R.Matrix4,Ri=new R.Matrix4,Di=new R.Matrix4,Fi=new R.Matrix4,ki=new R.Box3,_i=new R.Box3,Ai=new R.Vector3,Li=new R.Vector3,Ii=new R.Vector3,Bi=new R.Vector3,Vi=new R.Vector3,Ui=new R.Vector3(1,0,0),Oi=new R.Vector3(0,1,0),Ni=new R.Vector3(0,0,1),ji=C.forwardRef((({enabled:e=!0,matrix:t,onDragStart:r,onDrag:n,onDragEnd:o,autoTransform:i=!0,anchor:s,disableAxes:l=!1,disableSliders:c=!1,disableRotations:u=!1,disableScaling:d=!1,activeAxes:m=[!0,!0,!0],offset:f=[0,0,0],rotation:p=[0,0,0],scale:h=1,lineWidth:x=4,fixed:y=!1,translationLimits:v,rotationLimits:g,scaleLimits:w,depthTest:z=!0,axisColors:b=["#ff2060","#20df80","#2080ff"],hoveredColor:E="#ffff40",annotations:M=!1,annotationsClass:S,opacity:P=1,visible:D=!0,userData:F,children:k,..._},A)=>{const L=a.useThree((e=>e.invalidate)),I=C.useRef(null),B=C.useRef(null),V=C.useRef(null),U=C.useRef(null),O=C.useRef([0,0,0]),N=C.useRef(new R.Vector3(1,1,1)),j=C.useRef(new R.Vector3(1,1,1));C.useLayoutEffect((()=>{s&&(U.current.updateWorldMatrix(!0,!0),Ti.copy(U.current.matrixWorld).invert(),ki.makeEmpty(),U.current.traverse((e=>{e.geometry&&(e.geometry.boundingBox||e.geometry.computeBoundingBox(),Pi.copy(e.matrixWorld).premultiply(Ti),_i.copy(e.geometry.boundingBox),_i.applyMatrix4(Pi),ki.union(_i))})),Ai.copy(ki.max).add(ki.min).multiplyScalar(.5),Li.copy(ki.max).sub(ki.min).multiplyScalar(.5),Ii.copy(Li).multiply(new R.Vector3(...s)).add(Ai),Bi.set(...f).add(Ii),V.current.position.copy(Bi),L())}));const W=C.useMemo((()=>({onDragStart:e=>{Ei.copy(B.current.matrix),Mi.copy(B.current.matrixWorld),r&&r(e),L()},onDrag:e=>{Si.copy(I.current.matrixWorld),Ti.copy(Si).invert(),Ci.copy(Mi).premultiply(e),Pi.copy(Ci).premultiply(Ti),Ri.copy(Ei).invert(),Di.copy(Pi).multiply(Ri),i&&B.current.matrix.copy(Pi),n&&n(Pi,Di,Ci,e),L()},onDragEnd:()=>{o&&o(),L()},translation:O,translationLimits:v,rotationLimits:g,axisColors:b,hoveredColor:E,opacity:P,scale:h,lineWidth:x,fixed:y,depthTest:z,userData:F,annotations:M,annotationsClass:S})),[r,n,o,O,v,g,w,z,h,x,y,...b,E,P,F,i,M,S]),G=new R.Vector3;return a.useFrame((e=>{if(y){const t=ce(V.current.getWorldPosition(G),h,e.camera,e.size);N.current.setScalar(t)}t&&t instanceof R.Matrix4&&(B.current.matrix=t),B.current.updateWorldMatrix(!0,!0),Fi.makeRotationFromEuler(V.current.rotation).setPosition(V.current.position).premultiply(B.current.matrixWorld),j.current.setFromMatrixScale(Fi),Vi.copy(N.current).divide(j.current),(Math.abs(V.current.scale.x-Vi.x)>1e-4||Math.abs(V.current.scale.y-Vi.y)>1e-4||Math.abs(V.current.scale.z-Vi.z)>1e-4)&&(V.current.scale.copy(Vi),e.invalidate())})),C.useImperativeHandle(A,(()=>B.current),[]),C.createElement(Ko.Provider,{value:W},C.createElement("group",{ref:I},C.createElement("group",T.default({ref:B,matrix:t,matrixAutoUpdate:!1},_),C.createElement("group",{visible:D,ref:V,position:f,rotation:p},e&&C.createElement(C.Fragment,null,!l&&m[0]&&C.createElement(ni,{axis:0,direction:Ui}),!l&&m[1]&&C.createElement(ni,{axis:1,direction:Oi}),!l&&m[2]&&C.createElement(ni,{axis:2,direction:Ni}),!c&&m[0]&&m[1]&&C.createElement(xi,{axis:2,dir1:Ui,dir2:Oi}),!c&&m[0]&&m[2]&&C.createElement(xi,{axis:1,dir1:Ni,dir2:Ui}),!c&&m[2]&&m[1]&&C.createElement(xi,{axis:0,dir1:Oi,dir2:Ni}),!u&&m[0]&&m[1]&&C.createElement(mi,{axis:2,dir1:Ui,dir2:Oi}),!u&&m[0]&&m[2]&&C.createElement(mi,{axis:1,dir1:Ni,dir2:Ui}),!u&&m[2]&&m[1]&&C.createElement(mi,{axis:0,dir1:Oi,dir2:Ni}),!d&&m[0]&&C.createElement(bi,{axis:0,direction:Ui}),!d&&m[1]&&C.createElement(bi,{axis:1,direction:Oi}),!d&&m[2]&&C.createElement(bi,{axis:2,direction:Ni}))),C.createElement("group",{ref:U},k))))})),Wi=t.forwardRef((({options:e={video:!0},...r},n)=>{const a=f.suspend((()=>navigator.mediaDevices.getDisplayMedia(e)),[]);return t.useEffect((()=>()=>{null==a||a.getTracks().forEach((e=>e.stop())),f.clear([])}),[a]),C.createElement(or,T.default({ref:n},r,{src:a}))})),Gi=t.forwardRef((({constraints:e={audio:!1,video:{facingMode:"user"}},...r},n)=>{const a=f.suspend((()=>navigator.mediaDevices.getUserMedia(e)),[]);return t.useEffect((()=>()=>{null==a||a.getTracks().forEach((e=>e.stop())),f.clear([])}),[a]),C.createElement(or,T.default({ref:n},r,{src:a}))})),Hi=new R.Vector3(0,0,-1),$i=function(){const e=new R.Vector3,t=new R.Vector3,r=new R.Vector3,n=new R.Vector3,a=new R.Vector3;return function(o,i,s,l){return e.copy(o),t.copy(i),r.copy(s),n.copy(t).sub(e),a.copy(r).sub(e),l.crossVectors(a,n).normalize()}}();const qi=C.forwardRef((({points:e=Yi.SAMPLE_FACELANDMARKER_RESULT.faceLandmarks[0],face:t,facialTransformationMatrix:r,faceBlendshapes:n,offset:o,offsetScalar:i=80,width:s,height:l,depth:c=1,verticalTri:u=[159,386,152],origin:d,eyes:m=!0,eyesAsOrigin:f=!1,debug:p=!1,children:h,...x},y)=>{var v;t&&(e=t.keypoints,console.warn("Facemesh `face` prop is deprecated: use `points` instead"));const g=C.useRef(null),w=C.useRef(null),z=C.useRef(null),b=C.useRef(null),E=C.useRef(null),M=C.useRef(null),S=C.useRef(null),[T]=C.useState((()=>new R.Vector3)),[P]=C.useState((()=>new R.Object3D)),[D]=C.useState((()=>new R.Quaternion)),[F]=C.useState((()=>new R.Vector3)),{invalidate:k}=a.useThree();C.useEffect((()=>{var e;null==(e=E.current)||e.geometry.setIndex(Yi.TRIANGULATION)}),[]);const[_]=C.useState((()=>new R.Vector3));C.useEffect((()=>{var t,a;const h=null==(t=E.current)?void 0:t.geometry;if(!h)return;var x,y;(h.setFromPoints(e),h.setDrawRange(0,Yi.TRIANGULATION.length),r)?(P.matrix.fromArray(r.data),P.matrix.decompose(P.position,P.quaternion,P.scale),P.rotation.y*=-1,P.rotation.z*=-1,D.setFromEuler(P.rotation),o?(P.position.y*=-1,P.position.z*=-1,null==(x=g.current)||x.position.copy(P.position.divideScalar(i))):null==(y=g.current)||y.position.set(0,0,0)):($i(e[u[0]],e[u[1]],e[u[2]],T),D.setFromUnitVectors(Hi,T));const v=D.clone().invert();if(h.computeBoundingBox(),p&&k(),h.center(),h.applyQuaternion(v),null==(a=b.current)||a.setRotationFromQuaternion(D),m)if(n){if(M.current&&S.current&&z.current)if(f){const e=M.current._computeSphere(h),t=S.current._computeSphere(h),r=function(e,t){return e.clone().add(t).multiplyScalar(.5)}(e.center,t.center);d=r.negate(),M.current._update(h,n,e),S.current._update(h,n,t)}else M.current._update(h,n),S.current._update(h,n)}else console.warn("Facemesh `eyes` option only works if `faceBlendshapes` is provided: skipping.");if(z.current){if(void 0!==d)if("number"==typeof d){const e=h.getAttribute("position");F.set(-e.getX(d),-e.getY(d),-e.getZ(d))}else d.isVector3&&F.copy(d);else F.setScalar(0);z.current.position.copy(F)}if(w.current){let e=1;(s||l||c)&&(h.boundingBox.getSize(_),s&&(e=s/_.x),l&&(e=l/_.y),c&&(e=c/_.z)),w.current.scale.setScalar(1!==e?e:1)}h.computeVertexNormals(),h.attributes.position.needsUpdate=!0}),[e,r,n,P,o,i,s,l,c,u,d,m,p,k,T,D,_,F]);const A=C.useMemo((()=>({outerRef:b,meshRef:E,eyeRightRef:M,eyeLeftRef:S})),[]);C.useImperativeHandle(y,(()=>A),[A]);const[L]=C.useState((()=>new R.Vector3)),I=null==(v=E.current)?void 0:v.geometry.boundingBox,B=(null==I?void 0:I.getSize(L).z)||1;return C.createElement("group",x,C.createElement("group",{ref:g},C.createElement("group",{ref:b},C.createElement("group",{ref:w},p?C.createElement(C.Fragment,null,C.createElement("axesHelper",{args:[B]}),C.createElement(me,{points:[[0,0,0],[0,0,-B]],color:65535})):null,C.createElement("group",{ref:z},m&&n&&C.createElement("group",{name:"eyes"},C.createElement(Zi,{side:"left",ref:M,debug:p}),C.createElement(Zi,{side:"right",ref:S,debug:p})),C.createElement("mesh",{ref:E,name:"face"},h,p?C.createElement(C.Fragment,null,I&&C.createElement("box3Helper",{args:[I]})):null))))))})),Xi={contourLandmarks:{right:[33,133,159,145,153],left:[263,362,386,374,380]},blendshapes:{right:[14,16,18,12],left:[13,15,17,11]},color:{right:"red",left:"#00ff00"},fov:{horizontal:100,vertical:90}},Zi=C.forwardRef((({side:e,debug:t=!0},r)=>{const n=C.useRef(null),a=C.useRef(null),[o]=C.useState((()=>new R.Sphere)),i=C.useCallback((t=>{const r=t.getAttribute("position"),n=Xi.contourLandmarks[e].map((e=>new R.Vector3(r.getX(e),r.getY(e),r.getZ(e))));return o.center.set(0,0,0),n.forEach((e=>o.center.add(e))),o.center.divideScalar(n.length),o.radius=n[0].sub(n[1]).length()/2,o}),[o,e]),[s]=C.useState((()=>new R.Euler)),l=C.useCallback(((t,r,o)=>{var l;n.current&&(null!==(l=o)&&void 0!==l||(o=i(t)),n.current.position.copy(o.center),n.current.scale.setScalar(o.radius));if(r&&a.current){const t=Xi.blendshapes[e],n=r.categories[t[0]].score,o=r.categories[t[1]].score,i=r.categories[t[2]].score,l=r.categories[t[3]].score,c=.5*(Xi.fov.horizontal*R.MathUtils.DEG2RAD)*(l-i),u=.5*(Xi.fov.vertical*R.MathUtils.DEG2RAD)*(n-o)*("left"===e?1:-1);s.set(c,u,0),a.current.setRotationFromEuler(s)}}),[i,e,s]),c=C.useMemo((()=>({eyeMeshRef:n,irisDirRef:a,_computeSphere:i,_update:l})),[i,l]);C.useImperativeHandle(r,(()=>c),[c]);const u=Xi.color[e];return C.createElement("group",null,C.createElement("group",{ref:n},t&&C.createElement("axesHelper",null),C.createElement("group",{ref:a},C.createElement(C.Fragment,null,t&&C.createElement(me,{points:[[0,0,0],[0,0,-2]],lineWidth:1,color:u})))))})),Yi={TRIANGULATION:[127,34,139,11,0,37,232,231,120,72,37,39,128,121,47,232,121,128,104,69,67,175,171,148,157,154,155,118,50,101,73,39,40,9,151,108,48,115,131,194,204,211,74,40,185,80,42,183,40,92,186,230,229,118,202,212,214,83,18,17,76,61,146,160,29,30,56,157,173,106,204,194,135,214,192,203,165,98,21,71,68,51,45,4,144,24,23,77,146,91,205,50,187,201,200,18,91,106,182,90,91,181,85,84,17,206,203,36,148,171,140,92,40,39,193,189,244,159,158,28,247,246,161,236,3,196,54,68,104,193,168,8,117,228,31,189,193,55,98,97,99,126,47,100,166,79,218,155,154,26,209,49,131,135,136,150,47,126,217,223,52,53,45,51,134,211,170,140,67,69,108,43,106,91,230,119,120,226,130,247,63,53,52,238,20,242,46,70,156,78,62,96,46,53,63,143,34,227,173,155,133,123,117,111,44,125,19,236,134,51,216,206,205,154,153,22,39,37,167,200,201,208,36,142,100,57,212,202,20,60,99,28,158,157,35,226,113,160,159,27,204,202,210,113,225,46,43,202,204,62,76,77,137,123,116,41,38,72,203,129,142,64,98,240,49,102,64,41,73,74,212,216,207,42,74,184,169,170,211,170,149,176,105,66,69,122,6,168,123,147,187,96,77,90,65,55,107,89,90,180,101,100,120,63,105,104,93,137,227,15,86,85,129,102,49,14,87,86,55,8,9,100,47,121,145,23,22,88,89,179,6,122,196,88,95,96,138,172,136,215,58,172,115,48,219,42,80,81,195,3,51,43,146,61,171,175,199,81,82,38,53,46,225,144,163,110,246,33,7,52,65,66,229,228,117,34,127,234,107,108,69,109,108,151,48,64,235,62,78,191,129,209,126,111,35,143,163,161,246,117,123,50,222,65,52,19,125,141,221,55,65,3,195,197,25,7,33,220,237,44,70,71,139,122,193,245,247,130,33,71,21,162,153,158,159,170,169,150,188,174,196,216,186,92,144,160,161,2,97,167,141,125,241,164,167,37,72,38,12,145,159,160,38,82,13,63,68,71,226,35,111,158,153,154,101,50,205,206,92,165,209,198,217,165,167,97,220,115,218,133,112,243,239,238,241,214,135,169,190,173,133,171,208,32,125,44,237,86,87,178,85,86,179,84,85,180,83,84,181,201,83,182,137,93,132,76,62,183,61,76,184,57,61,185,212,57,186,214,207,187,34,143,156,79,239,237,123,137,177,44,1,4,201,194,32,64,102,129,213,215,138,59,166,219,242,99,97,2,94,141,75,59,235,24,110,228,25,130,226,23,24,229,22,23,230,26,22,231,112,26,232,189,190,243,221,56,190,28,56,221,27,28,222,29,27,223,30,29,224,247,30,225,238,79,20,166,59,75,60,75,240,147,177,215,20,79,166,187,147,213,112,233,244,233,128,245,128,114,188,114,217,174,131,115,220,217,198,236,198,131,134,177,132,58,143,35,124,110,163,7,228,110,25,356,389,368,11,302,267,452,350,349,302,303,269,357,343,277,452,453,357,333,332,297,175,152,377,384,398,382,347,348,330,303,304,270,9,336,337,278,279,360,418,262,431,304,408,409,310,415,407,270,409,410,450,348,347,422,430,434,313,314,17,306,307,375,387,388,260,286,414,398,335,406,418,364,367,416,423,358,327,251,284,298,281,5,4,373,374,253,307,320,321,425,427,411,421,313,18,321,405,406,320,404,405,315,16,17,426,425,266,377,400,369,322,391,269,417,465,464,386,257,258,466,260,388,456,399,419,284,332,333,417,285,8,346,340,261,413,441,285,327,460,328,355,371,329,392,439,438,382,341,256,429,420,360,364,394,379,277,343,437,443,444,283,275,440,363,431,262,369,297,338,337,273,375,321,450,451,349,446,342,467,293,334,282,458,461,462,276,353,383,308,324,325,276,300,293,372,345,447,382,398,362,352,345,340,274,1,19,456,248,281,436,427,425,381,256,252,269,391,393,200,199,428,266,330,329,287,273,422,250,462,328,258,286,384,265,353,342,387,259,257,424,431,430,342,353,276,273,335,424,292,325,307,366,447,345,271,303,302,423,266,371,294,455,460,279,278,294,271,272,304,432,434,427,272,407,408,394,430,431,395,369,400,334,333,299,351,417,168,352,280,411,325,319,320,295,296,336,319,403,404,330,348,349,293,298,333,323,454,447,15,16,315,358,429,279,14,15,316,285,336,9,329,349,350,374,380,252,318,402,403,6,197,419,318,319,325,367,364,365,435,367,397,344,438,439,272,271,311,195,5,281,273,287,291,396,428,199,311,271,268,283,444,445,373,254,339,263,466,249,282,334,296,449,347,346,264,447,454,336,296,299,338,10,151,278,439,455,292,407,415,358,371,355,340,345,372,390,249,466,346,347,280,442,443,282,19,94,370,441,442,295,248,419,197,263,255,359,440,275,274,300,383,368,351,412,465,263,467,466,301,368,389,380,374,386,395,378,379,412,351,419,436,426,322,373,390,388,2,164,393,370,462,461,164,0,267,302,11,12,374,373,387,268,12,13,293,300,301,446,261,340,385,384,381,330,266,425,426,423,391,429,355,437,391,327,326,440,457,438,341,382,362,459,457,461,434,430,394,414,463,362,396,369,262,354,461,457,316,403,402,315,404,403,314,405,404,313,406,405,421,418,406,366,401,361,306,408,407,291,409,408,287,410,409,432,436,410,434,416,411,264,368,383,309,438,457,352,376,401,274,275,4,421,428,262,294,327,358,433,416,367,289,455,439,462,370,326,2,326,370,305,460,455,254,449,448,255,261,446,253,450,449,252,451,450,256,452,451,341,453,452,413,464,463,441,413,414,258,442,441,257,443,442,259,444,443,260,445,444,467,342,445,459,458,250,289,392,290,290,328,460,376,433,435,250,290,392,411,416,433,341,463,464,453,464,465,357,465,412,343,412,399,360,363,440,437,399,456,420,456,363,401,435,288,372,383,353,339,255,249,448,261,255,133,243,190,133,155,112,33,246,247,33,130,25,398,384,286,362,398,414,362,463,341,263,359,467,263,249,255,466,467,260,75,60,166,238,239,79,162,127,139,72,11,37,121,232,120,73,72,39,114,128,47,233,232,128,103,104,67,152,175,148,173,157,155,119,118,101,74,73,40,107,9,108,49,48,131,32,194,211,184,74,185,191,80,183,185,40,186,119,230,118,210,202,214,84,83,17,77,76,146,161,160,30,190,56,173,182,106,194,138,135,192,129,203,98,54,21,68,5,51,4,145,144,23,90,77,91,207,205,187,83,201,18,181,91,182,180,90,181,16,85,17,205,206,36,176,148,140,165,92,39,245,193,244,27,159,28,30,247,161,174,236,196,103,54,104,55,193,8,111,117,31,221,189,55,240,98,99,142,126,100,219,166,218,112,155,26,198,209,131,169,135,150,114,47,217,224,223,53,220,45,134,32,211,140,109,67,108,146,43,91,231,230,120,113,226,247,105,63,52,241,238,242,124,46,156,95,78,96,70,46,63,116,143,227,116,123,111,1,44,19,3,236,51,207,216,205,26,154,22,165,39,167,199,200,208,101,36,100,43,57,202,242,20,99,56,28,157,124,35,113,29,160,27,211,204,210,124,113,46,106,43,204,96,62,77,227,137,116,73,41,72,36,203,142,235,64,240,48,49,64,42,41,74,214,212,207,183,42,184,210,169,211,140,170,176,104,105,69,193,122,168,50,123,187,89,96,90,66,65,107,179,89,180,119,101,120,68,63,104,234,93,227,16,15,85,209,129,49,15,14,86,107,55,9,120,100,121,153,145,22,178,88,179,197,6,196,89,88,96,135,138,136,138,215,172,218,115,219,41,42,81,5,195,51,57,43,61,208,171,199,41,81,38,224,53,225,24,144,110,105,52,66,118,229,117,227,34,234,66,107,69,10,109,151,219,48,235,183,62,191,142,129,126,116,111,143,7,163,246,118,117,50,223,222,52,94,19,141,222,221,65,196,3,197,45,220,44,156,70,139,188,122,245,139,71,162,145,153,159,149,170,150,122,188,196,206,216,92,163,144,161,164,2,167,242,141,241,0,164,37,11,72,12,144,145,160,12,38,13,70,63,71,31,226,111,157,158,154,36,101,205,203,206,165,126,209,217,98,165,97,237,220,218,237,239,241,210,214,169,140,171,32,241,125,237,179,86,178,180,85,179,181,84,180,182,83,181,194,201,182,177,137,132,184,76,183,185,61,184,186,57,185,216,212,186,192,214,187,139,34,156,218,79,237,147,123,177,45,44,4,208,201,32,98,64,129,192,213,138,235,59,219,141,242,97,97,2,141,240,75,235,229,24,228,31,25,226,230,23,229,231,22,230,232,26,231,233,112,232,244,189,243,189,221,190,222,28,221,223,27,222,224,29,223,225,30,224,113,247,225,99,60,240,213,147,215,60,20,166,192,187,213,243,112,244,244,233,245,245,128,188,188,114,174,134,131,220,174,217,236,236,198,134,215,177,58,156,143,124,25,110,7,31,228,25,264,356,368,0,11,267,451,452,349,267,302,269,350,357,277,350,452,357,299,333,297,396,175,377,381,384,382,280,347,330,269,303,270,151,9,337,344,278,360,424,418,431,270,304,409,272,310,407,322,270,410,449,450,347,432,422,434,18,313,17,291,306,375,259,387,260,424,335,418,434,364,416,391,423,327,301,251,298,275,281,4,254,373,253,375,307,321,280,425,411,200,421,18,335,321,406,321,320,405,314,315,17,423,426,266,396,377,369,270,322,269,413,417,464,385,386,258,248,456,419,298,284,333,168,417,8,448,346,261,417,413,285,326,327,328,277,355,329,309,392,438,381,382,256,279,429,360,365,364,379,355,277,437,282,443,283,281,275,363,395,431,369,299,297,337,335,273,321,348,450,349,359,446,467,283,293,282,250,458,462,300,276,383,292,308,325,283,276,293,264,372,447,346,352,340,354,274,19,363,456,281,426,436,425,380,381,252,267,269,393,421,200,428,371,266,329,432,287,422,290,250,328,385,258,384,446,265,342,386,387,257,422,424,430,445,342,276,422,273,424,306,292,307,352,366,345,268,271,302,358,423,371,327,294,460,331,279,294,303,271,304,436,432,427,304,272,408,395,394,431,378,395,400,296,334,299,6,351,168,376,352,411,307,325,320,285,295,336,320,319,404,329,330,349,334,293,333,366,323,447,316,15,315,331,358,279,317,14,316,8,285,9,277,329,350,253,374,252,319,318,403,351,6,419,324,318,325,397,367,365,288,435,397,278,344,439,310,272,311,248,195,281,375,273,291,175,396,199,312,311,268,276,283,445,390,373,339,295,282,296,448,449,346,356,264,454,337,336,299,337,338,151,294,278,455,308,292,415,429,358,355,265,340,372,388,390,466,352,346,280,295,442,282,354,19,370,285,441,295,195,248,197,457,440,274,301,300,368,417,351,465,251,301,389,385,380,386,394,395,379,399,412,419,410,436,322,387,373,388,326,2,393,354,370,461,393,164,267,268,302,12,386,374,387,312,268,13,298,293,301,265,446,340,380,385,381,280,330,425,322,426,391,420,429,437,393,391,326,344,440,438,458,459,461,364,434,394,428,396,262,274,354,457,317,316,402,316,315,403,315,314,404,314,313,405,313,421,406,323,366,361,292,306,407,306,291,408,291,287,409,287,432,410,427,434,411,372,264,383,459,309,457,366,352,401,1,274,4,418,421,262,331,294,358,435,433,367,392,289,439,328,462,326,94,2,370,289,305,455,339,254,448,359,255,446,254,253,449,253,252,450,252,256,451,256,341,452,414,413,463,286,441,414,286,258,441,258,257,442,257,259,443,259,260,444,260,467,445,309,459,250,305,289,290,305,290,460,401,376,435,309,250,392,376,411,433,453,341,464,357,453,465,343,357,412,437,343,399,344,360,440,420,437,456,360,420,363,361,401,288,265,372,353,390,339,249,339,448,255],SAMPLE_FACE:{keypoints:[{x:356.2804412841797,y:295.1960563659668,z:-23.786449432373047,name:"lips"},{x:354.8859405517578,y:264.69520568847656,z:-36.718435287475586},{x:355.2180862426758,y:275.3360366821289,z:-21.183712482452393},{x:347.349853515625,y:242.4400234222412,z:-25.093655586242676},{x:354.40135955810547,y:256.67933464050293,z:-38.23572635650635},{x:353.7689971923828,y:247.54886627197266,z:-34.5475435256958},{x:352.1288299560547,y:227.34312057495117,z:-13.095386028289795},{x:303.5013198852539,y:234.67002868652344,z:12.500141859054565,name:"rightEye"},{x:351.09378814697266,y:211.87547206878662,z:-6.413471698760986},{x:350.7115936279297,y:202.1251630783081,z:-6.413471698760986},{x:348.33667755126953,y:168.7741756439209,z:6.483500003814697,name:"faceOval"},{x:356.4806365966797,y:299.2995357513428,z:-23.144519329071045},{x:356.5511703491211,y:302.66146659851074,z:-21.020312309265137},{x:356.6239547729492,y:304.1536331176758,z:-18.137459754943848,name:"lips"},{x:356.5807342529297,y:305.1840591430664,z:-18.767719268798828,name:"lips"},{x:356.8241500854492,y:308.25711250305176,z:-20.16829490661621},{x:357.113037109375,y:312.26277351379395,z:-22.10575819015503},{x:357.34962463378906,y:317.1123218536377,z:-21.837315559387207,name:"lips"},{x:357.6658630371094,y:325.51036834716797,z:-16.27002477645874},{x:355.0201416015625,y:269.36279296875,z:-33.73054027557373},{x:348.5237503051758,y:270.33411026000977,z:-24.93025302886963},{x:279.97331619262695,y:213.24176788330078,z:47.759642601013184,name:"faceOval"},{x:322.66529083251953,y:238.5027265548706,z:5.535193085670471},{x:316.0983657836914,y:239.94489669799805,z:5.777376294136047},{x:309.9431610107422,y:240.24518966674805,z:7.510589361190796},{x:301.31994247436523,y:237.86138534545898,z:13.118728399276733},{x:328.14266204833984,y:235.80496788024902,z:6.646900177001953},{x:313.7326431274414,y:222.11161136627197,z:3.9887237548828125},{x:320.45196533203125,y:221.87729358673096,z:4.601476192474365},{x:307.35679626464844,y:223.63793849945068,z:5.932023525238037},{x:303.0031204223633,y:226.3743782043457,z:8.479321002960205},{x:296.80023193359375,y:242.94299125671387,z:15.931552648544312},{x:332.2352981567383,y:340.77341079711914,z:-10.165848731994629},{x:301.38587951660156,y:233.46447944641113,z:14.764405488967896,name:"rightEye"},{x:279.0147018432617,y:244.37155723571777,z:45.77549457550049},{x:289.60548400878906,y:239.1807460784912,z:23.191204071044922},{x:320.32257080078125,y:267.1292781829834,z:-4.954537749290466},{x:347.64583587646484,y:294.4955062866211,z:-23.062820434570312,name:"lips"},{x:349.28138732910156,y:303.1095886230469,z:-20.238323211669922},{x:338.9453125,y:298.19186210632324,z:-19.456336498260498,name:"lips"},{x:333.36788177490234,y:302.6706790924072,z:-14.776077270507812,name:"lips"},{x:342.89188385009766,y:304.3561363220215,z:-17.752301692962646},{x:337.7375030517578,y:306.0098361968994,z:-13.410515785217285},{x:325.6159210205078,y:316.22995376586914,z:-6.681914925575256},{x:349.0104675292969,y:264.9818515777588,z:-36.274919509887695},{x:347.7138900756836,y:257.5664806365967,z:-37.67549514770508},{x:291.79357528686523,y:218.88171672821045,z:11.578094959259033,name:"rightEyebrow"},{x:332.2689437866211,y:247.56946563720703,z:-3.3730539679527283},{x:332.0074462890625,y:267.1201229095459,z:-19.969879388809204},{x:331.27952575683594,y:263.6967658996582,z:-17.47218608856201},{x:301.04373931884766,y:269.56552505493164,z:3.61815482378006},{x:347.4863815307617,y:249.0706443786621,z:-32.633421421051025},{x:307.26118087768555,y:208.2646894454956,z:1.1591226607561111,name:"rightEyebrow"},{x:297.91919708251953,y:212.22604751586914,z:5.914516448974609,name:"rightEyebrow"},{x:285.1651382446289,y:197.98450469970703,z:36.391637325286865,name:"faceOval"},{x:337.04097747802734,y:211.25229835510254,z:-4.548954665660858},{x:326.5912628173828,y:223.16698551177979,z:6.670243740081787},{x:320.05664825439453,y:309.5834255218506,z:-4.055835008621216},{x:289.6866226196289,y:314.617395401001,z:53.875489234924316,name:"faceOval"},{x:337.4256896972656,y:270.8755302429199,z:-17.67060160636902},{x:343.69922637939453,y:273.0000400543213,z:-18.756048679351807},{x:327.4242401123047,y:309.22399520874023,z:-4.703601002693176,name:"lips"},{x:330.37220001220703,y:308.3323001861572,z:-6.442649960517883},{x:293.87027740478516,y:207.7961826324463,z:9.821539521217346,name:"rightEyebrow"},{x:332.11437225341797,y:271.22812271118164,z:-16.64351224899292},{x:320.1197814941406,y:207.40366458892822,z:-2.48164564371109,name:"rightEyebrow"},{x:318.59575271606445,y:201.07443809509277,z:-3.110446035861969,name:"rightEyebrow"},{x:310.72303771972656,y:175.75075149536133,z:13.328815698623657,name:"faceOval"},{x:289.67578887939453,y:202.29835510253906,z:21.370456218719482},{x:315.30879974365234,y:187.35260009765625,z:5.0304025411605835},{x:287.8936767578125,y:216.54793739318848,z:17.81065821647644,name:"rightEyebrow"},{x:283.9391899108887,y:215.01142501831055,z:32.04984903335571},{x:348.35330963134766,y:299.4155788421631,z:-22.47924566268921},{x:341.1790466308594,y:301.8221855163574,z:-18.977805376052856},{x:335.69713592529297,y:304.4266891479492,z:-14.682706594467163},{x:339.4615173339844,y:272.3654365539551,z:-16.38674020767212},{x:328.99600982666016,y:308.86685371398926,z:-5.616893768310547},{x:332.00313568115234,y:309.1875743865967,z:-10.335084199905396},{x:331.0068130493164,y:307.9274368286133,z:-6.681914925575256,name:"lips"},{x:341.13792419433594,y:266.4876937866211,z:-26.56425952911377},{x:339.02950286865234,y:305.6663703918457,z:-12.33674168586731,name:"lips"},{x:344.22935485839844,y:304.9452781677246,z:-15.161235332489014,name:"lips"},{x:350.1844024658203,y:304.374303817749,z:-17.5305438041687,name:"lips"},{x:348.52630615234375,y:325.9562301635742,z:-16.164982318878174},{x:348.6581802368164,y:317.1624183654785,z:-21.510512828826904,name:"lips"},{x:348.9766311645508,y:312.1923065185547,z:-21.708929538726807},{x:349.2427444458008,y:308.0660820007324,z:-19.643079042434692},{x:349.67491149902344,y:305.42747497558594,z:-18.16080331802368,name:"lips"},{x:337.95589447021484,y:306.6535949707031,z:-12.803598642349243,name:"lips"},{x:337.06878662109375,y:307.63169288635254,z:-14.274203777313232},{x:335.77449798583984,y:309.8449516296387,z:-15.698124170303345},{x:334.6099090576172,y:312.7997016906738,z:-14.764405488967896,name:"lips"},{x:327.2330856323242,y:293.80866050720215,z:-11.864047050476074},{x:280.97679138183594,y:279.79928970336914,z:68.90834331512451,name:"faceOval"},{x:355.13843536376953,y:271.7875671386719,z:-25.350427627563477},{x:334.7235870361328,y:307.4656391143799,z:-9.302158951759338,name:"lips"},{x:333.5293960571289,y:307.89782524108887,z:-10.200862884521484},{x:346.29688262939453,y:276.4256286621094,z:-19.748122692108154},{x:335.16246795654297,y:276.22097969055176,z:-12.313398122787476},{x:345.09132385253906,y:274.7082996368408,z:-19.304605722427368},{x:325.4267883300781,y:252.95130729675293,z:-1.6661019623279572},{x:315.347843170166,y:259.05200958251953,z:-.25604281574487686},{x:330.44933319091797,y:267.7570152282715,z:-14.017432928085327},{x:294.96768951416016,y:185.26001930236816,z:23.903164863586426,name:"faceOval"},{x:299.63531494140625,y:192.7913761138916,z:12.640198469161987},{x:304.5452117919922,y:202.4142837524414,z:3.244667649269104,name:"rightEyebrow"},{x:331.6915512084961,y:320.0467872619629,z:-10.632705688476562},{x:334.5911407470703,y:201.27566814422607,z:-6.133356094360352,name:"rightEyebrow"},{x:331.4815902709961,y:185.44180870056152,z:.6627205014228821},{x:328.05816650390625,y:170.8385467529297,z:7.358860373497009,name:"faceOval"},{x:304.49764251708984,y:239.76297855377197,z:10.387605428695679},{x:290.6382179260254,y:248.85257720947266,z:19.03616428375244},{x:331.5682601928711,y:233.20727348327637,z:7.837390303611755},{x:295.5115509033203,y:228.9834451675415,z:14.41426157951355},{x:336.94332122802734,y:241.8259334564209,z:-5.27842104434967},{x:336.2792205810547,y:262.7049922943115,z:-26.12074375152588},{x:284.4102478027344,y:255.3262710571289,z:25.467140674591064},{x:295.1420593261719,y:253.02655220031738,z:12.430112361907959},{x:303.5196113586426,y:254.20703887939453,z:6.139191389083862},{x:315.73450088500977,y:251.64799690246582,z:3.3788898587226868},{x:324.69661712646484,y:247.56494522094727,z:2.3328344523906708},{x:331.57970428466797,y:243.02241325378418,z:1.1423448473215103},{x:345.6210708618164,y:229.9976634979248,z:-10.825285911560059},{x:286.26644134521484,y:270.37991523742676,z:21.708929538726807},{x:290.2525520324707,y:228.4921360015869,z:17.71728754043579},{x:351.65367126464844,y:269.3400764465332,z:-33.450424671173096},{x:333.1378936767578,y:253.88388633728027,z:-7.230473756790161},{x:277.8318977355957,y:246.95331573486328,z:68.20805549621582,name:"faceOval"},{x:336.6680908203125,y:238.10003757476807,z:.7688578963279724},{x:329.95800018310547,y:269.18323516845703,z:-7.207130789756775},{x:299.17491912841797,y:234.13324356079102,z:15.95489501953125},{x:335.61729431152344,y:258.71752738952637,z:-23.016133308410645},{x:284.1079330444336,y:297.0343494415283,z:63.25934886932373,name:"faceOval"},{x:331.44542694091797,y:230.6892442703247,z:9.92658257484436,name:"rightEye"},{x:341.41536712646484,y:253.01264762878418,z:-29.038610458374023},{x:303.5472869873047,y:327.5896739959717,z:16.725212335586548},{x:304.7756576538086,y:337.4389457702637,z:27.38126277923584,name:"faceOval"},{x:280.80501556396484,y:275.32050132751465,z:45.0752067565918},{x:295.43582916259766,y:318.4501647949219,z:26.2608003616333},{x:281.4303207397461,y:228.7355661392212,z:40.94350814819336},{x:331.2549591064453,y:349.4216537475586,z:-7.376367449760437},{x:352.4247741699219,y:271.7330074310303,z:-24.953596591949463},{x:327.5672912597656,y:260.41900634765625,z:-5.456410646438599},{x:284.5432472229004,y:241.7647933959961,z:29.668869972229004},{x:310,y:235.66174507141113,z:8.502663969993591,name:"rightEye"},{x:315.7071113586426,y:235.7572603225708,z:6.938687562942505,name:"rightEye"},{x:330.41088104248047,y:311.04143142700195,z:-9.325502514839172,name:"lips"},{x:288.5377502441406,y:285.31983375549316,z:21.837315559387207},{x:344.55039978027344,y:359.4300842285156,z:-6.705257892608643,name:"faceOval"},{x:323.41880798339844,y:351.67362213134766,z:7.802375555038452,name:"faceOval"},{x:314.64088439941406,y:346.11894607543945,z:16.36339783668518,name:"faceOval"},{x:349.4945526123047,y:184.8434829711914,z:-.21847527474164963},{x:359.24694061279297,y:359.8348903656006,z:-8.403456211090088,name:"faceOval"},{x:321.26182556152344,y:234.64492321014404,z:6.90950870513916,name:"rightEye"},{x:326.318359375,y:232.90250301361084,z:8.029969334602356,name:"rightEye"},{x:329.6211624145508,y:231.6195774078369,z:9.722331762313843,name:"rightEye"},{x:285.9398078918457,y:228.2351303100586,z:24.650139808654785},{x:325.79288482666016,y:227.88007736206055,z:7.469738721847534,name:"rightEye"},{x:320.1699447631836,y:227.5934886932373,z:6.168370842933655,name:"rightEye"},{x:314.85408782958984,y:227.85282611846924,z:6.2675780057907104,name:"rightEye"},{x:309.3084907531738,y:229.1516876220703,z:7.7031683921813965,name:"rightEye"},{x:305.5621337890625,y:230.92366218566895,z:9.722331762313843,name:"rightEye"},{x:277.8681945800781,y:228.5354232788086,z:59.71122741699219,name:"faceOval"},{x:306.1444664001465,y:235.1954698562622,z:10.603528022766113,name:"rightEye"},{x:355.4478454589844,y:281.96210861206055,z:-20.565123558044434},{x:333.02661895751953,y:288.0105400085449,z:-14.72939133644104},{x:337.15728759765625,y:269.2059516906738,z:-19.8414945602417},{x:345.9898376464844,y:283.5453128814697,z:-20.4834246635437},{x:351.48963928222656,y:219.98916149139404,z:-7.0378947257995605},{x:312.39574432373047,y:336.50628089904785,z:8.671900033950806},{x:321.32152557373047,y:343.1755256652832,z:.9067271649837494},{x:343.78379821777344,y:353.2975959777832,z:-14.355905055999756},{x:296.8791389465332,y:327.91497230529785,z:41.01353645324707,name:"faceOval"},{x:329.6939468383789,y:229.27897453308105,z:8.934508562088013,name:"rightEye"},{x:341.6905212402344,y:241.4073657989502,z:-14.589333534240723},{x:359.03079986572266,y:353.48859786987305,z:-15.803166627883911},{x:333.1861877441406,y:356.43213272094727,z:-1.0234417766332626,name:"faceOval"},{x:283.97483825683594,y:291.4318656921387,z:41.94725513458252},{x:343.33770751953125,y:305.830135345459,z:-15.756480693817139,name:"lips"},{x:342.40283966064453,y:307.7453899383545,z:-17.4021577835083},{x:341.53621673583984,y:311.0595703125,z:-19.047834873199463},{x:340.9107208251953,y:315.4837703704834,z:-18.5576331615448,name:"lips"},{x:339.1478729248047,y:323.42233657836914,z:-14.367576837539673},{x:333.3201599121094,y:307.4406337738037,z:-9.617288708686829},{x:331.2411117553711,y:306.9811820983887,z:-9.669809937477112},{x:329.23255920410156,y:306.0508346557617,z:-9.582273960113525,name:"lips"},{x:322.4586486816406,y:301.33323669433594,z:-7.720675468444824},{x:297.1712112426758,y:286.9552803039551,z:8.240055441856384},{x:341.3060760498047,y:235.4432201385498,z:-7.504753470420837},{x:336.9318389892578,y:224.3451976776123,z:5.829898118972778},{x:332.65323638916016,y:226.70403957366943,z:8.105834126472473},{x:334.67357635498047,y:306.4397621154785,z:-8.981193900108337,name:"lips"},{x:297.4601936340332,y:306.29210472106934,z:15.476365089416504},{x:342.9119110107422,y:222.37077713012695,z:-2.754466235637665},{x:335.4629898071289,y:332.20250129699707,z:-11.823196411132812},{x:353.2412338256836,y:240.56339263916016,z:-27.147831916809082},{x:346.3080596923828,y:236.41446590423584,z:-18.452589511871338},{x:352.6475143432617,y:234.1420555114746,z:-19.748122692108154},{x:337.3209762573242,y:253.39937210083008,z:-16.024924516677856},{x:358.6122131347656,y:344.90861892700195,z:-18.592647314071655},{x:358.1117248535156,y:334.64990615844727,z:-17.49552845954895},{x:346.4450454711914,y:335.0321102142334,z:-16.32838249206543},{x:319.17640686035156,y:320.2833938598633,z:-3.276764452457428},{x:325.2540588378906,y:276.2369728088379,z:-6.460157036781311},{x:326.7214584350586,y:327.3939514160156,z:-7.417217493057251},{x:310.7190132141113,y:277.2265148162842,z:-3.5452082753181458},{x:319.78355407714844,y:284.8238182067871,z:-6.4543211460113525},{x:305.773983001709,y:290.83580017089844,z:.06907138042151928},{x:344.4001770019531,y:344.85408782958984,z:-16.946970224380493},{x:333.1879425048828,y:258.74256134033203,z:-11.90489649772644},{x:313.80598068237305,y:327.08919525146484,z:2.2277912497520447},{x:322.9637908935547,y:334.6819496154785,z:-3.3643004298210144},{x:313.4055519104004,y:311.2166690826416,z:-1.1175429821014404},{x:291.0865783691406,y:298.2831001281738,z:22.467575073242188},{x:305.6580924987793,y:313.3707904815674,z:5.561453700065613},{x:288.23760986328125,y:305.9941864013672,z:36.765122413635254},{x:315.10692596435547,y:296.26991271972656,z:-4.604393839836121},{x:337.50518798828125,y:247.5944423675537,z:-10.597691535949707},{x:338.8450622558594,y:265.47778129577637,z:-27.778091430664062},{x:334.25254821777344,y:269.0671920776367,z:-20.938611030578613},{x:341.64512634277344,y:259.6387195587158,z:-32.189905643463135},{x:331.44081115722656,y:219.0976095199585,z:4.207563698291779},{x:320.56339263916016,y:216.49658203125,z:2.930997312068939},{x:311.21912002563477,y:216.57853603363037,z:2.9674705862998962},{x:303.46256256103516,y:218.54614734649658,z:5.357203483581543},{x:297.99999237060547,y:222.505202293396,z:9.325502514839172},{x:294.93839263916016,y:236.39654159545898,z:18.534289598464966},{x:278.87489318847656,y:259.7095584869385,z:45.68212032318115},{x:300.3782653808594,y:245.38593292236328,z:12.278382778167725},{x:307.06348419189453,y:246.36857986450195,z:8.164191246032715},{x:315.5229187011719,y:245.3949737548828,z:5.503097176551819},{x:323.71395111083984,y:242.75178909301758,z:4.6335723996162415},{x:330.2785873413086,y:239.34658527374268,z:4.937030673027039},{x:334.6982192993164,y:236.0460376739502,z:4.823233783245087},{x:279.3412208557129,y:263.5196113586426,z:70.91583728790283,name:"faceOval"},{x:334.65972900390625,y:271.6648578643799,z:-17.775644063949585},{x:342.05677032470703,y:246.99846267700195,z:-20.84523916244507},{x:344.0357971191406,y:264.5701503753662,z:-32.936880588531494},{x:348.25531005859375,y:268.6645030975342,z:-30.695960521697998},{x:344.12227630615234,y:266.34212493896484,z:-29.808926582336426},{x:337.12318420410156,y:274.2556858062744,z:-15.768152475357056},{x:349.49047088623047,y:269.071683883667,z:-32.51670837402344},{x:350.1683044433594,y:271.4691352844238,z:-24.93025302886963},{x:333.9634704589844,y:230.56639194488525,z:8.89949381351471},{x:338.2147979736328,y:231.4807891845703,z:4.6715047955513},{x:340.4712677001953,y:231.74463272094727,z:-.34996166825294495},{x:303.28975677490234,y:232.24980354309082,z:11.916568279266357,name:"rightEye"},{x:299.4649124145508,y:229.53842639923096,z:12.325069904327393},{x:359.09618377685547,y:241.77349090576172,z:-24.650139808654785},{x:399.46216583251953,y:229.89503860473633,z:15.919880867004395,name:"leftEye"},{x:361.38919830322266,y:269.6129894256592,z:-24.510080814361572},{x:416.9973373413086,y:206.0895538330078,z:53.26857566833496,name:"faceOval"},{x:381.32179260253906,y:235.5476474761963,z:7.6214683055877686},{x:387.8068542480469,y:236.25958442687988,z:8.345099091529846},{x:393.95751953125,y:235.8660364151001,z:10.475142002105713},{x:401.84600830078125,y:232.77019500732422,z:16.760226488113403},{x:375.70568084716797,y:233.48456382751465,z:8.234220147132874},{x:388.17752838134766,y:218.94717693328857,z:6.810300946235657},{x:381.64928436279297,y:219.2656660079956,z:6.711093783378601},{x:394.4760513305664,y:219.66821193695068,z:9.173773527145386},{x:398.8843536376953,y:221.8837022781372,z:12.03328251838684},{x:406.5454864501953,y:237.12156772613525,z:19.7131085395813},{x:383.87447357177734,y:337.6932907104492,z:-8.631049990653992},{x:401.2682342529297,y:228.5916566848755,z:18.359217643737793,name:"leftEye"},{x:422.0449447631836,y:236.73934936523438,z:51.16771221160889},{x:412.69153594970703,y:232.80198097229004,z:27.52131938934326},{x:387.3497772216797,y:263.298397064209,z:-2.8609684109687805},{x:364.5124053955078,y:293.39221000671387,z:-22.397546768188477,name:"lips"},{x:363.62987518310547,y:302.1291446685791,z:-19.643079042434692},{x:373.2334518432617,y:295.8647060394287,z:-18.125789165496826,name:"lips"},{x:378.83365631103516,y:299.5177745819092,z:-13.153743743896484,name:"lips"},{x:369.91477966308594,y:302.5704002380371,z:-16.65518283843994},{x:374.9167251586914,y:303.5416603088379,z:-11.963253021240234},{x:387.58888244628906,y:312.2716999053955,z:-4.680258631706238},{x:360.6635284423828,y:264.31986808776855,z:-35.94811677932739},{x:361.04564666748047,y:256.8225860595703,z:-37.278664112091064},{x:408.3855438232422,y:213.52088928222656,z:15.756480693817139,name:"leftEyebrow"},{x:373.2946014404297,y:245.38101196289062,z:-1.9316278398036957},{x:376.83860778808594,y:264.3721103668213,z:-18.510947227478027},{x:376.9546127319336,y:261.0010528564453,z:-15.989909172058105},{x:406.1498260498047,y:263.5030174255371,z:7.072908878326416},{x:360.07205963134766,y:248.3631706237793,z:-32.16656446456909},{x:393.11119079589844,y:205.10473251342773,z:3.7786373496055603,name:"leftEyebrow"},{x:402.12791442871094,y:207.89000988006592,z:9.383859634399414,name:"leftEyebrow"},{x:410.8693313598633,y:191.6182279586792,z:41.27030849456787,name:"faceOval"},{x:364.9509811401367,y:210.40483474731445,z:-3.758212625980377},{x:375.94444274902344,y:221.1331844329834,z:8.368442058563232},{x:392.1904754638672,y:305.0360298156738,z:-1.752179116010666},{x:419.50225830078125,y:307.25592613220215,z:58.96425247192383,name:"faceOval"},{x:372.0027160644531,y:268.7212657928467,z:-16.631840467453003},{x:366.1614227294922,y:271.6237449645996,z:-18.219159841537476},{x:385.00938415527344,y:305.3863334655762,z:-2.567722797393799},{x:381.99771881103516,y:304.9723720550537,z:-4.575215280056},{x:405.078125,y:203.21216583251953,z:13.713973760604858,name:"leftEyebrow"},{x:377.13207244873047,y:268.4710121154785,z:-15.266278982162476},{x:380.9713363647461,y:205.36980628967285,z:-.7250899076461792,name:"leftEyebrow"},{x:381.7788314819336,y:198.9268398284912,z:-1.184653863310814,name:"leftEyebrow"},{x:385.5204772949219,y:172.1484375,z:16.04826807975769,name:"faceOval"},{x:407.94189453125,y:196.76236152648926,z:25.723915100097656},{x:383.03890228271484,y:184.5157527923584,z:7.393874526023865},{x:411.61781311035156,y:210.79241752624512,z:22.315845489501953,name:"leftEyebrow"},{x:414.30870056152344,y:208.4643030166626,z:37.021894454956055},{x:364.28722381591797,y:298.35777282714844,z:-21.86065673828125},{x:371.3682556152344,y:299.78848457336426,z:-17.834001779556274},{x:376.88201904296875,y:301.6696071624756,z:-13.153743743896484},{x:370.2193832397461,y:270.49095153808594,z:-15.569736957550049},{x:383.5081100463867,y:305.2726364135742,z:-3.673594295978546},{x:380.73760986328125,y:305.96869468688965,z:-8.660228252410889},{x:381.2334442138672,y:304.63574409484863,z:-4.820316135883331,name:"lips"},{x:368.1698989868164,y:264.8884963989258,z:-25.653886795043945},{x:373.5087203979492,y:303.4233856201172,z:-10.95950722694397,name:"lips"},{x:368.4544372558594,y:303.29601287841797,z:-14.169161319732666,name:"lips"},{x:362.76554107666016,y:303.5735607147217,z:-16.911956071853638,name:"lips"},{x:366.60980224609375,y:324.8870658874512,z:-15.616422891616821},{x:365.7067108154297,y:315.95678329467773,z:-20.903596878051758,name:"lips"},{x:365.0083923339844,y:311.2232208251953,z:-21.066999435424805},{x:364.1508102416992,y:307.0583438873291,z:-18.907777070999146},{x:363.37512969970703,y:304.5721435546875,z:-17.42550015449524,name:"lips"},{x:374.580078125,y:304.3059539794922,z:-11.40302300453186,name:"lips"},{x:375.55362701416016,y:305.0998020172119,z:-12.861957550048828},{x:377.2437286376953,y:307.1674346923828,z:-14.215847253799438},{x:378.68587493896484,y:309.9015712738037,z:-13.223772048950195,name:"lips"},{x:383.8992691040039,y:290.29629707336426,z:-9.97326910495758},{x:423.3871841430664,y:271.91688537597656,z:74.37058925628662,name:"faceOval"},{x:377.68043518066406,y:304.62209701538086,z:-7.603961229324341,name:"lips"},{x:379.00428771972656,y:304.9314594268799,z:-8.57852816581726},{x:364.00279998779297,y:275.2813911437988,z:-19.25792098045349},{x:374.68231201171875,y:273.82555961608887,z:-11.28047227859497},{x:365.0354766845703,y:273.4548568725586,z:-18.791062831878662},{x:380.61901092529297,y:249.8848056793213,z:.15501167625188828},{x:391.14158630371094,y:254.7934627532959,z:2.0906515419483185},{x:378.1761169433594,y:264.9612236022949,z:-12.605184316635132},{x:400.9540557861328,y:179.99592304229736,z:27.82477855682373,name:"faceOval"},{x:398.0038833618164,y:188.50656509399414,z:16.094952821731567},{x:394.8717498779297,y:199.0359592437744,z:6.226727366447449,name:"leftEyebrow"},{x:382.10926055908203,y:316.83926582336426,z:-8.946179747581482},{x:366.51588439941406,y:200.32583713531494,z:-5.24632453918457,name:"leftEyebrow"},{x:367.4893569946289,y:183.87210845947266,z:1.9039081037044525},{x:368.6243438720703,y:168.8127565383911,z:8.736093044281006,name:"faceOval"},{x:398.96175384521484,y:234.9675178527832,z:13.713973760604858},{x:412.9645538330078,y:242.23042488098145,z:23.272905349731445},{x:372.05257415771484,y:231.41919136047363,z:9.226294755935669},{x:406.0722351074219,y:223.58965873718262,z:18.370890617370605},{x:368.27442169189453,y:240.2039337158203,z:-4.166713654994965},{x:372.3575210571289,y:260.66442489624023,z:-24.976940155029297},{x:419.2244338989258,y:247.9079246520996,z:30.299127101898193},{x:409.43885803222656,y:246.60913467407227,z:16.398411989212036},{x:401.69139862060547,y:248.76328468322754,z:9.395531415939331},{x:389.7608184814453,y:247.56915092468262,z:5.841569304466248},{x:380.5461883544922,y:244.55984115600586,z:4.263003468513489},{x:373.25817108154297,y:240.80214500427246,z:2.5356262922286987},{x:358.77086639404297,y:229.35615062713623,z:-10.387605428695679},{x:419.5793914794922,y:262.8478717803955,z:26.5175724029541},{x:410.8808898925781,y:222.51372814178467,z:22.199130058288574},{x:358.45714569091797,y:268.91467094421387,z:-33.17030906677246},{x:373.4129333496094,y:251.6385841369629,z:-5.771540403366089},{x:422.5408172607422,y:239.23919677734375,z:74.04378890991211,name:"faceOval"},{x:367.8171920776367,y:236.58040523529053,z:1.820748895406723},{x:378.51959228515625,y:266.2532329559326,z:-5.74819803237915},{x:403.3472442626953,y:229.05112266540527,z:19.689764976501465},{x:372.34840393066406,y:256.6451168060303,z:-21.872329711914062},{x:422.54566192626953,y:289.1587829589844,z:68.67491245269775,name:"faceOval"},{x:371.9297409057617,y:228.90116214752197,z:11.432201862335205,name:"leftEye"},{x:366.21360778808594,y:251.6158962249756,z:-28.19826364517212},{x:409.1571807861328,y:321.3156223297119,z:20.2266526222229},{x:408.52943420410156,y:331.44238471984863,z:31.09278917312622,name:"faceOval"},{x:424.2788314819336,y:267.1992301940918,z:50.467424392700195},{x:415.60352325439453,y:311.6528606414795,z:30.579242706298828},{x:418.12793731689453,y:221.59927368164062,z:46.26569747924805},{x:385.68286895751953,y:346.0184955596924,z:-5.70151150226593},{x:357.82936096191406,y:271.3758373260498,z:-24.836881160736084},{x:379.588623046875,y:257.5071716308594,z:-3.755294680595398},{x:417.4592590332031,y:234.71948146820068,z:34.5475435256958},{x:393.4684371948242,y:231.58967971801758,z:11.408859491348267,name:"leftEye"},{x:387.8864288330078,y:232.14245796203613,z:9.51808214187622,name:"leftEye"},{x:382.4981689453125,y:307.5654888153076,z:-7.522260546684265,name:"lips"},{x:419.00169372558594,y:277.8332805633545,z:26.424202919006348},{x:373.62953186035156,y:357.6375102996826,z:-5.75986921787262,name:"faceOval"},{x:392.8708267211914,y:347.72446632385254,z:10.154176950454712,name:"faceOval"},{x:400.3953552246094,y:341.0005187988281,z:19.39797878265381,name:"faceOval"},{x:382.25440979003906,y:231.66935920715332,z:8.998700976371765,name:"leftEye"},{x:377.14550018310547,y:230.4228687286377,z:9.804032444953918,name:"leftEye"},{x:373.8358688354492,y:229.64950561523438,z:11.292144060134888,name:"leftEye"},{x:414.5794677734375,y:221.67891025543213,z:29.412097930908203},{x:377.00672149658203,y:225.66201210021973,z:9.360517263412476,name:"leftEye"},{x:382.29530334472656,y:224.8431158065796,z:8.32175612449646,name:"leftEye"},{x:387.5133514404297,y:224.49507236480713,z:8.917000889778137,name:"leftEye"},{x:393.15906524658203,y:225.24795055389404,z:10.737749338150024,name:"leftEye"},{x:397.05554962158203,y:226.55359268188477,z:13.002015352249146,name:"leftEye"},{x:420.5299377441406,y:221.014666557312,z:65.40690422058105,name:"faceOval"},{x:397.06920623779297,y:230.6661558151245,z:13.807345628738403,name:"leftEye"},{x:377.94647216796875,y:285.1647090911865,z:-13.305472135543823},{x:372.1118927001953,y:267.1267318725586,z:-18.83774757385254},{x:364.9968719482422,y:282.24411964416504,z:-19.818150997161865},{x:401.973876953125,y:331.20131492614746,z:11.566424369812012},{x:394.3083190917969,y:338.86693954467773,z:3.142542541027069},{x:373.9820861816406,y:351.4504623413086,z:-13.50388765335083},{x:414.3888854980469,y:321.24735832214355,z:45.51872253417969,name:"faceOval"},{x:373.44234466552734,y:227.33163356781006,z:10.626870393753052,name:"leftEye"},{x:364.0731430053711,y:240.31539916992188,z:-13.807345628738403},{x:384.2658233642578,y:353.3793067932129,z:.7385850697755814,name:"faceOval"},{x:423.20526123046875,y:283.5176181793213,z:47.152724266052246},{x:369.42798614501953,y:304.0898895263672,z:-14.647691249847412,name:"lips"},{x:370.63812255859375,y:305.90051651000977,z:-16.211668252944946},{x:371.91192626953125,y:309.0167713165283,z:-17.84567356109619},{x:373.0583953857422,y:313.3545398712158,z:-17.378815412521362,name:"lips"},{x:375.39905548095703,y:321.09289169311523,z:-13.118728399276733},{x:379.2567825317383,y:304.3582534790039,z:-7.924926280975342},{x:381.18797302246094,y:303.7031364440918,z:-7.843226194381714},{x:383.0918502807617,y:302.4884605407715,z:-7.6506465673446655,name:"lips"},{x:389.09461975097656,y:297.1475315093994,z:-5.5497825145721436},{x:411.6408920288086,y:280.24898529052734,z:12.02161192893982},{x:363.3110809326172,y:234.27620887756348,z:-6.775286793708801},{x:366.0474395751953,y:223.29872131347656,z:6.827808618545532},{x:370.34427642822266,y:225.1457118988037,z:9.558931589126587},{x:377.5371551513672,y:303.60079765319824,z:-7.358860373497009,name:"lips"},{x:412.9557800292969,y:299.53579902648926,z:19.39797878265381},{x:360.0810241699219,y:221.72012329101562,z:-2.153385728597641},{x:379.82784271240234,y:329.47723388671875,z:-10.48097848892212},{x:359.08477783203125,y:235.7911491394043,z:-18.079102039337158},{x:369.6688461303711,y:251.5407943725586,z:-14.962821006774902},{x:369.5555114746094,y:333.5307312011719,z:-15.67478060722351},{x:394.0193176269531,y:315.6973171234131,z:-.9920747578144073},{x:383.78997802734375,y:272.7268695831299,z:-4.689012169837952},{x:387.67765045166016,y:323.6722755432129,z:-5.640236139297485},{x:397.8769302368164,y:272.1331214904785,z:-.9395531564950943},{x:389.87476348876953,y:280.5630111694336,z:-4.29218202829361},{x:403.83888244628906,y:285.1167869567871,z:3.0229100584983826},{x:372.5467300415039,y:343.1070327758789,z:-16.153310537338257},{x:374.1112518310547,y:256.3721466064453,z:-10.574349164962769},{x:399.73785400390625,y:321.77515983581543,z:4.849494695663452},{x:392.03365325927734,y:330.56447982788086,z:-1.3407598435878754},{x:398.59134674072266,y:305.93902587890625,z:1.517290621995926},{x:417.95997619628906,y:290.9716987609863,z:26.89105987548828},{x:406.04541778564453,y:307.35154151916504,z:8.666064143180847},{x:420.75328826904297,y:298.40752601623535,z:41.78385257720947},{x:395.4522705078125,y:291.4153575897217,z:-2.1752697229385376},{x:368.6452102661133,y:245.8882999420166,z:-9.453888535499573},{x:370.34900665283203,y:263.56690406799316,z:-26.75100326538086},{x:374.98477935791016,y:266.6126346588135,z:-19.77146625518799},{x:366.99840545654297,y:258.12140464782715,z:-31.372904777526855},{x:371.00616455078125,y:217.63479709625244,z:5.60522198677063},{x:381.30577087402344,y:214.14087295532227,z:4.983716309070587},{x:390.1496124267578,y:213.38221549987793,z:5.593550801277161},{x:397.7696990966797,y:214.3659782409668,z:8.57852816581726},{x:403.1652069091797,y:217.65509605407715,z:13.013685941696167},{x:407.3551940917969,y:230.72525024414062,z:22.444231510162354},{x:424.0876770019531,y:251.7839241027832,z:51.16771221160889},{x:403.50196838378906,y:239.88757610321045,z:15.803166627883911},{x:397.31719970703125,y:241.49806022644043,z:11.233787536621094},{x:388.99425506591797,y:241.4366912841797,z:7.948269248008728},{x:380.7804489135742,y:239.78078842163086,z:6.600214838981628},{x:374.01336669921875,y:237.11946487426758,z:6.349278092384338},{x:369.39125061035156,y:234.35351371765137,z:5.987462401390076},{x:422.9730987548828,y:255.76455116271973,z:76.61150932312012,name:"faceOval"},{x:374.73915100097656,y:269.24214363098145,z:-16.608498096466064},{x:364.61681365966797,y:245.71088790893555,z:-20.02823829650879},{x:365.3834533691406,y:263.34174156188965,z:-32.32996463775635},{x:361.58252716064453,y:267.8273677825928,z:-30.345816612243652},{x:365.37208557128906,y:265.0249671936035,z:-29.178667068481445},{x:372.72605895996094,y:272.05135345458984,z:-14.834434986114502},{x:360.48614501953125,y:268.34827423095703,z:-32.189905643463135},{x:359.9516296386719,y:270.8049201965332,z:-24.650139808654785},{x:369.5049285888672,y:229.01945114135742,z:10.107489824295044},{x:365.5447769165039,y:230.24096488952637,z:5.593550801277161},{x:363.50669860839844,y:230.6208372116089,z:.43622106313705444},{x:399.3529510498047,y:227.65677452087402,z:15.35965085029602,name:"leftEye"},{x:402.5693130493164,y:224.60190296173096,z:15.931552648544312}],box:{xMin:277.8318977355957,yMin:168.7741756439209,xMax:424.2788314819336,yMax:359.8348903656006,width:146.4469337463379,height:191.0607147216797}},SAMPLE_FACELANDMARKER_RESULT:{faceLandmarks:[[{x:.5760777592658997,y:.8639070391654968,z:-.030997956171631813},{x:.572094738483429,y:.7886289358139038,z:-.07189624011516571},{x:.5723551511764526,y:.8075382709503174,z:-.03578168898820877},{x:.5548420548439026,y:.7188365459442139,z:-.057787876576185226},{x:.5706077814102173,y:.7674974799156189,z:-.07740399986505508},{x:.5681378245353699,y:.7387768030166626,z:-.07356284558773041},{x:.5621535181999207,y:.6681165099143982,z:-.04189874976873398},{x:.46613582968711853,y:.6679812073707581,z:.011289681307971478},{x:.5579932928085327,y:.6174106597900391,z:-.03502821549773216},{x:.5563451647758484,y:.5905600190162659,z:-.03928658738732338},{x:.5487832427024841,y:.4900572597980499,z:-.029898937791585922},{x:.5765544176101685,y:.8692144751548767,z:-.02831427752971649},{x:.5771114230155945,y:.873644232749939,z:-.02345779910683632},{x:.5771905779838562,y:.877016007900238,z:-.016658689826726913},{x:.5778058767318726,y:.8770116567611694,z:-.014505492523312569},{x:.5783766508102417,y:.8835000991821289,z:-.015996402129530907},{x:.5792440176010132,y:.8913810849189758,z:-.01924579218029976},{x:.5796768069267273,y:.8996334671974182,z:-.018261712044477463},{x:.5817288160324097,y:.9255813956260681,z:-.007126849144697189},{x:.5726592540740967,y:.7992473244667053,z:-.0643521398305893},{x:.5579419136047363,y:.7996989488601685,z:-.04566684365272522},{x:.4216199815273285,y:.5958762764930725,z:.06776496022939682},{x:.5052269697189331,y:.6796539425849915,z:-.0010737782577052712},{x:.49243026971817017,y:.6838865876197815,z:-.0005227324436418712},{x:.4796970784664154,y:.6856290102005005,z:.002684245817363262},{x:.4618356227874756,y:.6764569878578186,z:.013439622707664967},{x:.5160380601882935,y:.6737282276153564,z:-17607348127057776e-21},{x:.48070961236953735,y:.6255870461463928,z:-.008339674212038517},{x:.49719780683517456,y:.6256808042526245,z:-.008027955889701843},{x:.46674346923828125,y:.6317623853683472,z:-.004460199736058712},{x:.4582492709159851,y:.641118049621582,z:.0011905613355338573},{x:.45408669114112854,y:.6911458969116211,z:.020514748990535736},{x:.535312294960022,y:.9619986414909363,z:.012499462813138962},{x:.4608460068702698,y:.6628725528717041,z:.01517564244568348},{x:.4206731915473938,y:.6828458309173584,z:.07848648726940155},{x:.4390624463558197,y:.6796106696128845,z:.03283142298460007},{x:.5029968619346619,y:.7701570391654968,z:-.009734481573104858},{x:.5595027208328247,y:.8607323169708252,z:-.030043255537748337},{x:.5621269941329956,y:.8738374710083008,z:-.021709579974412918},{x:.5451499819755554,y:.865527331829071,z:-.022014077752828598},{x:.5351184010505676,y:.8705098032951355,z:-.011602800339460373},{x:.5495014190673828,y:.8744956254959106,z:-.016490943729877472},{x:.5395170450210571,y:.8759440779685974,z:-.007333362940698862},{x:.5183624029159546,y:.8959754705429077,z:.010520773939788342},{x:.5604349374771118,y:.7895449995994568,z:-.07082037627696991},{x:.557381272315979,y:.7687489986419678,z:-.07590588927268982},{x:.4432901442050934,y:.6308897733688354,z:.0027153254486620426},{x:.5258325338363647,y:.7151225805282593,z:-.014676518738269806},{x:.5271827578544617,y:.7833116054534912,z:-.037643320858478546},{x:.5257382988929749,y:.7717816233634949,z:-.03401920944452286},{x:.46516409516334534,y:.7705106735229492,z:.0065747760236263275},{x:.5558893084526062,y:.7420997619628906,z:-.0694495290517807},{x:.4720408320426941,y:.6066038608551025,z:-.021204356104135513},{x:.45432573556900024,y:.6158540844917297,z:-.011054684408009052},{x:.4305151402950287,y:.5608053803443909,z:.0396830290555954},{x:.5310865640640259,y:.6157484650611877,z:-.03081176057457924},{x:.5114666223526001,y:.6329749226570129,z:-.00335998204536736},{x:.506435751914978,y:.8786543607711792,z:.012980876490473747},{x:.4480472207069397,y:.8640613555908203,z:.12569651007652283},{x:.5372058153152466,y:.7942581176757812,z:-.03168361634016037},{x:.5488379597663879,y:.8001630306243896,z:-.03280917927622795},{x:.5213388204574585,y:.8794381618499756,z:.011892606504261494},{x:.5242055654525757,y:.8789222240447998,z:.008370225317776203},{x:.4477175176143646,y:.6039950251579285,z:-.0050799972377717495},{x:.526964008808136,y:.7916748523712158,z:-.02968614175915718},{x:.4971255660057068,y:.6050706505775452,z:-.028175678104162216},{x:.4938119053840637,y:.5882453918457031,z:-.03210941329598427},{x:.4757143557071686,y:.5094879865646362,z:-.01300730835646391},{x:.43947282433509827,y:.5816648006439209,z:.01415177434682846},{x:.485664039850235,y:.5477864146232605,z:-.023685332387685776},{x:.43635931611061096,y:.6226438283920288,z:.013606148771941662},{x:.42910251021385193,y:.6102726459503174,z:.03926564007997513},{x:.5605402588844299,y:.8680099248886108,z:-.027318159118294716},{x:.5474816560745239,y:.8702861070632935,z:-.019686367362737656},{x:.5373021364212036,y:.8728838562965393,z:-.010484928265213966},{x:.540735125541687,y:.7979167103767395,z:-.029073253273963928},{x:.5228585004806519,y:.87913578748703,z:.009915109723806381},{x:.530497670173645,y:.8815253973007202,z:.0020524784922599792},{x:.5259912610054016,y:.8790552616119385,z:.007895970717072487},{x:.5433906316757202,y:.7882310748100281,z:-.05121905356645584},{x:.541388213634491,y:.8777219653129578,z:-.00466804439201951},{x:.5515822172164917,y:.8767023086547852,z:-.010475946590304375},{x:.5637003779411316,y:.877059817314148,z:-.015273625031113625},{x:.5640299320220947,y:.9263423085212708,z:-.00658724969252944},{x:.5642300248146057,y:.8993074893951416,z:-.017653480172157288},{x:.5637336373329163,y:.8910360932350159,z:-.01852807030081749},{x:.5637134313583374,y:.8837276697158813,z:-.01482592523097992},{x:.564205527305603,y:.8768964409828186,z:-.01331155002117157},{x:.5419867634773254,y:.8778373599052429,z:-.0037720394320786},{x:.5404468774795532,y:.880696177482605,z:-.005610354244709015},{x:.5392338633537292,y:.8845721483230591,z:-.007352025713771582},{x:.538469672203064,y:.8891173601150513,z:-.005154991988092661},{x:.5189250111579895,y:.8452741503715515,z:-.009755070321261883},{x:.4258975088596344,y:.7662280797958374,z:.1387351155281067},{x:.5725725293159485,y:.8041572570800781,z:-.04583907872438431},{x:.5342061519622803,y:.8785833120346069,z:.002659974154084921},{x:.5324031114578247,y:.8804071545600891,z:.0017832003068178892},{x:.5538818836212158,y:.8078407645225525,z:-.03254539892077446},{x:.5325431823730469,y:.8026832938194275,z:-.019140373915433884},{x:.5514076948165894,y:.8043903112411499,z:-.03313535451889038},{x:.5131856203079224,y:.7284771800041199,z:-.009399853646755219},{x:.49331504106521606,y:.7443980574607849,z:-.005225230939686298},{x:.5239617824554443,y:.7807451486587524,z:-.025881027802824974},{x:.4473606050014496,y:.5315827131271362,z:.011164786294102669},{x:.45718759298324585,y:.5604941248893738,z:-.005943301599472761},{x:.4670005738735199,y:.5909327268600464,z:-.019681761041283607},{x:.5311570167541504,y:.9076261520385742,z:.00389476353302598},{x:.5249923467636108,y:.5893563628196716,z:-.037981919944286346},{x:.5166932344436646,y:.5429551005363464,z:-.03319704160094261},{x:.5085030198097229,y:.49676206707954407,z:-.02691275253891945},{x:.4687720239162445,y:.6834565997123718,z:.008113506250083447},{x:.4426414966583252,y:.7069531679153442,z:.028577271848917007},{x:.5230373740196228,y:.6675713658332825,z:.001773772411979735},{x:.4481240212917328,y:.6527872085571289,z:.012414850294589996},{x:.5339856743812561,y:.7012367844581604,z:-.020220188423991203},{x:.5347223281860352,y:.7761190533638,z:-.05141595005989075},{x:.4315067231655121,y:.7211957573890686,z:.04381405934691429},{x:.45203351974487305,y:.7206180095672607,z:.017288070172071457},{x:.46892452239990234,y:.7265436053276062,z:.005602988880127668},{x:.49314674735069275,y:.7202282547950745,z:-.0006408205372281373},{x:.5104925632476807,y:.7091827392578125,z:-.00362918758764863},{x:.5232142210006714,y:.698553740978241,z:-.00787867046892643},{x:.5497883558273315,y:.6743605136871338,z:-.036349106580019},{x:.43658503890037537,y:.7627100348472595,z:.042555369436740875},{x:.4397648870944977,y:.6528646349906921,z:.017956094816327095},{x:.5653332471847534,y:.7992802858352661,z:-.06365057826042175},{x:.5285563468933105,y:.736810564994812,z:-.018836988136172295},{x:.4180678725242615,y:.6792560815811157,z:.12284679710865021},{x:.5328429937362671,y:.6865872144699097,z:-.010484723374247551},{x:.5230283141136169,y:.7809416055679321,z:-.011922398582100868},{x:.4551771283149719,y:.6650775074958801,z:.01774493046104908},{x:.5337203741073608,y:.7618928551673889,z:-.04697106033563614},{x:.43463975191116333,y:.8133478164672852,z:.1354849934577942},{x:.5225707292556763,y:.6605283617973328,z:.004980515688657761},{x:.5441933870315552,y:.7497199773788452,z:-.06091512367129326},{x:.4774007797241211,y:.9159183502197266,z:.059622734785079956},{x:.48068761825561523,y:.9364941716194153,z:.08404944837093353},{x:.4268292486667633,y:.7657528519630432,z:.09051097184419632},{x:.46051913499832153,y:.8880485892295837,z:.0738474428653717},{x:.4243420660495758,y:.6434382200241089,z:.06230505183339119},{x:.5342157483100891,y:.9835634231567383,z:.021662971004843712},{x:.5668109655380249,y:.8042187094688416,z:-.044937074184417725},{x:.5176341533660889,y:.7530587315559387,z:-.012967454269528389},{x:.430206298828125,y:.6835605502128601,z:.04612284153699875},{x:.4794231951236725,y:.6732114553451538,z:.003970044665038586},{x:.49073347449302673,y:.6722435355186462,z:.0008692514384165406},{x:.5294116139411926,y:.884677529335022,z:.004413890186697245},{x:.4430122375488281,y:.80235356092453,z:.04987282305955887},{x:.5603825449943542,y:1.0092442035675049,z:.026417359709739685},{x:.5186598300933838,y:.9828659892082214,z:.0513598807156086},{x:.5010536909103394,y:.9640932679176331,z:.06591596454381943},{x:.5524769425392151,y:.539441704750061,z:-.035816047340631485},{x:.5879997611045837,y:1.0091472864151,z:.02285068854689598},{x:.5016193985939026,y:.6684437990188599,z:.00028415941051207483},{x:.511952817440033,y:.6642197370529175,z:.0021144719794392586},{x:.5194343328475952,y:.6623469591140747,z:.004674181342124939},{x:.4321230351924896,y:.6496355533599854,z:.03124697133898735},{x:.508686363697052,y:.6479565501213074,z:-.00044765998609364033},{x:.4963986277580261,y:.6431032419204712,z:-.0032507688738405704},{x:.4845542013645172,y:.6430778503417969,z:-.002903624437749386},{x:.4733612537384033,y:.647506833076477,z:.00023347247042693198},{x:.4668654501438141,y:.653346598148346,z:.004762572236359119},{x:.41815051436424255,y:.633708119392395,z:.09809435904026031},{x:.47159942984580994,y:.6711485385894775,z:.007849935442209244},{x:.5734396576881409,y:.8256140351295471,z:-.03155219927430153},{x:.5306524038314819,y:.8337990641593933,z:-.018351426348090172},{x:.5371729135513306,y:.7910830974578857,z:-.037286680191755295},{x:.5549534559249878,y:.8275275826454163,z:-.030664825811982155},{x:.5597432255744934,y:.6418541669845581,z:-.03318847343325615},{x:.4958484172821045,y:.9429569244384766,z:.048340678215026855},{x:.5140507817268372,y:.9634028077125549,z:.03589847311377525},{x:.5587693452835083,y:.9951097369194031,z:.00908728688955307},{x:.46411189436912537,y:.9051855206489563,z:.10601935535669327},{x:.5181609392166138,y:.6554316878318787,z:.002546071307733655},{x:.5436590909957886,y:.7085841298103333,z:-.03844436630606651},{x:.5872187614440918,y:.9960382580757141,z:.0063423276878893375},{x:.5379653573036194,y:.9989125728607178,z:.03636329993605614},{x:.4350326955318451,y:.8088565468788147,z:.09147704392671585},{x:.5523084998130798,y:.8773422837257385,z:-.009068487212061882},{x:.5510149598121643,y:.8816931843757629,z:-.011043853126466274},{x:.5503793954849243,y:.88776695728302,z:-.01348799467086792},{x:.5501549243927002,y:.8954370617866516,z:-.012142189778387547},{x:.546072781085968,y:.9192524552345276,z:-.003157563041895628},{x:.5314661860466003,y:.8771666884422302,z:.0005075141089037061},{x:.5293324589729309,y:.8762547969818115,z:.00039177737198770046},{x:.5275698900222778,y:.8750609755516052,z:47732755774632096e-21},{x:.5104271173477173,y:.8607332110404968,z:.0012934643309563398},{x:.45938700437545776,y:.8134918212890625,z:.023569690063595772},{x:.5418947339057922,y:.6864100694656372,z:-.027333909645676613},{x:.531914234161377,y:.6456130743026733,z:-.005434140563011169},{x:.523697018623352,y:.647885262966156,z:-.0002466466394253075},{x:.5338191390037537,y:.8783687353134155,z:.002268768846988678},{x:.46226605772972107,y:.8610277771949768,z:.04718952998518944},{x:.5434442758560181,y:.6456181406974792,z:-.02327350154519081},{x:.5399754643440247,y:.940219521522522,z:.005075343884527683},{x:.5661457777023315,y:.71457839012146,z:-.06242101639509201},{x:.5523148775100708,y:.6974870562553406,z:-.04863070324063301},{x:.5639959573745728,y:.6923378109931946,z:-.05180761218070984},{x:.5367592573165894,y:.7423217296600342,z:-.03623027727007866},{x:.5853689908981323,y:.9752064943313599,z:-.002361974213272333},{x:.5835235118865967,y:.9493685960769653,z:-.003941743168979883},{x:.5615018606185913,y:.949194610118866,z:-.0015953965485095978},{x:.5068561434745789,y:.9048219323158264,z:.01862684078514576},{x:.5134067535400391,y:.7971825003623962,z:-.008485661819577217},{x:.5223897099494934,y:.925589919090271,z:.01249657291918993},{x:.48500555753707886,y:.7959478497505188,z:-.0032065745908766985},{x:.5037734508514404,y:.8184596300125122,z:-.004932103678584099},{x:.4766361117362976,y:.828806459903717,z:.01027688942849636},{x:.5589827299118042,y:.974656343460083,z:.0009666886180639267},{x:.5294582843780518,y:.7541216611862183,z:-.025603046640753746},{x:.4973002076148987,y:.9208990931510925,z:.031931452453136444},{x:.5163551568984985,y:.9432790875434875,z:.024321340024471283},{x:.49399662017822266,y:.8814862370491028,z:.018687399104237556},{x:.44948166608810425,y:.836137592792511,z:.05702034756541252},{x:.47898444533348083,y:.8836610913276672,z:.03150695189833641},{x:.4454479217529297,y:.8499438166618347,z:.08868525922298431},{x:.49572959542274475,y:.8452823758125305,z:.0036111653316766024},{x:.5362502336502075,y:.7222585678100586,z:-.027912352234125137},{x:.5393770337104797,y:.7850722074508667,z:-.05415399745106697},{x:.531399667263031,y:.7898418307304382,z:-.03883346915245056},{x:.5451627373695374,y:.7717036604881287,z:-.06480253487825394},{x:.5206395983695984,y:.6287745833396912,z:-.010521138086915016},{x:.4974782466888428,y:.6191938519477844,z:-.014098240062594414},{x:.4774145185947418,y:.6193130612373352,z:-.013643337413668633},{x:.4616098403930664,y:.6259890198707581,z:-.008448202162981033},{x:.4516478478908539,y:.6368461847305298,z:9050309745362028e-20},{x:.4485096037387848,y:.6719120740890503,z:.022984720766544342},{x:.42177659273147583,y:.7240667343139648,z:.08511673659086227},{x:.4616215229034424,y:.6988231539726257,z:.014238474890589714},{x:.4755798876285553,y:.7034608721733093,z:.00625590980052948},{x:.4924992024898529,y:.7005885243415833,z:.0009391739731654525},{x:.5082254409790039,y:.693384051322937,z:-.0009464038303121924},{x:.5203112959861755,y:.6849707961082458,z:-.0022114769089967012},{x:.52867591381073,y:.6779075860977173,z:-.002962538506835699},{x:.4213953912258148,y:.7219811677932739,z:.1350894570350647},{x:.5320829749107361,y:.794858992099762,z:-.03181503340601921},{x:.5452795028686523,y:.7286570072174072,z:-.04771539941430092},{x:.5496407747268677,y:.7866933345794678,z:-.06452003121376038},{x:.557040274143219,y:.7962084412574768,z:-.05837344378232956},{x:.549176812171936,y:.7895247936248779,z:-.057761140167713165},{x:.5362890362739563,y:.8005836606025696,z:-.026903774589300156},{x:.560200035572052,y:.7983731031417847,z:-.06172555685043335},{x:.5616944432258606,y:.8022753596305847,z:-.045200999826192856},{x:.5273328423500061,y:.6611284017562866,z:.0029021520167589188},{x:.534850537776947,y:.6660012006759644,z:-.005215510260313749},{x:.5394860506057739,y:.6701375246047974,z:-.014931917190551758},{x:.4634307324886322,y:.658291757106781,z:.009295716881752014},{x:.4538393020629883,y:.6519932150840759,z:.00930330716073513},{x:.5776031613349915,y:.7159298658370972,z:-.057365912944078445},{x:.6504855155944824,y:.6461779475212097,z:.014184834435582161},{x:.5860154032707214,y:.7962266206741333,z:-.04522843658924103},{x:.6842049360275269,y:.5631637573242188,z:.07207967340946198},{x:.6152560710906982,y:.6674962639808655,z:.0007529259892180562},{x:.6280948519706726,y:.6684326529502869,z:.0016892586136236787},{x:.6408625245094299,y:.6663892269134521,z:.005331226624548435},{x:.6557814478874207,y:.6534678936004639,z:.01646413467824459},{x:.6035663485527039,y:.6639701724052429,z:.0013799630105495453},{x:.6329053044319153,y:.608010470867157,z:-.006195899099111557},{x:.6167260408401489,y:.6117533445358276,z:-.006319951266050339},{x:.6471013426780701,y:.6112449765205383,z:-.0017843559617176652},{x:.6560901999473572,y:.6185776591300964,z:.004047257360070944},{x:.6666946411132812,y:.6651176810264587,z:.023647578433156013},{x:.6311345100402832,y:.9495396018028259,z:.014004078693687916},{x:.6544655561447144,y:.6397901773452759,z:.01809609681367874},{x:.6965808868408203,y:.6482675075531006,z:.08304904401302338},{x:.679817259311676,y:.650188148021698,z:.03632688894867897},{x:.6336516737937927,y:.7541458010673523,z:-.007742783520370722},{x:.5921701192855835,y:.8567668199539185,z:-.029399123042821884},{x:.591663658618927,y:.870215654373169,z:-.02103729173541069},{x:.6068367958068848,y:.8584195375442505,z:-.020668085664510727},{x:.6176617741584778,y:.860965371131897,z:-.009790095500648022},{x:.6040634512901306,y:.8686612844467163,z:-.015289564616978168},{x:.6143736839294434,y:.8671170473098755,z:-.005712216719985008},{x:.6373105049133301,y:.8815656900405884,z:.012672550976276398},{x:.5832505822181702,y:.7866312861442566,z:-.07051534950733185},{x:.5836675763130188,y:.7658692598342896,z:-.07566110789775848},{x:.6709531545639038,y:.604898989200592,z:.005951565690338612},{x:.6029891967773438,y:.705652117729187,z:-.013388276100158691},{x:.6131622195243835,y:.7728396058082581,z:-.036248479038476944},{x:.6123163104057312,y:.7612020373344421,z:-.03264721855521202},{x:.6696187853813171,y:.744706928730011,z:.009673702530562878},{x:.5803102254867554,y:.7385968565940857,z:-.0689152330160141},{x:.6404349207878113,y:.5877999663352966,z:-.01929756999015808},{x:.6588467955589294,y:.5929454565048218,z:-.008487257175147533},{x:.6720337867736816,y:.530631422996521,z:.043437421321868896},{x:.584305465221405,y:.6099005341529846,z:-.030301367864012718},{x:.6034283638000488,y:.6217452883720398,z:-.001970183802768588},{x:.6460927724838257,y:.8608663082122803,z:.015541625209152699},{x:.6957815289497375,y:.8326103091239929,z:.13015234470367432},{x:.6043362617492676,y:.7861682772636414,z:-.030476901680231094},{x:.594293475151062,y:.7942103147506714,z:-.032218821346759796},{x:.6324057579040527,y:.8665139675140381,z:.014255806803703308},{x:.6296147704124451,y:.8667733669281006,z:.010388285852968693},{x:.663644552230835,y:.5798642635345459,z:-.0022301070857793093},{x:.6140630841255188,y:.7809288501739502,z:-.02835679054260254},{x:.615908145904541,y:.5921698212623596,z:-.026804860681295395},{x:.617181122303009,y:.5748661756515503,z:-.03060605563223362},{x:.6222207546234131,y:.49137672781944275,z:-.011151673272252083},{x:.6669357419013977,y:.5541607141494751,z:.017466170713305473},{x:.6182981729507446,y:.5320425629615784,z:-.021793590858578682},{x:.6760554313659668,y:.595052182674408,z:.017115700989961624},{x:.6801463961601257,y:.5800720453262329,z:.043127160519361496},{x:.5922210812568665,y:.8644017577171326,z:-.02662893570959568},{x:.6054555177688599,y:.8637874722480774,z:-.018363753333687782},{x:.6161889433860779,y:.8641164898872375,z:-.008808949030935764},{x:.6017249822616577,y:.7901403307914734,z:-.028126630932092667},{x:.631446123123169,y:.8664817810058594,z:.012112865224480629},{x:.6249198913574219,y:.8716511130332947,z:.003882825840264559},{x:.6281915903091431,y:.867301881313324,z:.009891441091895103},{x:.5986843109130859,y:.7813931703567505,z:-.050227612257003784},{x:.6126407384872437,y:.869275689125061,z:-.0031255714129656553},{x:.6027271151542664,y:.8711842894554138,z:-.009324162267148495},{x:.59088134765625,y:.8742044568061829,z:-.014608660712838173},{x:.5984604358673096,y:.9216185212135315,z:-.005981989670544863},{x:.5950398445129395,y:.8964707255363464,z:-.01703473925590515},{x:.5941568613052368,y:.8882410526275635,z:-.017784785479307175},{x:.5928806662559509,y:.8803883194923401,z:-.014153128489851952},{x:.5909661054611206,y:.8748103976249695,z:-.012609979137778282},{x:.6128016710281372,y:.8702545762062073,z:-.0022550546564161777},{x:.6150846481323242,y:.8726804256439209,z:-.00414019962772727},{x:.6173093914985657,y:.8770190477371216,z:-.005970994010567665},{x:.619335412979126,y:.8814800977706909,z:-.0036864024586975574},{x:.6292637586593628,y:.8314558267593384,z:-.007714875973761082},{x:.702275276184082,y:.7320667505264282,z:.1433621346950531},{x:.6204835176467896,y:.8689177632331848,z:.0044869170524179935},{x:.6223508715629578,y:.8704851269721985,z:.00352082890458405},{x:.590448260307312,y:.8029727935791016,z:-.03200828656554222},{x:.6097423434257507,y:.7933741211891174,z:-.018042555078864098},{x:.59229576587677,y:.7993767261505127,z:-.032564569264650345},{x:.6171364188194275,y:.7153720259666443,z:-.007672437466681004},{x:.6389747858047485,y:.726390540599823,z:-.002999067772179842},{x:.6151940226554871,y:.769412100315094,z:-.024427521973848343},{x:.6526776552200317,y:.505868136882782,z:.01412637997418642},{x:.6475822329521179,y:.5375454425811768,z:-.0033899128902703524},{x:.6433356404304504,y:.5714520215988159,z:-.017428796738386154},{x:.626949667930603,y:.8962116837501526,z:.005602736957371235},{x:.5868416428565979,y:.5829002261161804,z:-.03727729618549347},{x:.5877229571342468,y:.5345035791397095,z:-.032396964728832245},{x:.5887066125869751,y:.48655083775520325,z:-.025856535881757736},{x:.6507197618484497,y:.6612282991409302,z:.011114613153040409},{x:.6803066730499268,y:.677992045879364,z:.032125361263751984},{x:.5963194370269775,y:.6598632335662842,z:.002976928371936083},{x:.667536199092865,y:.6274255514144897,z:.015618261881172657},{x:.5930740833282471,y:.6940041780471802,z:-.019217798486351967},{x:.6053346395492554,y:.7676517963409424,z:-.050308309495449066},{x:.6934473514556885,y:.6884298920631409,z:.04794462397694588},{x:.6738007664680481,y:.6934011578559875,z:.020697161555290222},{x:.6588084697723389,y:.7033141851425171,z:.008462334051728249},{x:.6346072554588318,y:.7029502391815186,z:.001542167621664703},{x:.6157816648483276,y:.6966525912284851,z:-.002009218093007803},{x:.6015574336051941,y:.688928484916687,z:-.006588225718587637},{x:.5746836066246033,y:.6711069345474243,z:-.03597589209675789},{x:.6947521567344666,y:.7309479117393494,z:.046707939356565475},{x:.6759101152420044,y:.6249120831489563,z:.021654341369867325},{x:.5794773101806641,y:.7971615195274353,z:-.06339326500892639},{x:.6041849851608276,y:.727514922618866,z:-.017512541264295578},{x:.6968844532966614,y:.6440950036048889,z:.12727996706962585},{x:.5910853147506714,y:.679325520992279,z:-.009497715160250664},{x:.6157375574111938,y:.7695677280426025,z:-.010624290443956852},{x:.6606494784355164,y:.6410489678382874,z:.0208158977329731},{x:.6040687561035156,y:.7531470656394958,z:-.045887019485235214},{x:.7012156248092651,y:.780247151851654,z:.14028730988502502},{x:.595149576663971,y:.6527782678604126,z:.006308757700026035},{x:.5925500392913818,y:.7436665892601013,z:-.060151755809783936},{x:.6780198812484741,y:.8905693888664246,z:.0626060739159584},{x:.676746666431427,y:.9113880395889282,z:.08726003766059875},{x:.7030686140060425,y:.7312687635421753,z:.09529774636030197},{x:.688987135887146,y:.8588417172431946,z:.07752864807844162},{x:.6883691549301147,y:.6109960675239563,z:.06669612973928452},{x:.6358906030654907,y:.9702065587043762,z:.023120900616049767},{x:.5781539678573608,y:.8023634552955627,z:-.044763918966054916},{x:.6170316934585571,y:.7408350706100464,z:-.011375460773706436},{x:.688542366027832,y:.6516284346580505,z:.050206027925014496},{x:.6385149359703064,y:.6540714502334595,z:.006462941411882639},{x:.6279382109642029,y:.6563615798950195,z:.003062846139073372},{x:.6268895268440247,y:.8736732006072998,z:.00627936702221632},{x:.6944946050643921,y:.7709181308746338,z:.053824134171009064},{x:.614617109298706,y:1.0022112131118774,z:.02719894051551819},{x:.6493719220161438,y:.9665167927742004,z:.053563784807920456},{x:.6624587178230286,y:.943530797958374,z:.068605437874794},{x:.6162528991699219,y:.6558693051338196,z:.002187855076044798},{x:.6058168411254883,y:.654328465461731,z:.0036193584091961384},{x:.5987918972969055,y:.6536934971809387,z:.006134530063718557},{x:.6831037402153015,y:.6195642948150635,z:.03511790186166763},{x:.6062582731246948,y:.6356398463249207,z:.001280312892049551},{x:.6174948811531067,y:.62776118516922,z:-.0013642468256875873},{x:.6297246217727661,y:.6253792643547058,z:-.0007034156005829573},{x:.6407091617584229,y:.627578616142273,z:.0028144705574959517},{x:.6479622721672058,y:.6322650909423828,z:.00750273372977972},{x:.6915091276168823,y:.5990704298019409,z:.10270945727825165},{x:.6457163095474243,y:.6504453420639038,z:.010696077719330788},{x:.6164222955703735,y:.8231936097145081,z:-.016772059723734856},{x:.6042401194572449,y:.7830976843833923,z:-.03630910441279411},{x:.5922216773033142,y:.8228387236595154,z:-.029992375522851944},{x:.6646111011505127,y:.92097008228302,z:.050967294722795486},{x:.651232898235321,y:.9460107088088989,z:.038000158965587616},{x:.6140977144241333,y:.9882472157478333,z:.009882091544568539},{x:.6870781183242798,y:.8768675327301025,z:.10980932414531708},{x:.5986856818199158,y:.6456438899040222,z:.003999010659754276},{x:.585981547832489,y:.7034481763839722,z:-.0377722829580307},{x:.6342031359672546,y:.9867448806762695,z:.03786521404981613},{x:.7013950943946838,y:.776049017906189,z:.09598205983638763},{x:.6030206680297852,y:.8719133138656616,z:-.007931148633360863},{x:.6050592064857483,y:.8767156004905701,z:-.009791925549507141},{x:.6073468923568726,y:.8831382393836975,z:-.012361008673906326},{x:.6087977290153503,y:.890143632888794,z:-.01098148338496685},{x:.6147705316543579,y:.9110084772109985,z:-.0018823575228452682},{x:.622577965259552,y:.8670604825019836,z:.002609190298244357},{x:.6241236329078674,y:.8651344180107117,z:.0025534380692988634},{x:.6257084608078003,y:.8638408184051514,z:.0023300074972212315},{x:.639931321144104,y:.8449671268463135,z:.0038123116828501225},{x:.6810906529426575,y:.7856625318527222,z:.02717764675617218},{x:.583532452583313,y:.6811994910240173,z:-.026588857173919678},{x:.5855660438537598,y:.6393819451332092,z:-.004512844607234001},{x:.5932201743125916,y:.6398029327392578,z:.0008020466193556786},{x:.6200879812240601,y:.8683351874351501,z:.00417016725987196},{x:.6842559576034546,y:.8330534100532532,z:.050836317241191864},{x:.5754412412643433,y:.6418221592903137,z:-.022838059812784195},{x:.6232790350914001,y:.9295297265052795,z:.006339520215988159},{x:.5764067769050598,y:.694546639919281,z:-.04825803264975548},{x:.59778892993927,y:.7343927621841431,z:-.035004377365112305},{x:.6042810678482056,y:.9441440105438232,z:-.0010970570147037506},{x:.6496372222900391,y:.8869078159332275,z:.021036235615611076},{x:.6274012327194214,y:.7830310463905334,z:-.006658440921455622},{x:.637792706489563,y:.9104999899864197,z:.014290250837802887},{x:.6549934148788452,y:.7748609185218811,z:-.0006672973395325243},{x:.6404005289077759,y:.801220715045929,z:-.0026642554439604282},{x:.6671456694602966,y:.8045546412467957,z:.013180811889469624},{x:.6107483506202698,y:.9680658578872681,z:.001778992242179811},{x:.6060343980789185,y:.744587242603302,z:-.024382334202528},{x:.6602751612663269,y:.8998945355415344,z:.0344940721988678},{x:.6463775038719177,y:.9262562394142151,z:.02617623284459114},{x:.6579852104187012,y:.8602304458618164,z:.021586716175079346},{x:.6926165223121643,y:.8053340315818787,z:.061075080186128616},{x:.6724731922149658,y:.8594399690628052,z:.03457934781908989},{x:.6975721716880798,y:.8183245062828064,z:.09300774335861206},{x:.6512877941131592,y:.8258221745491028,z:.006324059329926968},{x:.594887375831604,y:.7148372530937195,z:-.026898479089140892},{x:.6017440557479858,y:.7773507833480835,z:-.05312420800328255},{x:.6096571683883667,y:.7806998491287231,z:-.037646256387233734},{x:.5952993035316467,y:.7654367685317993,z:-.06398405134677887},{x:.5950021147727966,y:.6201304793357849,z:-.009297547861933708},{x:.6165438890457153,y:.6052900552749634,z:-.012455573305487633},{x:.6362661719322205,y:.6015968918800354,z:-.011649220250546932},{x:.6522727608680725,y:.6046400666236877,z:-.005903332494199276},{x:.6625409722328186,y:.6128141283988953,z:.0030042496509850025},{x:.6688099503517151,y:.6457712054252625,z:.026322703808546066},{x:.7013440728187561,y:.6893666386604309,z:.08984331786632538},{x:.6608623266220093,y:.6749406456947327,z:.0172116681933403},{x:.6482325196266174,y:.6823726296424866,z:.008881398476660252},{x:.6313265562057495,y:.6842025518417358,z:.0031308617908507586},{x:.6147016286849976,y:.6809731721878052,z:.0007630771724507213},{x:.6018834114074707,y:.6755372285842896,z:-.0008834321051836014},{x:.5925027132034302,y:.670681357383728,z:-.001968748401850462},{x:.700127363204956,y:.6871103644371033,z:.13980500400066376},{x:.6095665693283081,y:.7853189706802368,z:-.03074747882783413},{x:.5880423784255981,y:.7229287028312683,z:-.04691500961780548},{x:.5930182337760925,y:.7811514139175415,z:-.06398335844278336},{x:.5867722034454346,y:.7922660112380981,z:-.05794971063733101},{x:.5933279991149902,y:.7842848896980286,z:-.05714067071676254},{x:.6063535809516907,y:.7920218706130981,z:-.02590685710310936},{x:.5839452743530273,y:.794978141784668,z:-.0615212507545948},{x:.5828126072883606,y:.8000800013542175,z:-.0449722595512867},{x:.5909603834152222,y:.6541213393211365,z:.003991890233010054},{x:.5852181911468506,y:.6602938771247864,z:-.004428438376635313},{x:.5825737714767456,y:.6651063561439514,z:-.014345290139317513},{x:.6517343521118164,y:.6362385153770447,z:.012151890434324741},{x:.6615052819252014,y:.6281577944755554,z:.0123682152479887},{x:.4856873154640198,y:.6568945646286011,z:.000720038078725338},{x:.49988406896591187,y:.6547410488128662,z:.0006949726957827806},{x:.48438939452171326,y:.6392973065376282,z:.000705525919329375},{x:.47143134474754333,y:.6589511632919312,z:.0006980331381782889},{x:.48704618215560913,y:.6752797961235046,z:.0006921177846379578},{x:.6243702173233032,y:.640461802482605,z:-6592126737814397e-20},{x:.6390967965126038,y:.6385173797607422,z:-.00016105435497593135},{x:.6230536699295044,y:.6224825382232666,z:-.00016136496560648084},{x:.6095397472381592,y:.641917884349823,z:-.0001803556369850412},{x:.6250996589660645,y:.6586247682571411,z:-.0001785515050869435}]],faceBlendshapes:[{categories:[{index:0,score:5187174338061595e-21,categoryName:"_neutral",displayName:""},{index:1,score:.24521504342556,categoryName:"browDownLeft",displayName:""},{index:2,score:.1987743377685547,categoryName:"browDownRight",displayName:""},{index:3,score:.013400448486208916,categoryName:"browInnerUp",displayName:""},{index:4,score:.012361560948193073,categoryName:"browOuterUpLeft",displayName:""},{index:5,score:.019305096939206123,categoryName:"browOuterUpRight",displayName:""},{index:6,score:28426356948330067e-21,categoryName:"cheekPuff",displayName:""},{index:7,score:3.4500112633395474e-7,categoryName:"cheekSquintLeft",displayName:""},{index:8,score:4.83789051486383e-7,categoryName:"cheekSquintRight",displayName:""},{index:9,score:.07650448381900787,categoryName:"eyeBlinkLeft",displayName:""},{index:10,score:.05070012807846069,categoryName:"eyeBlinkRight",displayName:""},{index:11,score:.13978900015354156,categoryName:"eyeLookDownLeft",displayName:""},{index:12,score:.14198613166809082,categoryName:"eyeLookDownRight",displayName:""},{index:13,score:.2177766114473343,categoryName:"eyeLookInLeft",displayName:""},{index:14,score:.014739357866346836,categoryName:"eyeLookInRight",displayName:""},{index:15,score:.02361512929201126,categoryName:"eyeLookOutLeft",displayName:""},{index:16,score:.19679604470729828,categoryName:"eyeLookOutRight",displayName:""},{index:17,score:.04874616861343384,categoryName:"eyeLookUpLeft",displayName:""},{index:18,score:.049392376095056534,categoryName:"eyeLookUpRight",displayName:""},{index:19,score:.34944331645965576,categoryName:"eyeSquintLeft",displayName:""},{index:20,score:.2939716875553131,categoryName:"eyeSquintRight",displayName:""},{index:21,score:.005955042317509651,categoryName:"eyeWideLeft",displayName:""},{index:22,score:.006776117719709873,categoryName:"eyeWideRight",displayName:""},{index:23,score:16942436559475027e-21,categoryName:"jawForward",displayName:""},{index:24,score:.0045165494084358215,categoryName:"jawLeft",displayName:""},{index:25,score:.07803940027952194,categoryName:"jawOpen",displayName:""},{index:26,score:2090057751047425e-20,categoryName:"jawRight",displayName:""},{index:27,score:.06032035872340202,categoryName:"mouthClose",displayName:""},{index:28,score:.00228882092051208,categoryName:"mouthDimpleLeft",displayName:""},{index:29,score:.00781762320548296,categoryName:"mouthDimpleRight",displayName:""},{index:30,score:.0017093931091949344,categoryName:"mouthFrownLeft",displayName:""},{index:31,score:.0019319106359034777,categoryName:"mouthFrownRight",displayName:""},{index:32,score:8485237776767462e-20,categoryName:"mouthFunnel",displayName:""},{index:33,score:.0009051355300471187,categoryName:"mouthLeft",displayName:""},{index:34,score:.0003630454302765429,categoryName:"mouthLowerDownLeft",displayName:""},{index:35,score:.00017601238505449146,categoryName:"mouthLowerDownRight",displayName:""},{index:36,score:.12865161895751953,categoryName:"mouthPressLeft",displayName:""},{index:37,score:.20137207210063934,categoryName:"mouthPressRight",displayName:""},{index:38,score:.0022203284315764904,categoryName:"mouthPucker",displayName:""},{index:39,score:.0009096377179957926,categoryName:"mouthRight",displayName:""},{index:40,score:.34189721941947937,categoryName:"mouthRollLower",displayName:""},{index:41,score:.11409689486026764,categoryName:"mouthRollUpper",displayName:""},{index:42,score:.17172536253929138,categoryName:"mouthShrugLower",displayName:""},{index:43,score:.004038424696773291,categoryName:"mouthShrugUpper",displayName:""},{index:44,score:.00023205230536404997,categoryName:"mouthSmileLeft",displayName:""},{index:45,score:.00019313619122840464,categoryName:"mouthSmileRight",displayName:""},{index:46,score:.0018571305554360151,categoryName:"mouthStretchLeft",displayName:""},{index:47,score:.0023813238367438316,categoryName:"mouthStretchRight",displayName:""},{index:48,score:24323100660694763e-21,categoryName:"mouthUpperUpLeft",displayName:""},{index:49,score:3161552012898028e-20,categoryName:"mouthUpperUpRight",displayName:""},{index:50,score:1.08198406678639e-7,categoryName:"noseSneerLeft",displayName:""},{index:51,score:12652527630052646e-22,categoryName:"noseSneerRight",displayName:""}],headIndex:-1,headName:""}],facialTransformationMatrixes:[{rows:4,columns:4,data:[.9947517514228821,.10230544209480286,.0013679931871592999,0,-.10230997204780579,.9947447776794434,.003816320328041911,0,-.000970348424743861,-.0039362297393381596,.9999914169311523,0,2.8888821601867676,-7.808934211730957,-30.52109146118164,1]}]}},Qi=t.createContext({}),Ki={basePath:"https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.17/wasm",options:{baseOptions:{modelAssetPath:"https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task",delegate:"GPU"},runningMode:"VIDEO",outputFaceBlendshapes:!0,outputFacialTransformationMatrixes:!0}},Ji=t.forwardRef((({basePath:e=Ki.basePath,options:r=Ki.options,children:n},a)=>{const o=JSON.stringify(r),i=f.suspend((async()=>{const{FilesetResolver:t,FaceLandmarker:n}=await Promise.resolve().then((function(){return S(require("@mediapipe/tasks-vision"))})),a=await t.forVisionTasks(e);return n.createFromOptions(a,r)}),[e,o]);return t.useEffect((()=>()=>{null==i||i.close(),f.clear([e,o])}),[i,e,o]),t.useImperativeHandle(a,(()=>i),[i]),C.createElement(Qi.Provider,{value:i},n)}));function es(){return t.useContext(Qi)}function ts(e,t){return e.clone().add(t).multiplyScalar(.5)}function rs(e,t,r){const n=e.localToWorld(t);return r.worldToLocal(n)}const ns=t.createContext({}),as=t.forwardRef((({camera:e,videoTexture:r={start:!0},manualDetect:n=!1,faceLandmarkerResult:o,manualUpdate:i=!1,makeDefault:l,smoothTime:c=.25,offset:u=!0,offsetScalar:d=80,eyes:m=!1,eyesAsOrigin:f=!0,depth:p=.15,debug:h=!1,facemesh:x},y)=>{var v,g;const w=a.useThree((e=>e.scene)),z=a.useThree((e=>e.camera)),b=a.useThree((e=>e.set)),E=a.useThree((e=>e.get)),M=e||z,S=t.useRef(null),[P]=t.useState((()=>new R.Object3D)),[D]=t.useState((()=>new R.Vector3)),[F]=t.useState((()=>new R.Vector3)),[k]=t.useState((()=>new R.Vector3)),[_]=t.useState((()=>new R.Vector3)),A=t.useCallback((()=>{P.parent=M.parent;const e=S.current;if(e){const{outerRef:t,eyeRightRef:r,eyeLeftRef:n}=e;if(r.current&&n.current){const{irisDirRef:e}=r.current,{irisDirRef:a}=n.current;e.current&&a.current&&t.current&&(D.copy(rs(e.current,new R.Vector3(0,0,0),t.current)),F.copy(rs(a.current,new R.Vector3(0,0,0),t.current)),P.position.copy(rs(t.current,ts(D,F),M.parent||w)),k.copy(rs(e.current,new R.Vector3(0,0,1),t.current)),_.copy(rs(a.current,new R.Vector3(0,0,1),t.current)),P.lookAt(t.current.localToWorld(ts(k,_))))}else t.current&&(P.position.copy(rs(t.current,new R.Vector3(0,0,0),M.parent||w)),P.lookAt(t.current.localToWorld(new R.Vector3(0,0,1))))}return P}),[M,F,_,D,k,w,P]),[L]=t.useState((()=>new R.Object3D)),I=t.useCallback((function(e,t){if(M){var r;if(null!==(r=t)&&void 0!==r||(t=A()),c>0){const r=1e-9;s.easing.damp3(L.position,t.position,c,e,void 0,void 0,r),s.easing.dampE(L.rotation,t.rotation,c,e,void 0,void 0,r)}else L.position.copy(t.position),L.rotation.copy(t.rotation);M.position.copy(L.position),M.rotation.copy(L.rotation)}}),[M,A,c,L.position,L.rotation]);a.useFrame(((e,t)=>{i||I(t)}));const B=t.useRef(null),[V,U]=t.useState(),O=es(),N=t.useCallback(((e,t)=>{const r=B.current;if(!r)return;const n=r.source.data,a=null==O?void 0:O.detectForVideo(n,e);U(a)}),[O]),j=t.useMemo((()=>Object.assign(Object.create(R.EventDispatcher.prototype),{computeTarget:A,update:I,facemeshApiRef:S})),[A,I]);t.useImperativeHandle(y,(()=>j),[j]),t.useEffect((()=>{if(l){const e=E().controls;return b({controls:j}),()=>b({controls:e})}}),[l,j,E,b]);const W=null!=o?o:V,G=null==W?void 0:W.faceLandmarks[0],H=null==W||null==(v=W.facialTransformationMatrixes)?void 0:v[0],$=null==W||null==(g=W.faceBlendshapes)?void 0:g[0],q={onVideoFrame:N,...r};return C.createElement(ns.Provider,{value:j},!n&&C.createElement(t.Suspense,{fallback:null},"src"in q?C.createElement(or,T.default({ref:B},q)):C.createElement(Gi,T.default({ref:B},q))),C.createElement(qi,T.default({ref:S,children:C.createElement("meshNormalMaterial",{side:R.DoubleSide})},x,{points:G,depth:p,facialTransformationMatrix:H,faceBlendshapes:$,eyes:m,eyesAsOrigin:f,offset:u,offsetScalar:d,debug:h,"rotation-z":Math.PI,visible:h})))})),os=new R.Vector3,is=new R.Vector2,ss=new R.Vector3,ls=new R.Vector3,cs=new R.Vector3,us=new R.Plane,ds=C.forwardRef((({autoTransform:e=!0,matrix:t,axisLock:r,dragLimits:n,onHover:o,onDragStart:s,onDrag:l,onDragEnd:c,children:u,dragConfig:d,...m},f)=>{const p=a.useThree((e=>e.controls)),{camera:h,size:x,raycaster:y,invalidate:v}=a.useThree(),g=C.useRef(null),w=i.useGesture({onHover:({hovering:e})=>o&&o(null!=e&&e),onDragStart:({event:e})=>{p&&(p.enabled=!1);const{point:t}=e;g.current.matrix.decompose(os,new R.Quaternion,new R.Vector3),ss.copy(t),ls.copy(ss).sub(os),s&&s(os),v()},onDrag:({xy:[t,a],intentional:o})=>{if(!o)return;const i=(t-x.left)/x.width*2-1,s=-(a-x.top)/x.height*2+1;if(is.set(i,s),y.setFromCamera(is,h),r)switch(r){case"x":cs.set(1,0,0);break;case"y":cs.set(0,1,0);break;case"z":cs.set(0,0,1)}else h.getWorldDirection(cs).negate();us.setFromNormalAndCoplanarPoint(cs,ss),y.ray.intersectPlane(us,ss);const c=g.current.matrix.clone(),u=g.current.matrixWorld.clone(),d=new R.Vector3(ss.x-ls.x,ss.y-ls.y,ss.z-ls.z);if(n&&(d.x=n[0]?Math.max(Math.min(d.x,n[0][1]),n[0][0]):d.x,d.y=n[1]?Math.max(Math.min(d.y,n[1][1]),n[1][0]):d.y,d.z=n[2]?Math.max(Math.min(d.z,n[2][1]),n[2][0]):d.z),e){g.current.matrix.setPosition(d);const e=g.current.matrix.clone().multiply(c.invert()),t=g.current.matrix.clone().multiply(u.invert());l&&l(g.current.matrix,e,g.current.matrixWorld,t)}else{const e=(new R.Matrix4).copy(g.current.matrix);e.setPosition(d);const t=e.clone().multiply(c.invert()),r=e.clone().multiply(u.invert());l&&l(e,t,g.current.matrixWorld,r)}v()},onDragEnd:()=>{p&&(p.enabled=!0),c&&c(),v()}},{drag:{filterTaps:!0,threshold:1,..."object"==typeof d?d:{}}});return C.useImperativeHandle(f,(()=>g.current),[]),C.useLayoutEffect((()=>{t&&(g.current.matrix=t)}),[t]),C.createElement("group",T.default({ref:g},w(),{matrix:t,matrixAutoUpdate:!1},m),u)}));exports.AccumulativeShadows=sa,exports.AdaptiveDpr=function({pixelated:e}){const t=a.useThree((e=>e.gl)),r=a.useThree((e=>e.internal.active)),n=a.useThree((e=>e.performance.current)),o=a.useThree((e=>e.viewport.initialDpr)),i=a.useThree((e=>e.setDpr));return C.useEffect((()=>{const n=t.domElement;return()=>{r&&i(o),e&&n&&(n.style.imageRendering="auto")}}),[]),C.useEffect((()=>{i(n*o),e&&t.domElement&&(t.domElement.style.imageRendering=1===n?"auto":"pixelated")}),[n]),null},exports.AdaptiveEvents=function(){const e=a.useThree((e=>e.get)),t=a.useThree((e=>e.setEvents)),r=a.useThree((e=>e.performance.current));return C.useEffect((()=>{const r=e().events.enabled;return()=>t({enabled:r})}),[]),C.useEffect((()=>t({enabled:1===r})),[r]),null},exports.ArcballControls=pt,exports.AsciiRenderer=function({renderIndex:e=1,bgColor:t="black",fgColor:r="white",characters:n=" .:-+*=%@#",invert:o=!0,color:i=!1,resolution:s=.15}){const{size:l,gl:c,scene:d,camera:m}=a.useThree(),f=C.useMemo((()=>{const e=new u.AsciiEffect(c,n,{invert:o,color:i,resolution:s});return e.domElement.style.position="absolute",e.domElement.style.top="0px",e.domElement.style.left="0px",e.domElement.style.pointerEvents="none",e}),[n,o,i,s]);return C.useLayoutEffect((()=>{f.domElement.style.color=r,f.domElement.style.backgroundColor=t}),[r,t]),C.useEffect((()=>(c.domElement.style.opacity="0",c.domElement.parentNode.appendChild(f.domElement),()=>{c.domElement.style.opacity="1",c.domElement.parentNode.removeChild(f.domElement)})),[f]),C.useEffect((()=>{f.setSize(l.width,l.height)}),[f,l]),a.useFrame((e=>{f.render(d,m)}),e),C.createElement(C.Fragment,null)},exports.BBAnchor=({anchor:e,...t})=>{const r=C.useRef(null),n=C.useRef(null);return C.useEffect((()=>{var e;null!=(e=r.current)&&null!=(e=e.parent)&&e.parent&&(n.current=r.current.parent,r.current.parent.parent.add(r.current))}),[]),a.useFrame((()=>{n.current&&(yr.setFromObject(n.current),yr.getSize(vr),r.current.position.set(n.current.position.x+vr.x*(Array.isArray(e)?e[0]:e.x)/2,n.current.position.y+vr.y*(Array.isArray(e)?e[1]:e.y)/2,n.current.position.z+vr.z*(Array.isArray(e)?e[2]:e.z)/2))})),C.createElement("group",T.default({ref:r},t))},exports.Backdrop=function({children:e,floor:t=.25,segments:r=20,receiveShadow:n,...a}){const o=C.useRef(null);return C.useLayoutEffect((()=>{let e=0;const n=r/r/2,a=o.current.attributes.position;for(let o=0;o<r+1;o++)for(let i=0;i<r+1;i++)a.setXYZ(e++,o/r-n+(0===o?-t:0),i/r-n,ma(o/r));a.needsUpdate=!0,o.current.computeVertexNormals()}),[r,t]),C.createElement("group",a,C.createElement("mesh",{receiveShadow:n,rotation:[-Math.PI/2,0,Math.PI/2]},C.createElement("planeGeometry",{ref:o,args:[1,1,r,r]}),e))},exports.BakeShadows=function(){const e=a.useThree((e=>e.gl));return t.useEffect((()=>(e.shadowMap.autoUpdate=!1,e.shadowMap.needsUpdate=!0,()=>{e.shadowMap.autoUpdate=e.shadowMap.needsUpdate=!0})),[e.shadowMap]),null},exports.Billboard=ne,exports.Bounds=Vn,exports.Box=un,exports.Bvh=hr,exports.CameraControls=vt,exports.CameraShake=On,exports.Capsule=Cn,exports.CatmullRomLine=xe,exports.Caustics=ga,exports.Center=br,exports.Circle=dn,exports.Clone=je,exports.Cloud=ja,exports.CloudInstance=Na,exports.Clouds=Oa,exports.ComputedAttribute=({compute:e,name:t,...r})=>{const[a]=C.useState((()=>new n.BufferAttribute(new Float32Array(0),1))),o=C.useRef(null);return C.useLayoutEffect((()=>{if(o.current){var t;const r=null!==(t=o.current.parent)&&void 0!==t?t:o.current.__r3f.parent,n=e(r);o.current.copy(n)}}),[e]),C.createElement("primitive",T.default({ref:o,object:a,attach:`attributes-${t}`},r))},exports.Cone=mn,exports.ContactShadows=aa,exports.CubeCamera=function({children:e,frames:t=1/0,resolution:r,near:n,far:o,envMap:i,fog:s,...l}){const c=C.useRef(null),{fbo:u,camera:d,update:m}=lt({resolution:r,near:n,far:o,envMap:i,fog:s});let f=0;return a.useFrame((()=>{c.current&&(t===1/0||f<t)&&(c.current.visible=!1,m(),c.current.visible=!0,f++)})),C.createElement("group",l,C.createElement("primitive",{object:d}),C.createElement("group",{ref:c},null==e?void 0:e(u.texture)))},exports.CubeTexture=function({children:e,files:t,...r}){const n=Kt(t,{...r});return C.createElement(C.Fragment,null,null==e?void 0:e(n))},exports.CubicBezierLine=he,exports.CurveModifier=Gr,exports.CycleRaycast=function({onChanged:e,portal:t,preventDefault:r=!0,scroll:n=!0,keyCode:o=9}){const i=C.useRef(0),s=a.useThree((e=>e.setEvents)),l=a.useThree((e=>e.get)),c=a.useThree((e=>e.gl));return C.useEffect((()=>{var a;let u,d=[];const m=l().events.filter,f=null!==(a=null==t?void 0:t.current)&&void 0!==a?a:c.domElement.parentNode,p=()=>f&&e&&e(d,Math.round(i.current)%d.length);s({filter:(e,t)=>{let r=[...e];r.length===d.length&&d.every((e=>r.map((e=>e.object.uuid)).includes(e.object.uuid)))||(i.current=0,d=r,p()),m&&(r=m(r,t));for(let e=0;e<Math.round(i.current)%r.length;e++){const e=r.shift();r=[...r,e]}return r}});const h=e=>{var t,r;i.current=e(i.current),null==(t=l().events.handlers)||t.onPointerCancel(void 0),null==(r=l().events.handlers)||r.onPointerMove(u),p()},x=e=>{(e.keyCode||e.which)===o&&(r&&e.preventDefault(),d.length>1&&h((e=>e+1)))},y=e=>{r&&e.preventDefault();let t=0;e||(e=window.event),e.wheelDelta?t=e.wheelDelta/120:e.detail&&(t=-e.detail/3),d.length>1&&h((e=>Math.abs(e-t)))},v=e=>u=e;return document.addEventListener("pointermove",v,{passive:!0}),n&&document.addEventListener("wheel",y),void 0!==o&&document.addEventListener("keydown",x),()=>{s({filter:m}),void 0!==o&&document.removeEventListener("keydown",x),n&&document.removeEventListener("wheel",y),document.removeEventListener("pointermove",v)}}),[c,l,s,r,n,o]),null},exports.Cylinder=fn,exports.Decal=Xe,exports.Detailed=So,exports.DetectGPU=function({children:e,...t}){const r=fr(t);return C.createElement(C.Fragment,null,null==e?void 0:e(r))},exports.DeviceOrientationControls=ct,exports.Dodecahedron=Mn,exports.DragControls=ds,exports.Edges=Le,exports.Effects=Me,exports.Environment=na,exports.EnvironmentCube=ea,exports.EnvironmentMap=Jn,exports.EnvironmentPortal=ta,exports.Example=Er,exports.Extrude=Sn,exports.FaceControls=as,exports.FaceLandmarker=Ji,exports.FaceLandmarkerDefaults=Ki,exports.Facemesh=qi,exports.FacemeshDatas=Yi,exports.FacemeshEye=Zi,exports.FacemeshEyeDefaults=Xi,exports.Fbo=({children:e,width:t,height:r,...n})=>{const a=ot(t,r,n);return C.createElement(C.Fragment,null,null==e?void 0:e(a))},exports.Fbx=function({path:e,...t}){const r=Jt(e).children[0];return C.createElement(je,T.default({},t,{object:r}))},exports.FirstPersonControls=yt,exports.Fisheye=function({renderPriority:e=1,zoom:t=0,segments:r=64,children:n,resolution:o=896,...i}){const s=C.useRef(null),l=C.useRef(null),{width:c,height:u}=a.useThree((e=>e.size)),[d]=C.useState((()=>new R.OrthographicCamera));C.useLayoutEffect((()=>{d.position.set(0,0,100),d.zoom=100,d.left=c/-2,d.right=c/2,d.top=u/2,d.bottom=u/-2,d.updateProjectionMatrix()}),[c,u]);const m=Math.sqrt(c*c+u*u)/100*(.5+t/2),f=new R.Vector3,p=new R.Sphere(new R.Vector3,m),h=new R.Matrix3,x=C.useCallback(((e,t,r)=>{t.pointer.set(e.offsetX/t.size.width*2-1,-e.offsetY/t.size.height*2+1),t.raycaster.setFromCamera(t.pointer,d),t.raycaster.ray.intersectSphere(p,f)&&(f.normalize(),h.getNormalMatrix(l.current.camera.matrixWorld),l.current.camera.getWorldPosition(t.raycaster.ray.origin),t.raycaster.ray.direction.set(0,0,1).reflect(f),t.raycaster.ray.direction.x*=-1,t.raycaster.ray.direction.applyNormalMatrix(h).multiplyScalar(-1))}),[]);return a.useFrame((t=>{e&&t.gl.render(s.current,d)}),e),C.createElement(C.Fragment,null,C.createElement("mesh",T.default({ref:s},i,{scale:m}),C.createElement("sphereGeometry",{args:[1,r,r]}),C.createElement("meshBasicMaterial",null,C.createElement(_o,{compute:x,attach:"envMap",flip:!0,resolution:o,ref:l},n,C.createElement(Io,{api:l})))))},exports.Float=Nn,exports.FlyControls=ut,exports.GizmoHelper=({alignment:e="bottom-right",margin:t=[80,80],renderPriority:r=1,onUpdate:o,onTarget:i,children:s})=>{const l=a.useThree((e=>e.size)),c=a.useThree((e=>e.camera)),u=a.useThree((e=>e.controls)),d=a.useThree((e=>e.invalidate)),m=C.useRef(null),f=C.useRef(null),p=C.useRef(!1),h=C.useRef(0),x=C.useRef(new n.Vector3(0,0,0)),y=C.useRef(new n.Vector3(0,0,0));C.useEffect((()=>{y.current.copy(c.up),Pt.up.copy(c.up)}),[c]);const v=C.useCallback((e=>{p.current=!0,(u||i)&&(x.current=(null==i?void 0:i())||(At(u)?u.getTarget(x.current):null==u?void 0:u.target)),h.current=c.position.distanceTo(kt),Dt.copy(c.quaternion),_t.copy(e).multiplyScalar(h.current).add(kt),Pt.lookAt(_t),Ft.copy(Pt.quaternion),d()}),[u,c,i,d]);a.useFrame(((e,t)=>{if(f.current&&m.current){var r;if(p.current)if(Dt.angleTo(Ft)<.01)p.current=!1,"minPolarAngle"in u&&c.up.copy(y.current);else{const e=t*Ct;Dt.rotateTowards(Ft,e),c.position.set(0,0,1).applyQuaternion(Dt).multiplyScalar(h.current).add(x.current),c.up.set(0,1,0).applyQuaternion(Dt).normalize(),c.quaternion.copy(Dt),At(u)&&u.setPosition(c.position.x,c.position.y,c.position.z),o?o():u&&u.update(t),d()}Rt.copy(c.matrix).invert(),null==(r=m.current)||r.quaternion.setFromRotationMatrix(Rt)}}));const g=C.useMemo((()=>({tweenCamera:v})),[v]),[w,z]=t,b=e.endsWith("-center")?0:e.endsWith("-left")?-l.width/2+w:l.width/2-w,E=e.startsWith("center-")?0:e.startsWith("top-")?l.height/2-z:-l.height/2+z;return C.createElement(Mt,{renderPriority:r},C.createElement(St.Provider,{value:g},C.createElement(it,{makeDefault:!0,ref:f,position:[0,0,200]}),C.createElement("group",{ref:m,position:[b,E,0]},s)))},exports.GizmoViewcube=e=>C.createElement("group",{scale:[60,60,60]},C.createElement($t,e),Wt.map(((t,r)=>C.createElement(qt,T.default({key:r,position:t,dimensions:Gt[r]},e)))),Nt.map(((t,r)=>C.createElement(qt,T.default({key:r,position:t,dimensions:jt},e))))),exports.GizmoViewport=({hideNegativeAxes:e,hideAxisHeads:t,disabled:r,font:n="18px Inter var, Arial, sans-serif",axisColors:a=["#ff2060","#20df80","#2080ff"],axisHeadScale:o=1,axisScale:i,labels:s=["X","Y","Z"],labelColor:l="#000",onClick:c,...u})=>{const[d,m,f]=a,{tweenCamera:p}=Tt(),h={font:n,disabled:r,labelColor:l,onClick:c,axisHeadScale:o,onPointerDown:r?void 0:e=>{p(e.object.position),e.stopPropagation()}};return C.createElement("group",T.default({scale:40},u),C.createElement(Xt,{color:d,rotation:[0,0,0],scale:i}),C.createElement(Xt,{color:m,rotation:[0,0,Math.PI/2],scale:i}),C.createElement(Xt,{color:f,rotation:[0,-Math.PI/2,0],scale:i}),!t&&C.createElement(C.Fragment,null,C.createElement(Zt,T.default({arcStyle:d,position:[1,0,0],label:s[0]},h)),C.createElement(Zt,T.default({arcStyle:m,position:[0,1,0],label:s[1]},h)),C.createElement(Zt,T.default({arcStyle:f,position:[0,0,1],label:s[2]},h)),!e&&C.createElement(C.Fragment,null,C.createElement(Zt,T.default({arcStyle:d,position:[-1,0,0]},h)),C.createElement(Zt,T.default({arcStyle:m,position:[0,-1,0]},h)),C.createElement(Zt,T.default({arcStyle:f,position:[0,0,-1]},h)))))},exports.Gltf=et,exports.GradientTexture=function({stops:e,colors:t,size:r=1024,width:n=16,type:o=Se.Linear,innerCircleRadius:i=0,outerCircleRadius:s="auto",...l}){const c=a.useThree((e=>e.gl)),u=C.useMemo((()=>{const a=document.createElement("canvas"),l=a.getContext("2d");let c;if(a.width=n,a.height=r,o===Se.Linear)c=l.createLinearGradient(0,0,0,r);else{const e=a.width/2,t=a.height/2,r="auto"!==s?Math.abs(Number(s)):Math.sqrt(e**2+t**2);c=l.createRadialGradient(e,t,Math.abs(i),e,t,r)}const u=new R.Color;let d=e.length;for(;d--;)c.addColorStop(e[d],u.set(t[d]).getStyle());return l.save(),l.fillStyle=c,l.fillRect(0,0,n,r),l.restore(),a}),[e]);return C.createElement("canvasTexture",T.default({colorSpace:c.outputColorSpace,args:[u],attach:"map"},l))},exports.GradientType=Se,exports.Grid=Qt,exports.Helper=({type:e,args:t=[]})=>{const r=C.useRef(null),n=C.useRef(null);return C.useLayoutEffect((()=>{n.current=r.current.parent})),ur(n,e,...t),C.createElement("object3D",{ref:r})},exports.Html=H,exports.Hud=Mt,exports.Icosahedron=bn,exports.Image=Ae,exports.Instance=Br,exports.InstancedAttribute=Or,exports.Instances=Vr,exports.IsObject=Ce,exports.KeyboardControls=function({map:e,children:t,onChange:r,domElement:n}){const a=e.map((e=>e.name+e.keys)).join("-"),i=C.useMemo((()=>o.create(c.subscribeWithSelector((()=>e.reduce(((e,t)=>({...e,[t.name]:!1})),{}))))),[a]),s=C.useMemo((()=>[i.subscribe,i.getState,i]),[a]),l=i.setState;return C.useEffect((()=>{const t=e.map((({name:e,keys:t,up:n})=>({keys:t,up:n,fn:t=>{l({[e]:t}),r&&r(e,t,s[1]())}}))).reduce(((e,{keys:t,fn:r,up:n=!0})=>(t.forEach((t=>e[t]={fn:r,pressed:!1,up:n})),e)),{}),a=({key:e,code:r})=>{const n=t[e]||t[r];if(!n)return;const{fn:a,pressed:o,up:i}=n;n.pressed=!0,!i&&o||a(!0)},o=({key:e,code:r})=>{const n=t[e]||t[r];if(!n)return;const{fn:a,up:o}=n;n.pressed=!1,o&&a(!1)},i=n||window;return i.addEventListener("keydown",a,{passive:!0}),i.addEventListener("keyup",o,{passive:!0}),()=>{i.removeEventListener("keydown",a),i.removeEventListener("keyup",o)}}),[n,a]),C.createElement(te.Provider,{value:s,children:t})},exports.Ktx2=({children:e,input:t,basisPath:r})=>{const n=tr(t,r);return C.createElement(C.Fragment,null,null==e?void 0:e(n))},exports.Lathe=Tn,exports.Lightformer=Ca,exports.Line=me,exports.Loader=function({containerStyles:e,innerStyles:t,barStyles:r,dataStyles:n,dataInterpolation:a=X,initialState:o=e=>e}){const{active:i,progress:s}=q(),l=C.useRef(0),c=C.useRef(0),u=C.useRef(null),[d,m]=C.useState(o(i));C.useEffect((()=>{let e;return i!==d&&(e=setTimeout((()=>m(i)),300)),()=>clearTimeout(e)}),[d,i]);const f=C.useCallback((()=>{u.current&&(l.current+=(s-l.current)/2,(l.current>.95*s||100===s)&&(l.current=s),u.current.innerText=a(l.current),l.current<s&&(c.current=requestAnimationFrame(f)))}),[a,s]);return C.useEffect((()=>(f(),()=>cancelAnimationFrame(c.current))),[f]),d?C.createElement("div",{style:{...Z.container,opacity:i?1:0,...e}},C.createElement("div",null,C.createElement("div",{style:{...Z.inner,...t}},C.createElement("div",{style:{...Z.bar,transform:`scaleX(${s/100})`,...r}}),C.createElement("span",{ref:u,style:{...Z.data,...n}})))):null},exports.MapControls=dt,exports.MarchingCube=He,exports.MarchingCubes=Ge,exports.MarchingPlane=$e,exports.Mask=Lo,exports.MatcapTexture=({children:e,id:t,format:r,onLoad:n})=>{const a=Za(t,r,n);return C.createElement(C.Fragment,null,null==e?void 0:e(a))},exports.Merged=Ur,exports.MeshDiscardMaterial=nn,exports.MeshDistortMaterial=$r,exports.MeshPortalMaterial=Vo,exports.MeshReflectorMaterial=Kr,exports.MeshRefractionMaterial=function({aberrationStrength:e=0,fastChroma:r=!0,envMap:n,...o}){a.extend({MeshRefractionMaterial:Jr});const i=t.useRef(),{size:s}=a.useThree(),l=t.useMemo((()=>{var t,a;const o={},i=(s=n)&&s.isCubeTexture;var s;const l=(null!==(t=i?null==(a=n.image[0])?void 0:a.width:n.image.width)&&void 0!==t?t:1024)/4,c=Math.floor(Math.log2(l)),u=Math.pow(2,c),d=3*Math.max(u,112),m=4*u;return i&&(o.ENVMAP_TYPE_CUBEM=""),o.CUBEUV_TEXEL_WIDTH=""+1/d,o.CUBEUV_TEXEL_HEIGHT=""+1/m,o.CUBEUV_MAX_MIP=`${c}.0`,e>0&&(o.CHROMATIC_ABERRATIONS=""),r&&(o.FAST_CHROMA=""),o}),[e,r]);return t.useLayoutEffect((()=>{var e;const t=null==(e=i.current)||null==(e=e.__r3f)||null==(e=e.parent)?void 0:e.geometry;t&&(i.current.bvh=new w.MeshBVHUniformStruct,i.current.bvh.updateFrom(new w.MeshBVH(t.clone().toNonIndexed(),{strategy:w.SAH})))}),[]),a.useFrame((({camera:e})=>{i.current.viewMatrixInverse=e.matrixWorld,i.current.projectionMatrixInverse=e.projectionMatrixInverse})),C.createElement("meshRefractionMaterial",T.default({key:JSON.stringify(l),defines:l,ref:i,resolution:[s.width,s.height],aberrationStrength:e,envMap:n},o))},exports.MeshTransmissionMaterial=rn,exports.MeshWobbleMaterial=Xr,exports.MotionPathControls=bt,exports.MultiMaterial=function(e){const t=C.useRef(null);return C.useLayoutEffect((()=>{var e;const r=null==(e=t.current)?void 0:e.parent,n=null==r?void 0:r.geometry;if(n){const e=r.material;r.material=t.current.__r3f.objects;const a=[...n.groups];return n.clearGroups(),r.material.forEach(((e,t)=>{t<r.material.length-1&&(e.depthWrite=!1),n.addGroup(0,1/0,t)})),()=>{r.material=e,n.groups=a}}})),C.createElement("group",T.default({ref:t},e))},exports.NormalTexture=({children:e,id:t,onLoad:r,...n})=>{const a=Ya(t,n,r);return C.createElement(C.Fragment,null,null==e?void 0:e(a))},exports.Octahedron=En,exports.OrbitControls=mt,exports.OrthographicCamera=it,exports.Outlines=function({color:e="black",opacity:t=1,transparent:r=!1,screenspace:n=!1,toneMapped:o=!0,polygonOffset:i=!1,polygonOffsetFactor:s=0,renderOrder:l=0,thickness:c=.05,angle:d=Math.PI,clippingPlanes:m,...f}){const p=C.useRef(),[h]=C.useState((()=>new Ie({side:R.BackSide}))),{gl:x}=a.useThree(),y=x.getDrawingBufferSize(new R.Vector2);C.useMemo((()=>a.extend({OutlinesMaterial:Ie})),[]);const v=C.useRef(0),g=C.useRef();return C.useLayoutEffect((()=>{const e=p.current;if(!e)return;const t=e.parent;if(t&&t.geometry&&(v.current!==d||g.current!==t.geometry)){var r;v.current=d,g.current=t.geometry;let n=null==(r=e.children)?void 0:r[0];n&&(d&&n.geometry.dispose(),e.remove(n)),t.skeleton?(n=new R.SkinnedMesh,n.material=h,n.bind(t.skeleton,t.bindMatrix),e.add(n)):t.isInstancedMesh?(n=new R.InstancedMesh(t.geometry,h,t.count),n.instanceMatrix=t.instanceMatrix,e.add(n)):(n=new R.Mesh,n.material=h,e.add(n)),n.geometry=d?u.toCreasedNormals(t.geometry,d):t.geometry,n.morphTargetInfluences=t.morphTargetInfluences,n.morphTargetDictionary=t.morphTargetDictionary}})),C.useLayoutEffect((()=>{const u=p.current;if(!u)return;const d=u.children[0];if(d){d.renderOrder=l;const f=u.parent;a.applyProps(d,{morphTargetInfluences:f.morphTargetInfluences,morphTargetDictionary:f.morphTargetDictionary}),a.applyProps(d.material,{transparent:r,thickness:c,color:e,opacity:t,size:y,screenspace:n,toneMapped:o,polygonOffset:i,polygonOffsetFactor:s,clippingPlanes:m,clipping:m&&m.length>0})}})),C.useEffect((()=>()=>{const e=p.current;if(!e)return;const t=e.children[0];t&&(d&&t.geometry.dispose(),e.remove(t))}),[]),C.createElement("group",T.default({ref:p},f))},exports.PerformanceMonitor=function({iterations:e=10,ms:r=250,threshold:n=.75,step:o=.1,factor:i=.5,flipflops:s=1/0,bounds:l=e=>e>100?[60,100]:[40,60],onIncline:c,onDecline:u,onChange:d,onFallback:m,children:f}){const p=Math.pow(10,0),[h,x]=t.useState((()=>({fps:0,index:0,factor:i,flipped:0,refreshrate:0,fallback:!1,frames:[],averages:[],subscriptions:new Map,subscribe:e=>{const t=Symbol();return h.subscriptions.set(t,e.current),()=>{h.subscriptions.delete(t)}}})));let y=0;return a.useFrame((()=>{const{frames:t,averages:a}=h;if(!h.fallback&&a.length<e){t.push(performance.now());const i=t[t.length-1]-t[0];if(i>=r){if(h.fps=Math.round(t.length/i*1e3*p)/p,h.refreshrate=Math.max(h.refreshrate,h.fps),a[h.index++%e]=h.fps,a.length===e){const[t,r]=l(h.refreshrate),i=a.filter((e=>e>=r)),f=a.filter((e=>e<t));i.length>e*n&&(h.factor=Math.min(1,h.factor+o),h.flipped++,c&&c(h),h.subscriptions.forEach((e=>e.onIncline&&e.onIncline(h)))),f.length>e*n&&(h.factor=Math.max(0,h.factor-o),h.flipped++,u&&u(h),h.subscriptions.forEach((e=>e.onDecline&&e.onDecline(h)))),y!==h.factor&&(y=h.factor,d&&d(h),h.subscriptions.forEach((e=>e.onChange&&e.onChange(h)))),h.flipped>s&&!h.fallback&&(h.fallback=!0,m&&m(h),h.subscriptions.forEach((e=>e.onFallback&&e.onFallback(h)))),h.averages=[]}h.frames=[]}}})),C.createElement(Do.Provider,{value:h},f)},exports.PerspectiveCamera=st,exports.PivotControls=ji,exports.Plane=hn,exports.Point=yo,exports.PointMaterial=sn,exports.PointMaterialImpl=on,exports.PointerLockControls=xt,exports.Points=go,exports.PointsBuffer=vo,exports.Polyhedron=zn,exports.PositionMesh=Rr,exports.PositionPoint=co,exports.PositionalAudio=ye,exports.Preload=function({all:e,scene:t,camera:r}){const o=a.useThree((({gl:e})=>e)),i=a.useThree((({camera:e})=>e)),s=a.useThree((({scene:e})=>e));return C.useLayoutEffect((()=>{const a=[];e&&(t||s).traverse((e=>{!1===e.visible&&(a.push(e),e.visible=!0)})),o.compile(t||s,r||i);const l=new n.WebGLCubeRenderTarget(128);new n.CubeCamera(.01,1e5,l).update(o,t||s),l.dispose(),a.forEach((e=>e.visible=!1))}),[]),null},exports.PresentationControls=function({enabled:e=!0,snap:t,global:r,domElement:o,cursor:s=!0,children:c,speed:u=1,rotation:d=[0,0,0],zoom:m=1,polar:f=[0,Math.PI/2],azimuth:p=[-1/0,1/0],config:h={mass:1,tension:170,friction:26}}){const x=a.useThree((e=>e.events)),y=a.useThree((e=>e.gl)),v=o||x.connected||y.domElement,{size:g}=a.useThree(),w=C.useMemo((()=>[d[0]+f[0],d[0]+f[1]]),[d[0],f[0],f[1]]),z=C.useMemo((()=>[d[1]+p[0],d[1]+p[1]]),[d[1],p[0],p[1]]),b=C.useMemo((()=>[n.MathUtils.clamp(d[0],...w),n.MathUtils.clamp(d[1],...z),d[2]]),[d[0],d[1],d[2],w,z]),[E,M]=l.useSpring((()=>({scale:1,rotation:b,config:h})));C.useEffect((()=>{M.start({scale:1,rotation:b,config:h})}),[b]),C.useEffect((()=>{if(r&&s&&e)return v.style.cursor="grab",y.domElement.style.cursor="",()=>{v.style.cursor="default",y.domElement.style.cursor="default"}}),[r,s,v,e]);const S=i.useGesture({onHover:({last:t})=>{s&&!r&&e&&(v.style.cursor=t?"auto":"grab")},onDrag:({down:r,delta:[a,o],memo:[i,l]=E.rotation.animation.to||b})=>{if(!e)return[o,a];s&&(v.style.cursor=r?"grabbing":"grab"),a=n.MathUtils.clamp(l+a/g.width*Math.PI*u,...z),o=n.MathUtils.clamp(i+o/g.height*Math.PI*u,...w);const c=t&&!r&&"boolean"!=typeof t?t:h;return M.start({scale:r&&o>w[1]/2?m:1,rotation:t&&!r?b:[o,a,0],config:e=>"scale"===e?{...c,friction:3*c.friction}:c}),[o,a]}},{target:r?v:void 0});return C.createElement(l.a.group,T.default({},null==S?void 0:S(),E),c)},exports.Progress=function({children:e}){const t=q();return C.createElement(C.Fragment,null,null==e?void 0:e(t))},exports.QuadraticBezierLine=pe,exports.RandomizedLight=la,exports.Reflector=wa,exports.RenderCubeTexture=_o,exports.RenderTexture=Fo,exports.Resize=_n,exports.Ring=wn,exports.RoundedBox=Dn,exports.Sampler=function({children:e,weight:t,transform:r,instances:n,mesh:a,count:o=16,...i}){const s=C.useRef(null),l=C.useRef(null),c=C.useRef(null);return C.useLayoutEffect((()=>{var e,t;l.current=null!==(e=null==n?void 0:n.current)&&void 0!==e?e:s.current.children.find((e=>e.hasOwnProperty("instanceMatrix"))),c.current=null!==(t=null==a?void 0:a.current)&&void 0!==t?t:s.current.children.find((e=>"Mesh"===e.type))}),[e,null==a?void 0:a.current,null==n?void 0:n.current]),Ne(c,o,r,t,l),C.createElement("group",T.default({ref:s},i),e)},exports.ScreenQuad=kn,exports.ScreenSizer=de,exports.ScreenSpace=ae,exports.ScreenVideoTexture=Wi,exports.Scroll=ee,exports.ScrollControls=function({eps:e=1e-5,enabled:t=!0,infinite:r,horizontal:n,pages:o=1,distance:i=1,damping:l=.25,maxSpeed:c=1/0,prepend:u=!1,style:d={},children:m}){const{get:f,setEvents:p,gl:h,size:x,invalidate:y,events:v}=a.useThree(),[g]=C.useState((()=>document.createElement("div"))),[w]=C.useState((()=>document.createElement("div"))),[z]=C.useState((()=>document.createElement("div"))),b=h.domElement.parentNode,E=C.useRef(0),M=C.useMemo((()=>{const t={el:g,eps:e,fill:w,fixed:z,horizontal:n,damping:l,offset:0,delta:0,scroll:E,pages:o,range(e,t,r=0){const n=e-r,a=n+t+2*r;return this.offset<n?0:this.offset>a?1:(this.offset-n)/(a-n)},curve(e,t,r=0){return Math.sin(this.range(e,t,r)*Math.PI)},visible(e,t,r=0){const n=e-r,a=n+t+2*r;return this.offset>=n&&this.offset<=a}};return t}),[e,l,n,o]);C.useEffect((()=>{g.style.position="absolute",g.style.width="100%",g.style.height="100%",g.style[n?"overflowX":"overflowY"]="auto",g.style[n?"overflowY":"overflowX"]="hidden",g.style.top="0px",g.style.left="0px";for(const e in d)g.style[e]=d[e];z.style.position="sticky",z.style.top="0px",z.style.left="0px",z.style.width="100%",z.style.height="100%",z.style.overflow="hidden",g.appendChild(z),w.style.height=n?"100%":o*i*100+"%",w.style.width=n?o*i*100+"%":"100%",w.style.pointerEvents="none",g.appendChild(w),u?b.prepend(g):b.appendChild(g),g[n?"scrollLeft":"scrollTop"]=1;const e=v.connected||h.domElement;requestAnimationFrame((()=>null==v.connect?void 0:v.connect(g)));const t=f().events.compute;return p({compute(e,t){const{left:r,top:n}=b.getBoundingClientRect(),a=e.clientX-r,o=e.clientY-n;t.pointer.set(a/t.size.width*2-1,-o/t.size.height*2+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),()=>{b.removeChild(g),p({compute:t}),null==v.connect||v.connect(e)}}),[o,i,n,g,w,z,b]),C.useEffect((()=>{if(v.connected===g){const e=x[n?"width":"height"],a=g[n?"scrollWidth":"scrollHeight"],o=a-e;let i=0,s=!0,l=!0;const c=()=>{if(t&&!l&&(y(),i=g[n?"scrollLeft":"scrollTop"],E.current=i/o,r)){if(!s)if(i>=o){const e=1-M.offset;g[n?"scrollLeft":"scrollTop"]=1,E.current=M.offset=-e,s=!0}else if(i<=0){const e=1+M.offset;g[n?"scrollLeft":"scrollTop"]=a,E.current=M.offset=e,s=!0}s&&setTimeout((()=>s=!1),40)}};g.addEventListener("scroll",c,{passive:!0}),requestAnimationFrame((()=>l=!1));const u=e=>g.scrollLeft+=e.deltaY/2;return n&&g.addEventListener("wheel",u,{passive:!0}),()=>{g.removeEventListener("scroll",c),n&&g.removeEventListener("wheel",u)}}}),[g,v,x,r,M,y,n,t]);let S=0;return a.useFrame(((t,r)=>{S=M.offset,s.easing.damp(M,"offset",E.current,l,r,c,void 0,e),s.easing.damp(M,"delta",Math.abs(S-M.offset),l,r,c,void 0,e),M.delta>e&&y()})),C.createElement(Y.Provider,{value:M},m)},exports.Segment=Mo,exports.SegmentObject=bo,exports.Segments=zo,exports.Select=function({box:e,multiple:t,children:r,onChange:n,onChangePointerUp:o,border:i="1px solid #55aaff",backgroundColor:s="rgba(75, 160, 255, 0.1)",filter:l=e=>e,...c}){const[m,f]=C.useState(!1),{setEvents:p,camera:h,raycaster:x,gl:y,controls:v,size:g,get:w}=a.useThree(),[z,b]=C.useState(!1),[E,M]=C.useReducer(((e,{object:t,shift:r})=>void 0===t?[]:Array.isArray(t)?t:r?e.includes(t)?e.filter((e=>e!==t)):[t,...e]:e[0]===t?[]:[t]),[]);C.useEffect((()=>{m?null==n||n(E):null==o||o(E)}),[E,m]);const S=C.useCallback((e=>{e.stopPropagation(),M({object:l([e.object])[0],shift:t&&e.shiftKey})}),[]),P=C.useCallback((e=>!z&&M({})),[z]),D=C.useRef(null);return C.useEffect((()=>{if(!e||!t)return;const r=new u.SelectionBox(h,D.current),n=document.createElement("div");n.style.pointerEvents="none",n.style.border=i,n.style.backgroundColor=s,n.style.position="fixed";const a=new R.Vector2,o=new R.Vector2,c=new R.Vector2,m=w().events.enabled,x=null==v?void 0:v.enabled;let z=!1;function b(e,t){const{offsetX:r,offsetY:n}=e,{width:a,height:o}=g;t.set(r/a*2-1,-n/o*2+1)}function E(e){e.shiftKey&&(!function(e){var t;v&&(v.enabled=!1),p({enabled:!1}),f(z=!0),null==(t=y.domElement.parentElement)||t.appendChild(n),n.style.left=`${e.clientX}px`,n.style.top=`${e.clientY}px`,n.style.width="0px",n.style.height="0px",a.x=e.clientX,a.y=e.clientY}(e),b(e,r.startPoint))}let S=[];function T(e){if(z){!function(e){c.x=Math.max(a.x,e.clientX),c.y=Math.max(a.y,e.clientY),o.x=Math.min(a.x,e.clientX),o.y=Math.min(a.y,e.clientY),n.style.left=`${o.x}px`,n.style.top=`${o.y}px`,n.style.width=c.x-o.x+"px",n.style.height=c.y-o.y+"px"}(e),b(e,r.endPoint);const t=r.select().sort((e=>e.uuid)).filter((e=>e.isMesh));d.shallow(t,S)||(S=t,M({object:l(t)}))}}function C(e){var t;z&&z&&(v&&(v.enabled=x),p({enabled:m}),f(z=!1),null==(t=n.parentElement)||t.removeChild(n))}return document.addEventListener("pointerdown",E,{passive:!0}),document.addEventListener("pointermove",T,{passive:!0,capture:!0}),document.addEventListener("pointerup",C,{passive:!0}),()=>{document.removeEventListener("pointerdown",E),document.removeEventListener("pointermove",T,!0),document.removeEventListener("pointerup",C)}}),[g.width,g.height,x,h,v,y]),C.createElement("group",T.default({ref:D,onClick:S,onPointerOver:()=>b(!0),onPointerOut:()=>b(!1),onPointerMissed:P},c),C.createElement(re.Provider,{value:E},r))},exports.Shadow=fa,exports.ShadowAlpha=function({opacity:e,alphaMap:t}){const r=C.useRef(null),n=C.useRef(null),o=C.useRef({value:1}),i=C.useRef({value:null}),s=C.useRef({value:!1});return C.useLayoutEffect((()=>{r.current.onBeforeCompile=n.current.onBeforeCompile=e=>{const t=e.fragmentShader.indexOf("void main");let r,n="",a=t;for(;"\n"!==r&&a<t+100;)r=e.fragmentShader.charAt(a),n+=r,a++;n=n.trim(),e.vertexShader=e.vertexShader.replace("void main() {","\n        varying vec2 custom_vUv;\n\n        void main() {\n          custom_vUv = uv;\n          \n        "),e.fragmentShader=e.fragmentShader.replace(n,"\n          uniform float uShadowOpacity;\n          uniform sampler2D uAlphaMap;\n          uniform bool uHasAlphaMap;\n\n          varying vec2 custom_vUv;\n  \n          float bayerDither2x2( vec2 v ) {\n            return mod( 3.0 * v.y + 2.0 * v.x, 4.0 );\n          }\n    \n          float bayerDither4x4( vec2 v ) {\n            vec2 P1 = mod( v, 2.0 );\n            vec2 P2 = mod( floor( 0.5  * v ), 2.0 );\n            return 4.0 * bayerDither2x2( P1 ) + bayerDither2x2( P2 );\n          }\n  \n          void main() {\n            float alpha = \n              uHasAlphaMap ? \n                uShadowOpacity * texture2D(uAlphaMap, custom_vUv).x\n              : uShadowOpacity;\n\n            if( ( bayerDither4x4( floor( mod( gl_FragCoord.xy, 4.0 ) ) ) ) / 16.0 >= alpha ) discard;\n            \n          "),e.uniforms.uShadowOpacity=o.current,e.uniforms.uAlphaMap=i.current,e.uniforms.uHasAlphaMap=s.current}}),[]),a.useFrame((()=>{var n;const a=null==(n=r.current.__r3f)?void 0:n.parent;if(a){const r=a.material;r&&(o.current.value=null!=e?e:r.opacity,!1===t?(i.current.value=null,s.current.value=!1):(i.current.value=t||r.alphaMap,s.current.value=!!i.current.value))}})),C.createElement(C.Fragment,null,C.createElement("meshDepthMaterial",{ref:r,attach:"customDepthMaterial",depthPacking:R.RGBADepthPacking}),C.createElement("meshDistanceMaterial",{ref:n,attach:"customDistanceMaterial"}))},exports.Shape=Pn,exports.Sky=Ra,exports.SoftShadows=function({focus:e=0,samples:t=10,size:r=25}){const n=a.useThree((e=>e.gl)),o=a.useThree((e=>e.scene)),i=a.useThree((e=>e.camera));return C.useEffect((()=>{const a=R.ShaderChunk.shadowmap_pars_fragment;return R.ShaderChunk.shadowmap_pars_fragment=R.ShaderChunk.shadowmap_pars_fragment.replace("#ifdef USE_SHADOWMAP","#ifdef USE_SHADOWMAP\n"+(({focus:e=0,size:t=25,samples:r=10}={})=>`\n#define PENUMBRA_FILTER_SIZE float(${t})\n#define RGB_NOISE_FUNCTION(uv) (randRGB(uv))\nvec3 randRGB(vec2 uv) {\n  return vec3(\n    fract(sin(dot(uv, vec2(12.75613, 38.12123))) * 13234.76575),\n    fract(sin(dot(uv, vec2(19.45531, 58.46547))) * 43678.23431),\n    fract(sin(dot(uv, vec2(23.67817, 78.23121))) * 93567.23423)\n  );\n}\n\nvec3 lowPassRandRGB(vec2 uv) {\n  // 3x3 convolution (average)\n  // can be implemented as separable with an extra buffer for a total of 6 samples instead of 9\n  vec3 result = vec3(0);\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(-1.0, +1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2( 0.0, +1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0, -1.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0,  0.0));\n  result += RGB_NOISE_FUNCTION(uv + vec2(+1.0, +1.0));\n  result *= 0.111111111; // 1.0 / 9.0\n  return result;\n}\nvec3 highPassRandRGB(vec2 uv) {\n  // by subtracting the low-pass signal from the original signal, we're being left with the high-pass signal\n  // hp(x) = x - lp(x)\n  return RGB_NOISE_FUNCTION(uv) - lowPassRandRGB(uv) + 0.5;\n}\n\n\nvec2 vogelDiskSample(int sampleIndex, int sampleCount, float angle) {\n  const float goldenAngle = 2.399963f; // radians\n  float r = sqrt(float(sampleIndex) + 0.5f) / sqrt(float(sampleCount));\n  float theta = float(sampleIndex) * goldenAngle + angle;\n  float sine = sin(theta);\n  float cosine = cos(theta);\n  return vec2(cosine, sine) * r;\n}\nfloat penumbraSize( const in float zReceiver, const in float zBlocker ) { // Parallel plane estimation\n  return (zReceiver - zBlocker) / zBlocker;\n}\nfloat findBlocker(sampler2D shadowMap, vec2 uv, float compare, float angle) {\n  float texelSize = 1.0 / float(textureSize(shadowMap, 0).x);\n  float blockerDepthSum = float(${e});\n  float blockers = 0.0;\n\n  int j = 0;\n  vec2 offset = vec2(0.);\n  float depth = 0.;\n\n  #pragma unroll_loop_start\n  for(int i = 0; i < ${r}; i ++) {\n    offset = (vogelDiskSample(j, ${r}, angle) * texelSize) * 2.0 * PENUMBRA_FILTER_SIZE;\n    depth = unpackRGBAToDepth( texture2D( shadowMap, uv + offset));\n    if (depth < compare) {\n      blockerDepthSum += depth;\n      blockers++;\n    }\n    j++;\n  }\n  #pragma unroll_loop_end\n\n  if (blockers > 0.0) {\n    return blockerDepthSum / blockers;\n  }\n  return -1.0;\n}\n\n        \nfloat vogelFilter(sampler2D shadowMap, vec2 uv, float zReceiver, float filterRadius, float angle) {\n  float texelSize = 1.0 / float(textureSize(shadowMap, 0).x);\n  float shadow = 0.0f;\n  int j = 0;\n  vec2 vogelSample = vec2(0.0);\n  vec2 offset = vec2(0.0);\n  #pragma unroll_loop_start\n  for (int i = 0; i < ${r}; i++) {\n    vogelSample = vogelDiskSample(j, ${r}, angle) * texelSize;\n    offset = vogelSample * (1.0 + filterRadius * float(${t}));\n    shadow += step( zReceiver, unpackRGBAToDepth( texture2D( shadowMap, uv + offset ) ) );\n    j++;\n  }\n  #pragma unroll_loop_end\n  return shadow * 1.0 / ${r}.0;\n}\n\nfloat PCSS (sampler2D shadowMap, vec4 coords) {\n  vec2 uv = coords.xy;\n  float zReceiver = coords.z; // Assumed to be eye-space z in this code\n  float angle = highPassRandRGB(gl_FragCoord.xy).r * PI2;\n  float avgBlockerDepth = findBlocker(shadowMap, uv, zReceiver, angle);\n  if (avgBlockerDepth == -1.0) {\n    return 1.0;\n  }\n  float penumbraRatio = penumbraSize(zReceiver, avgBlockerDepth);\n  return vogelFilter(shadowMap, uv, zReceiver, 1.25 * penumbraRatio, angle);\n}`)({size:r,samples:t,focus:e})).replace("#if defined( SHADOWMAP_TYPE_PCF )","\nreturn PCSS(shadowMap, shadowCoord);\n#if defined( SHADOWMAP_TYPE_PCF )"),ln(n,o,i),()=>{R.ShaderChunk.shadowmap_pars_fragment=a,ln(n,o,i)}}),[e,r,t]),null},exports.Sparkles=Xa,exports.Sphere=pn,exports.Splat=function({src:e,toneMapped:t=!1,alphaTest:r=0,alphaHash:n=!1,chunkSize:o=25e3,...i}){a.extend({SplatMaterial:tt});const s=C.useRef(null),l=a.useThree((e=>e.gl)),c=a.useThree((e=>e.camera)),u=a.useLoader(nt,e,(e=>{e.gl=l,e.chunkSize=o}));return C.useLayoutEffect((()=>u.connect(s.current)),[e]),a.useFrame((()=>u.update(s.current,c,n))),C.createElement("mesh",T.default({ref:s,frustumCulled:!1},i),C.createElement("splatMaterial",{key:`${e}/${r}/${n}${tt.key}`,transparent:!n,depthTest:!0,alphaTest:n?0:r,centerAndScaleTexture:u.centerAndScaleTexture,covAndColorTexture:u.covAndColorTexture,depthWrite:!!n||r>0,blending:n?R.NormalBlending:R.CustomBlending,blendSrcAlpha:R.OneFactor,alphaHash:!!n,toneMapped:t}))},exports.SpotLight=Ta,exports.SpotLightShadow=function(e){return e.shader?C.createElement(Ma,e):C.createElement(Sa,e)},exports.SpriteAnimator=Wr,exports.Stage=function({children:e,center:t,adjustCamera:r=!0,intensity:n=.5,shadows:a="contact",environment:o="city",preset:i="rembrandt",...s}){var l,c,u,d,m,f,p,h;const x="string"==typeof i?ua[i]:i,[{radius:y,height:v},g]=C.useState({radius:0,width:0,height:0,depth:0}),w=null!==(l=null==a?void 0:a.bias)&&void 0!==l?l:-1e-4,z=null!==(c=null==a?void 0:a.normalBias)&&void 0!==c?c:0,b=null!==(u=null==a?void 0:a.size)&&void 0!==u?u:1024,E=null!==(d=null==a?void 0:a.offset)&&void 0!==d?d:0,M="contact"===a||"contact"===(null==a?void 0:a.type),S="accumulative"===a||"accumulative"===(null==a?void 0:a.type),P={..."object"==typeof a?a:{}},R=o?"string"==typeof o?{preset:o}:o:null,D=C.useCallback((e=>{const{width:r,height:n,depth:a,boundingSphere:o}=e;g({radius:o.radius,width:r,height:n,depth:a}),null!=t&&t.onCentered&&t.onCentered(e)}),[]);return C.createElement(C.Fragment,null,C.createElement("ambientLight",{intensity:n/3}),C.createElement("spotLight",{penumbra:1,position:[x.main[0]*y,x.main[1]*y,x.main[2]*y],intensity:2*n,castShadow:!!a,"shadow-bias":w,"shadow-normalBias":z,"shadow-mapSize":b}),C.createElement("pointLight",{position:[x.fill[0]*y,x.fill[1]*y,x.fill[2]*y],intensity:n}),C.createElement(Vn,T.default({fit:!!r,clip:!!r,margin:Number(r),observe:!0},s),C.createElement(da,{radius:y,adjustCamera:r}),C.createElement(br,T.default({},t,{position:[0,E/2,0],onCentered:D}),e)),C.createElement("group",{position:[0,-v/2-E/2,0]},M&&C.createElement(aa,T.default({scale:4*y,far:y,blur:2},P)),S&&C.createElement(sa,T.default({temporal:!0,frames:100,alphaTest:.9,toneMapped:!0,scale:4*y},P),C.createElement(la,{amount:null!==(m=P.amount)&&void 0!==m?m:8,radius:null!==(f=P.radius)&&void 0!==f?f:y,ambient:null!==(p=P.ambient)&&void 0!==p?p:.5,intensity:null!==(h=P.intensity)&&void 0!==h?h:1,position:[x.main[0]*y,x.main[1]*y,x.main[2]*y],size:4*y,bias:-w,mapSize:b}))),o&&C.createElement(na,R))},exports.Stars=ka,exports.Stats=function({showPanel:e=0,className:t,parent:r}){const n=function(e,t=[],r){const[n,a]=C.useState();return C.useLayoutEffect((()=>{const t=e();return a(t),dr(r,t),()=>dr(r,null)}),t),n}((()=>new F.default),[]);return C.useEffect((()=>{if(n){const o=r&&r.current||document.body;n.showPanel(e),null==o||o.appendChild(n.dom);const i=(null!=t?t:"").split(" ").filter((e=>e));i.length&&n.dom.classList.add(...i);const s=a.addEffect((()=>n.begin())),l=a.addAfterEffect((()=>n.end()));return()=>{i.length&&n.dom.classList.remove(...i),null==o||o.removeChild(n.dom),s(),l()}}}),[r,n,t,e]),null},exports.StatsGl=mr,exports.Svg=Ze,exports.Tetrahedron=gn,exports.Text=ve,exports.Text3D=Ee,exports.Texture=({children:e,input:t,onLoad:r})=>{const n=Pe(t,r);return C.createElement(C.Fragment,null,null==e?void 0:e(n))},exports.Torus=yn,exports.TorusKnot=vn,exports.TrackballControls=ft,exports.Trail=Oe,exports.TrailTexture=({children:e,...t})=>{const r=zr(t);return C.createElement(C.Fragment,null,null==e?void 0:e(r))},exports.TransformControls=ht,exports.Tube=xn,exports.VideoTexture=or,exports.View=Qo,exports.WebcamVideoTexture=Gi,exports.Wireframe=function({geometry:e,...t}){return e?C.createElement(no,T.default({geometry:e},t)):C.createElement(ao,t)},exports.accumulativeContext=oa,exports.calcPosFromAngles=Pa,exports.calculateScaleFactor=ce,exports.checkIfFrameIsEmpty=lr,exports.createInstances=function(){const e=C.createContext(null);return[C.forwardRef(((t,r)=>C.createElement(Vr,T.default({ref:r,context:e},t)))),C.forwardRef(((t,r)=>C.createElement(Br,T.default({ref:r,context:e},t))))]},exports.getFirstFrame=sr,exports.isWebGL2Available=()=>{try{var e=document.createElement("canvas");return!(!window.WebGL2RenderingContext||!e.getContext("webgl2"))}catch(e){return!1}},exports.meshBounds=function(e,t){const r=this.geometry,n=this.material,a=this.matrixWorld;void 0!==n&&(null===r.boundingSphere&&r.computeBoundingSphere(),Po.copy(r.boundingSphere),Po.applyMatrix4(a),!1!==e.ray.intersectsSphere(Po)&&(To.copy(a).invert(),Co.copy(e.ray).applyMatrix4(To),null!==r.boundingBox&&null===Co.intersectBox(r.boundingBox,Ro)||t.push({distance:Ro.distanceTo(e.ray.origin),point:Ro.clone(),object:this})))},exports.shaderMaterial=Te,exports.useAnimations=function(e,t){const r=C.useRef(),[o]=C.useState((()=>t?t instanceof n.Object3D?{current:t}:t:r)),[i]=C.useState((()=>new n.AnimationMixer(void 0)));C.useLayoutEffect((()=>{t&&(o.current=t instanceof n.Object3D?t:t.current),i._root=o.current}));const s=C.useRef({}),l=C.useMemo((()=>{const t={};return e.forEach((e=>Object.defineProperty(t,e.name,{enumerable:!0,get(){if(o.current)return s.current[e.name]||(s.current[e.name]=i.clipAction(e,o.current))},configurable:!0}))),{ref:o,clips:e,actions:t,names:e.map((e=>e.name)),mixer:i}}),[e]);return a.useFrame(((e,t)=>i.update(t))),C.useEffect((()=>{const e=o.current;return()=>{s.current={},i.stopAllAction(),Object.values(l.actions).forEach((t=>{e&&i.uncacheAction(t,e)}))}}),[e]),l},exports.useAspect=function(e,t,r=1){const n=a.useThree((e=>e.viewport)),o=t*(n.aspect>e/t?n.width/e:n.height/t);return[e*(n.aspect>e/t?n.width/e:n.height/t)*r,o*r,1]},exports.useBVH=function(e,t){t={strategy:w.SAH,verbose:!1,setBoundingBox:!0,maxDepth:40,maxLeafTris:10,indirect:!1,...t},C.useEffect((()=>{if(e.current){e.current.raycast=w.acceleratedRaycast;const r=e.current.geometry;return r.computeBoundsTree=w.computeBoundsTree,r.disposeBoundsTree=w.disposeBoundsTree,r.computeBoundsTree(t),()=>{r.boundsTree&&r.disposeBoundsTree()}}}),[e,JSON.stringify(t)])},exports.useBounds=Un,exports.useBoxProjectedEnv=function(e=new R.Vector3,t=new R.Vector3){const[r]=C.useState((()=>({position:new R.Vector3,size:new R.Vector3})));a.applyProps(r,{position:e,size:t});const n=C.useRef(null),o=C.useMemo((()=>({ref:n,onBeforeCompile:e=>function(e,t,r){e.defines.BOX_PROJECTED_ENV_MAP=!0,e.uniforms.envMapPosition={value:t},e.uniforms.envMapSize={value:r},e.vertexShader=`\n  varying vec3 vWorldPosition;\n  ${e.vertexShader.replace("#include <worldpos_vertex>","\n#if defined( USE_ENVMAP ) || defined( DISTANCE ) || defined ( USE_SHADOWMAP )\n  vec4 worldPosition = modelMatrix * vec4( transformed, 1.0 );\n  #ifdef BOX_PROJECTED_ENV_MAP\n    vWorldPosition = worldPosition.xyz;\n  #endif\n#endif\n")}`,e.fragmentShader=`\n    \n#ifdef BOX_PROJECTED_ENV_MAP\n  uniform vec3 envMapSize;\n  uniform vec3 envMapPosition;\n  varying vec3 vWorldPosition;\n    \n  vec3 parallaxCorrectNormal( vec3 v, vec3 cubeSize, vec3 cubePos ) {\n    vec3 nDir = normalize( v );\n    vec3 rbmax = ( .5 * cubeSize + cubePos - vWorldPosition ) / nDir;\n    vec3 rbmin = ( -.5 * cubeSize + cubePos - vWorldPosition ) / nDir;\n    vec3 rbminmax;\n    rbminmax.x = ( nDir.x > 0. ) ? rbmax.x : rbmin.x;\n    rbminmax.y = ( nDir.y > 0. ) ? rbmax.y : rbmin.y;\n    rbminmax.z = ( nDir.z > 0. ) ? rbmax.z : rbmin.z;\n    float correction = min( min( rbminmax.x, rbminmax.y ), rbminmax.z );\n    vec3 boxIntersection = vWorldPosition + nDir * correction;    \n    return boxIntersection - cubePos;\n  }\n#endif\n\n    ${e.fragmentShader.replace("#include <envmap_physical_pars_fragment>",R.ShaderChunk.envmap_physical_pars_fragment).replace("vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );","vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n         \n#ifdef BOX_PROJECTED_ENV_MAP\n  worldNormal = parallaxCorrectNormal( worldNormal, envMapSize, envMapPosition );\n#endif\n\n         ").replace("reflectVec = inverseTransformDirection( reflectVec, viewMatrix );","reflectVec = inverseTransformDirection( reflectVec, viewMatrix );\n         \n#ifdef BOX_PROJECTED_ENV_MAP\n  reflectVec = parallaxCorrectNormal( reflectVec, envMapSize, envMapPosition );\n#endif\n\n        ")}`}(e,r.position,r.size),customProgramCacheKey:()=>JSON.stringify(r.position.toArray())+JSON.stringify(r.size.toArray())})),[...r.position.toArray(),...r.size.toArray()]);return C.useLayoutEffect((()=>{n.current.needsUpdate=!0}),[r]),o},exports.useCamera=function(e,t){const r=a.useThree((e=>e.pointer)),[o]=C.useState((()=>{const o=new n.Raycaster;return t&&a.applyProps(o,t,{}),function(t,a){o.setFromCamera(r,e instanceof n.Camera?e:e.current);const i=this.constructor.prototype.raycast.bind(this);i&&i(o,a)}}));return o},exports.useContextBridge=function(...e){const t=C.useRef([]);return t.current=e.map((e=>C.useContext(e))),C.useMemo((()=>({children:r})=>e.reduceRight(((e,r,n)=>C.createElement(r.Provider,{value:t.current[n],children:e})),r)),[])},exports.useCubeCamera=lt,exports.useCubeTexture=Kt,exports.useCursor=function(e,t="pointer",r="auto",n=document.body){C.useEffect((()=>{if(e)return n.style.cursor=t,()=>{n.style.cursor=r}}),[e])},exports.useDepthBuffer=function({size:e=256,frames:t=1/0}={}){const r=a.useThree((e=>e.viewport.dpr)),{width:o,height:i}=a.useThree((e=>e.size)),s=e||o*r,l=e||i*r,c=C.useMemo((()=>{const e=new n.DepthTexture(s,l);return e.format=n.DepthFormat,e.type=n.UnsignedShortType,{depthTexture:e}}),[s,l]);let u=0;const d=ot(s,l,c);return a.useFrame((e=>{(t===1/0||u<t)&&(e.gl.setRenderTarget(d),e.gl.render(e.scene,e.camera),e.gl.setRenderTarget(null),u++)})),d.depthTexture},exports.useDetectGPU=fr,exports.useEnvironment=$n,exports.useFBO=ot,exports.useFBX=Jt,exports.useFaceControls=()=>t.useContext(ns),exports.useFaceLandmarker=es,exports.useFont=ze,exports.useGLTF=Je,exports.useGizmoContext=Tt,exports.useHelper=ur,exports.useIntersect=xr,exports.useKTX2=tr,exports.useKeyboardControls=function(e){const[t,r,n]=C.useContext(te);return e?n(e):[t,r]},exports.useMask=function(e,t=!1){return{stencilWrite:!0,stencilRef:e,stencilFunc:t?R.NotEqualStencilFunc:R.EqualStencilFunc,stencilFail:R.KeepStencilOp,stencilZFail:R.KeepStencilOp,stencilZPass:R.KeepStencilOp}},exports.useMatcapTexture=Za,exports.useMotion=wt,exports.useNormalTexture=Ya,exports.usePerformanceMonitor=function({onIncline:e,onDecline:r,onChange:n,onFallback:a}){const o=t.useContext(Do),i=t.useRef({onIncline:e,onDecline:r,onChange:n,onFallback:a});t.useLayoutEffect((()=>{i.current.onIncline=e,i.current.onDecline=r,i.current.onChange=n,i.current.onFallback=a}),[e,r,n,a]),t.useLayoutEffect((()=>o.subscribe(i)),[o])},exports.useProgress=q,exports.useScroll=Q,exports.useSelect=function(){return C.useContext(re)},exports.useSpriteAnimator=function(){return C.useContext(Nr)},exports.useSpriteLoader=cr,exports.useSurfaceSampler=Ne,exports.useTexture=Pe,exports.useTrail=Ue,exports.useTrailTexture=zr,exports.useVideoTexture=ar;
