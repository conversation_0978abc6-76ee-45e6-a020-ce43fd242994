'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Rocket, Star, Gift, Crown, 
  Users, Clock, Award, Zap,
  Mail, ArrowRight, CheckCircle,
  Calendar, TrendingUp
} from 'lucide-react';

export default function EarlyAdopterSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [countdown, setCountdown] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const sectionRef = useRef(null);

  const benefits = [
    {
      icon: Crown,
      title: "Lifetime Pro Access",
      description: "Get permanent access to Professional features worth $348/year",
      value: "$348/year",
      color: "text-yellow-400"
    },
    {
      icon: Star,
      title: "Exclusive Beta Features",
      description: "First access to new features before public release",
      value: "Priceless",
      color: "text-purple-400"
    },
    {
      icon: Award,
      title: "Founder's Badge",
      description: "Special recognition as an early supporter of ReportU",
      value: "Exclusive",
      color: "text-blue-400"
    },
    {
      icon: Users,
      title: "Direct Feedback Channel",
      description: "Shape the future of ReportU with direct input to our team",
      value: "Influence",
      color: "text-green-400"
    },
    {
      icon: Gift,
      title: "Exclusive Merchandise",
      description: "Limited edition ReportU swag and branded items",
      value: "$50+",
      color: "text-pink-400"
    },
    {
      icon: Zap,
      title: "Priority Support",
      description: "24/7 dedicated support with <1 hour response time",
      value: "Premium",
      color: "text-cyan-400"
    }
  ];

  const stats = [
    { number: "1,247", label: "Early Adopters", icon: Users },
    { number: "72", label: "Hours Left", icon: Clock },
    { number: "500", label: "Spots Remaining", icon: Star },
    { number: "98%", label: "Satisfaction Rate", icon: TrendingUp }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    // Countdown timer (72 hours from now)
    const targetDate = new Date();
    targetDate.setHours(targetDate.getHours() + 72);

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = targetDate.getTime() - now;

      if (distance > 0) {
        setCountdown({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (email) {
      // Simulate form submission
      setIsSubmitted(true);
      // Store in localStorage for demo purposes
      localStorage.setItem('earlyAdopterEmail', email);
      localStorage.setItem('earlyAdopterDate', new Date().toISOString());
    }
  };

  return (
    <section ref={sectionRef} className="py-20 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div 
            className={`inline-flex items-center gap-2 glass px-4 py-2 rounded-full mb-6 transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <Rocket className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-gray-300">Limited Time Offer</span>
          </div>
          
          <h2 
            className={`text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 transition-all duration-1000 delay-200 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <span className="text-white">Join the </span>
            <span 
              className="text-neon"
              style={{
                background: 'linear-gradient(45deg, #00d4ff, #8b5cf6, #10b981)',
                backgroundSize: '200% 200%',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                color: 'transparent',
                animation: 'gradient-shift 3s ease infinite'
              }}
            >
              Revolution
            </span>
          </h2>
          
          <p 
            className={`text-xl text-gray-300 max-w-3xl mx-auto mb-8 transition-all duration-1000 delay-400 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            Be among the first 2,000 early adopters and get exclusive lifetime benefits 
            worth over $500. Limited spots available!
          </p>

          {/* Countdown Timer */}
          <div 
            className={`flex justify-center gap-4 mb-8 transition-all duration-1000 delay-600 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            {[
              { value: countdown.days, label: 'Days' },
              { value: countdown.hours, label: 'Hours' },
              { value: countdown.minutes, label: 'Minutes' },
              { value: countdown.seconds, label: 'Seconds' }
            ].map((time, index) => (
              <div key={index} className="glass p-4 rounded-lg text-center min-w-[80px]">
                <div className="text-2xl font-bold text-primary">{time.value.toString().padStart(2, '0')}</div>
                <div className="text-xs text-gray-400 uppercase">{time.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div 
          className={`grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16 transition-all duration-1000 delay-800 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={index} className="glass p-6 rounded-xl text-center hover-glow">
                <Icon className="w-8 h-8 text-primary mx-auto mb-3" />
                <div className="text-2xl font-bold text-white mb-1">{stat.number}</div>
                <div className="text-sm text-gray-400">{stat.label}</div>
              </div>
            );
          })}
        </div>

        {/* Benefits Grid */}
        <div 
          className={`grid lg:grid-cols-2 gap-8 mb-16 transition-all duration-1000 delay-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div>
            <h3 className="text-3xl font-bold text-white mb-8">Exclusive Early Adopter Benefits</h3>
            <div className="space-y-6">
              {benefits.map((benefit, index) => {
                const Icon = benefit.icon;
                return (
                  <div key={index} className="glass p-6 rounded-xl hover-glow">
                    <div className="flex items-start gap-4">
                      <div className="p-3 rounded-lg bg-gray-800">
                        <Icon className={`w-6 h-6 ${benefit.color}`} />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-lg font-semibold text-white">{benefit.title}</h4>
                          <span className={`text-sm font-medium ${benefit.color}`}>
                            {benefit.value}
                          </span>
                        </div>
                        <p className="text-gray-400 text-sm">{benefit.description}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Signup Form */}
          <div className="glass p-8 rounded-2xl">
            {!isSubmitted ? (
              <>
                <div className="text-center mb-8">
                  <Rocket className="w-16 h-16 text-primary mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-white mb-2">Secure Your Spot</h3>
                  <p className="text-gray-300">Join 1,247 early adopters already on board</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Email Address
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-primary focus:outline-none transition-colors duration-300"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <label className="flex items-center gap-3">
                      <input type="checkbox" className="w-4 h-4 text-primary" required />
                      <span className="text-sm text-gray-400">
                        I agree to receive updates about ReportU and early adopter benefits
                      </span>
                    </label>
                    
                    <label className="flex items-center gap-3">
                      <input type="checkbox" className="w-4 h-4 text-primary" />
                      <span className="text-sm text-gray-400">
                        Send me exclusive tips and best practices for effective reporting
                      </span>
                    </label>
                  </div>

                  <button
                    type="submit"
                    className="w-full btn-primary py-4 text-lg font-semibold flex items-center justify-center gap-2"
                  >
                    <Crown className="w-5 h-5" />
                    Claim Early Adopter Benefits
                    <ArrowRight className="w-5 h-5" />
                  </button>

                  <div className="text-center">
                    <p className="text-xs text-gray-500">
                      🔒 Your email is secure. No spam, unsubscribe anytime.
                    </p>
                  </div>
                </form>
              </>
            ) : (
              <div className="text-center">
                <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-white mb-2">Welcome Aboard! 🎉</h3>
                <p className="text-gray-300 mb-6">
                  You're now an official ReportU Early Adopter. Check your email for next steps 
                  and exclusive access details.
                </p>
                <div className="glass p-4 rounded-lg">
                  <p className="text-sm text-gray-400">
                    <Calendar className="w-4 h-4 inline mr-2" />
                    Registered: {new Date().toLocaleDateString()}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Social Proof */}
        <div 
          className={`text-center transition-all duration-1000 delay-1200 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="glass p-6 rounded-xl max-w-2xl mx-auto">
            <div className="flex items-center justify-center gap-4 mb-4">
              <div className="flex -space-x-2">
                {['👩‍💼', '👨‍🏫', '👩‍⚕️', '👨‍🎓', '👮‍♂️'].map((avatar, index) => (
                  <div key={index} className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-lg border-2 border-gray-800">
                    {avatar}
                  </div>
                ))}
              </div>
              <div className="text-left">
                <div className="text-white font-semibold">1,247+ Early Adopters</div>
                <div className="text-sm text-gray-400">From Malaysia & Singapore</div>
              </div>
            </div>
            <p className="text-gray-300 text-sm">
              "The early adopter program exceeded my expectations. The lifetime benefits 
              and direct access to the team made it incredibly valuable." - Sarah C.
            </p>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </section>
  );
}
