"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three");const n=new e.Matrix4,t=new e.Ray,o=new e.Sphere,r=new e.Vector3;exports.meshBounds=function(e,i){const s=this.geometry,p=this.material,a=this.matrixWorld;void 0!==p&&(null===s.boundingSphere&&s.computeBoundingSphere(),o.copy(s.boundingSphere),o.applyMatrix4(a),!1!==e.ray.intersectsSphere(o)&&(n.copy(a).invert(),t.copy(e.ray).applyMatrix4(n),null!==s.boundingBox&&null===t.intersectBox(s.boundingBox,r)||i.push({distance:r.distanceTo(e.ray.origin),point:r.clone(),object:this})))};
