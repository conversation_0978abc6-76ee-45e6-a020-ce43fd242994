import DemoNavigation from '@/components/demo/DemoNavigation';
import DemoLevel1 from '@/components/demo/DemoLevel1';
import DemoLevel2 from '@/components/demo/DemoLevel2';
import DemoLevel3 from '@/components/demo/DemoLevel3';
import DemoLevel4 from '@/components/demo/DemoLevel4';
import ParticleBackground from '@/components/effects/ParticleBackground';

export const metadata = {
  title: "Interactive Demo - ReportU Platform",
  description: "Experience the full ReportU platform with real working simulations. Try our 4-level demo system with live functionality.",
};

export default function DemoPage() {
  return (
    <div className="relative min-h-screen">
      {/* Background Effects */}
      <ParticleBackground />

      {/* Demo Navigation */}
      <DemoNavigation />

      {/* Main Demo Content */}
      <main className="relative z-10">
        {/* Demo Level 1: Basic Report Submission */}
        <DemoLevel1 />

        {/* Demo Level 2: Smart Routing */}
        <DemoLevel2 />

        {/* Demo Level 3: Real-Time Tracking */}
        <DemoLevel3 />

        {/* Demo Level 4: Advanced Features */}
        <DemoLevel4 />
      </main>
    </div>
  );
}
