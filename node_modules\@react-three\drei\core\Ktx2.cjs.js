"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("@react-three/fiber"),t=require("three-stdlib"),s=require("./Texture.cjs.js");function a(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var s=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,s.get?s:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("three");var c=a(e);const n="https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master";function u(a,c=`${n}/basis/`){const u=r.useThree((e=>e.gl)),o=r.useLoader(t.KTX2Loader,s.IsObject(a)?Object.values(a):a,(e=>{e.detectSupport(u),e.setTranscoderPath(c)}));if(e.useEffect((()=>{(Array.isArray(o)?o:[o]).forEach(u.initTexture)}),[u,o]),s.IsObject(a)){const e=Object.keys(a),r={};return e.forEach((t=>Object.assign(r,{[t]:o[e.indexOf(t)]}))),r}return o}u.preload=(e,s=`${n}/basis/`)=>r.useLoader.preload(t.KTX2Loader,e,(e=>{e.setTranscoderPath(s)})),u.clear=e=>r.useLoader.clear(t.KTX2Loader,e);exports.Ktx2=({children:e,input:r,basisPath:t})=>{const s=u(r,t);return c.createElement(c.Fragment,null,null==e?void 0:e(s))},exports.useKTX2=u;
