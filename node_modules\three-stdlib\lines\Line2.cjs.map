{"version": 3, "file": "Line2.cjs", "sources": ["../../src/lines/Line2.js"], "sourcesContent": ["import { LineSegments2 } from '../lines/LineSegments2'\nimport { LineGeometry } from '../lines/LineGeometry'\nimport { LineMaterial } from '../lines/LineMaterial'\n\nclass Line2 extends LineSegments2 {\n  constructor(geometry = new LineGeometry(), material = new LineMaterial({ color: Math.random() * 0xffffff })) {\n    super(geometry, material)\n\n    this.isLine2 = true\n\n    this.type = 'Line2'\n  }\n}\n\nexport { Line2 }\n"], "names": ["LineSegments2", "LineGeometry", "LineMaterial"], "mappings": ";;;;;AAIA,MAAM,cAAcA,cAAAA,cAAc;AAAA,EAChC,YAAY,WAAW,IAAIC,0BAAc,GAAE,WAAW,IAAIC,aAAAA,aAAa,EAAE,OAAO,KAAK,WAAW,SAAU,CAAA,GAAG;AAC3G,UAAM,UAAU,QAAQ;AAExB,SAAK,UAAU;AAEf,SAAK,OAAO;AAAA,EACb;AACH;;"}