/**
 * @typedef {object} Phaser.Types.Core.ImagesConfig
 * @since 3.0.0
 *
 * @property {(string|undefined|null)} [default] - A base64 encoded image file to use as the 'default' texture.
 * @property {(string|undefined|null)} [missing] - A base64 encoded image file to use as the 'missing' texture.
 * @property {(string|undefined|null)} [white] - A base64 encoded image file to use as the 'white' texture.
 */
