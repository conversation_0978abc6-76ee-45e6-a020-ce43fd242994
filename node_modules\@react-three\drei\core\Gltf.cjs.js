"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("react"),t=require("three-stdlib"),o=require("@react-three/fiber"),c=require("./Clone.cjs.js");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function n(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var o=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,o.get?o:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("three");var u=s(e),a=n(r);let d=null,f="https://www.gstatic.com/draco/versioned/decoders/1.5.5/";function i(e=!0,r=!0,o){return c=>{o&&o(c),e&&(d||(d=new t.DRACOLoader),d.setDecoderPath("string"==typeof e?e:f),c.setDRACOLoader(d)),r&&c.setMeshoptDecoder("function"==typeof t.MeshoptDecoder?t.MeshoptDecoder():t.MeshoptDecoder)}}const l=(e,r,c,s)=>o.useLoader(t.GLTFLoader,e,i(r,c,s));l.preload=(e,r,c,s)=>o.useLoader.preload(t.GLTFLoader,e,i(r,c,s)),l.clear=e=>o.useLoader.clear(t.GLTFLoader,e),l.setDecoderPath=e=>{f=e};const p=a.forwardRef((({src:e,useDraco:r,useMeshOpt:t,extendLoader:o,...s},n)=>{const{scene:d}=l(e,r,t,o);return a.createElement(c.Clone,u.default({ref:n},s,{object:d}))}));exports.Gltf=p,exports.useGLTF=l;
