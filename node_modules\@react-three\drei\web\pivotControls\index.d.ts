import * as React from 'react';
import * as THREE from 'three';
import { ForwardRefComponent } from '../../helpers/ts-utils';
import { OnDragStartProps } from './context';
type PivotControlsProps = {
    enabled?: boolean;
    scale?: number;
    lineWidth?: number;
    fixed?: boolean;
    offset?: [number, number, number];
    rotation?: [number, number, number];
    matrix?: THREE.Matrix4;
    anchor?: [number, number, number];
    autoTransform?: boolean;
    activeAxes?: [boolean, boolean, boolean];
    disableAxes?: boolean;
    disableSliders?: boolean;
    disableRotations?: boolean;
    disableScaling?: boolean;
    translationLimits?: [[number, number] | undefined, [number, number] | undefined, [number, number] | undefined];
    rotationLimits?: [[number, number] | undefined, [number, number] | undefined, [number, number] | undefined];
    scaleLimits?: [[number, number] | undefined, [number, number] | undefined, [number, number] | undefined];
    axisColors?: [string | number, string | number, string | number];
    hoveredColor?: string | number;
    annotations?: boolean;
    annotationsClass?: string;
    onDragStart?: (props: OnDragStartProps) => void;
    onDrag?: (l: THREE.Matrix4, deltaL: THREE.Matrix4, w: THREE.Matrix4, deltaW: THREE.Matrix4) => void;
    onDragEnd?: () => void;
    depthTest?: boolean;
    opacity?: number;
    visible?: boolean;
    userData?: {
        [key: string]: any;
    };
    children?: React.ReactNode;
};
export declare const PivotControls: ForwardRefComponent<PivotControlsProps, THREE.Group>;
export {};
