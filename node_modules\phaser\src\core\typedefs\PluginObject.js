/**
 * @typedef {object} Phaser.Types.Core.PluginObject
 * @since 3.8.0
 *
 * @property {?Phaser.Types.Core.PluginObjectItem[]} [global] - Global plugins to install.
 * @property {?Phaser.Types.Core.PluginObjectItem[]} [scene] - Scene plugins to install.
 * @property {string[]} [default] - The default set of scene plugins (names).
 * @property {string[]} [defaultMerge] - Plugins to *add* to the default set of scene plugins.
 */
