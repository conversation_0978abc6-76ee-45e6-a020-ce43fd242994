/**
 * @typedef {object} Phaser.Types.Core.MouseInputConfig
 * @since 3.0.0
 *
 * @property {*} [target=null] - Where the Mouse Manager listens for mouse input events. The default is the game canvas.
 * @property {boolean} [preventDefaultDown=true] - If `true` the DOM `mousedown` event will have `preventDefault` set.
 * @property {boolean} [preventDefaultUp=true] - If `true` the DOM `mouseup` event will have `preventDefault` set.
 * @property {boolean} [preventDefaultMove=true] - If `true` the DOM `mousemove` event will have `preventDefault` set.
 * @property {boolean} [preventDefaultWheel=true] - If `true` the DOM `wheel` event will have `preventDefault` set.
 */
