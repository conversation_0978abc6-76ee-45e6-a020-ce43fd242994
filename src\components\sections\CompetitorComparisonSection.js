'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Check, X, Star, Zap, Shield, Globe, 
  Clock, Users, Smartphone, Award,
  TrendingUp, ArrowRight
} from 'lucide-react';

export default function CompetitorComparisonSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeCompetitor, setActiveCompetitor] = useState(0);
  const sectionRef = useRef(null);

  const competitors = [
    {
      name: "ReportU",
      logo: "🚀",
      tagline: "Next-Gen Reporting",
      price: "Free",
      highlight: true,
      color: "from-blue-500 to-cyan-500"
    },
    {
      name: "GovPortal",
      logo: "🏛️",
      tagline: "Traditional System",
      price: "N/A",
      highlight: false,
      color: "from-gray-500 to-gray-600"
    },
    {
      name: "CityReport",
      logo: "🏙️",
      tagline: "Municipal Only",
      price: "$29/mo",
      highlight: false,
      color: "from-orange-500 to-red-500"
    },
    {
      name: "SafetyNet",
      logo: "🛡️",
      tagline: "Basic Reporting",
      price: "$19/mo",
      highlight: false,
      color: "from-purple-500 to-pink-500"
    }
  ];

  const features = [
    {
      category: "Core Features",
      items: [
        {
          name: "Quick Report Submission",
          reportu: true,
          govportal: false,
          cityreport: true,
          safetynet: false,
          icon: Zap
        },
        {
          name: "AI-Powered Routing",
          reportu: true,
          govportal: false,
          cityreport: false,
          safetynet: false,
          icon: Shield
        },
        {
          name: "Real-Time Tracking",
          reportu: true,
          govportal: false,
          cityreport: true,
          safetynet: false,
          icon: Clock
        },
        {
          name: "Cross-Border Support",
          reportu: true,
          govportal: false,
          cityreport: false,
          safetynet: false,
          icon: Globe
        }
      ]
    },
    {
      category: "User Experience",
      items: [
        {
          name: "Mobile-First Design",
          reportu: true,
          govportal: false,
          cityreport: true,
          safetynet: true,
          icon: Smartphone
        },
        {
          name: "Multimedia Upload",
          reportu: true,
          govportal: false,
          cityreport: true,
          safetynet: false,
          icon: Award
        },
        {
          name: "Anonymous Reporting",
          reportu: true,
          govportal: false,
          cityreport: false,
          safetynet: true,
          icon: Users
        },
        {
          name: "24/7 Support",
          reportu: true,
          govportal: false,
          cityreport: false,
          safetynet: false,
          icon: TrendingUp
        }
      ]
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const getFeatureValue = (feature, competitor) => {
    switch (competitor) {
      case 'reportu': return feature.reportu;
      case 'govportal': return feature.govportal;
      case 'cityreport': return feature.cityreport;
      case 'safetynet': return feature.safetynet;
      default: return false;
    }
  };

  return (
    <section ref={sectionRef} className="py-20 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div 
            className={`inline-flex items-center gap-2 glass px-4 py-2 rounded-full mb-6 transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <TrendingUp className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-gray-300">Competitive Analysis</span>
          </div>
          
          <h2 
            className={`text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 transition-all duration-1000 delay-200 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <span className="text-white">Why Choose </span>
            <span 
              className="text-neon"
              style={{
                background: 'linear-gradient(45deg, #00d4ff, #8b5cf6, #10b981)',
                backgroundSize: '200% 200%',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                color: 'transparent',
                animation: 'gradient-shift 3s ease infinite'
              }}
            >
              ReportU?
            </span>
          </h2>
          
          <p 
            className={`text-xl text-gray-300 max-w-3xl mx-auto transition-all duration-1000 delay-400 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            See how ReportU outperforms traditional reporting systems and competitors 
            with revolutionary features and unmatched user experience.
          </p>
        </div>

        {/* Competitor Cards */}
        <div 
          className={`grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12 transition-all duration-1000 delay-600 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          {competitors.map((competitor, index) => (
            <div
              key={index}
              onClick={() => setActiveCompetitor(index)}
              className={`glass p-6 rounded-xl cursor-pointer transition-all duration-500 hover-glow ${
                activeCompetitor === index 
                  ? 'ring-2 ring-primary/50 scale-105' 
                  : 'hover:scale-102'
              } ${competitor.highlight ? 'bg-primary/5' : ''}`}
            >
              <div className="text-center">
                <div className="text-4xl mb-3">{competitor.logo}</div>
                <h3 className={`text-lg font-bold mb-1 ${
                  competitor.highlight ? 'text-primary' : 'text-white'
                }`}>
                  {competitor.name}
                </h3>
                <p className="text-sm text-gray-400 mb-2">{competitor.tagline}</p>
                <div className={`text-lg font-semibold ${
                  competitor.highlight ? 'text-green-400' : 'text-gray-300'
                }`}>
                  {competitor.price}
                </div>
                {competitor.highlight && (
                  <div className="flex items-center justify-center gap-1 mt-2">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-xs text-yellow-400 font-medium">Recommended</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Comparison Table */}
        <div 
          className={`glass rounded-2xl overflow-hidden transition-all duration-1000 delay-800 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          {features.map((category, categoryIndex) => (
            <div key={categoryIndex} className="border-b border-gray-700 last:border-b-0">
              <div className="bg-gray-800/50 px-6 py-4">
                <h4 className="text-lg font-semibold text-white">{category.category}</h4>
              </div>
              
              {category.items.map((feature, featureIndex) => {
                const Icon = feature.icon;
                return (
                  <div 
                    key={featureIndex}
                    className="grid grid-cols-5 gap-4 px-6 py-4 border-b border-gray-700/50 last:border-b-0 hover:bg-gray-800/30 transition-colors duration-300"
                  >
                    <div className="flex items-center gap-3">
                      <Icon className="w-5 h-5 text-gray-400" />
                      <span className="text-white font-medium">{feature.name}</span>
                    </div>
                    
                    {['reportu', 'govportal', 'cityreport', 'safetynet'].map((comp, compIndex) => (
                      <div key={compIndex} className="flex justify-center">
                        {getFeatureValue(feature, comp) ? (
                          <Check className={`w-6 h-6 ${
                            comp === 'reportu' ? 'text-primary' : 'text-green-400'
                          }`} />
                        ) : (
                          <X className="w-6 h-6 text-red-400" />
                        )}
                      </div>
                    ))}
                  </div>
                );
              })}
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div 
          className={`text-center mt-12 transition-all duration-1000 delay-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="glass p-8 rounded-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to Experience the Difference?
            </h3>
            <p className="text-gray-300 mb-6">
              Join thousands who chose ReportU for faster, smarter, and more effective reporting.
            </p>
            <button 
              onClick={() => document.querySelector('#demo')?.scrollIntoView({ behavior: 'smooth' })}
              className="btn-primary flex items-center gap-2 mx-auto px-6 py-3"
            >
              <Zap className="w-4 h-4" />
              Try ReportU Now
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </section>
  );
}
