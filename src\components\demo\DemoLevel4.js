'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Crown, Shield, BarChart3, Users, 
  Globe, Eye, Zap, Settings,
  TrendingUp, MapPin, Calendar, Award,
  Download, Share2, Filter, Search,
  ArrowRight, CheckCircle, Star
} from 'lucide-react';

export default function DemoLevel4() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('analytics');
  const [selectedFeature, setSelectedFeature] = useState(null);
  const sectionRef = useRef(null);

  const advancedFeatures = [
    {
      id: 'analytics',
      name: 'Analytics Dashboard',
      icon: BarChart3,
      description: 'Comprehensive reporting insights and trends',
      color: 'text-blue-400',
      demo: 'analytics'
    },
    {
      id: 'anonymous',
      name: 'Anonymous Reporting',
      icon: Eye,
      description: 'Complete privacy protection for sensitive reports',
      color: 'text-purple-400',
      demo: 'privacy'
    },
    {
      id: 'emergency',
      name: 'Emergency Priority',
      icon: Zap,
      description: 'Instant escalation for urgent situations',
      color: 'text-red-400',
      demo: 'emergency'
    },
    {
      id: 'crossborder',
      name: 'Cross-Border Integration',
      icon: Globe,
      description: 'Seamless Malaysia-Singapore coordination',
      color: 'text-green-400',
      demo: 'crossborder'
    },
    {
      id: 'community',
      name: 'Community Features',
      icon: Users,
      description: 'Public safety trends and community engagement',
      color: 'text-orange-400',
      demo: 'community'
    },
    {
      id: 'enterprise',
      name: 'Enterprise Tools',
      icon: Crown,
      description: 'Advanced features for organizations',
      color: 'text-yellow-400',
      demo: 'enterprise'
    }
  ];

  const analyticsData = {
    totalReports: 2847,
    resolvedReports: 2654,
    averageResponseTime: '18 minutes',
    satisfactionRate: '4.9/5',
    trends: [
      { category: 'Traffic Violations', count: 1247, change: '+12%' },
      { category: 'Public Safety', count: 892, change: '+8%' },
      { category: 'Environmental', count: 456, change: '-3%' },
      { category: 'Counterfeit Products', count: 252, change: '+15%' }
    ],
    locations: [
      { area: 'Kuala Lumpur', reports: 1456, status: 'High Activity' },
      { area: 'Johor Bahru', reports: 892, status: 'Moderate' },
      { area: 'Singapore CBD', reports: 499, status: 'Cross-Border' }
    ]
  };

  const communityData = {
    activeUsers: '50,247',
    safetyScore: 8.7,
    recentAlerts: [
      { type: 'Traffic', location: 'KLCC Area', time: '2 hours ago', severity: 'Medium' },
      { type: 'Safety', location: 'Orchard Road', time: '4 hours ago', severity: 'Low' },
      { type: 'Emergency', location: 'Johor Causeway', time: '6 hours ago', severity: 'High' }
    ],
    topContributors: [
      { name: 'Sarah Chen', reports: 47, badge: 'Gold Reporter' },
      { name: 'Ahmad Rahman', reports: 32, badge: 'Safety Champion' },
      { name: 'Li Wei', reports: 28, badge: 'Community Guardian' }
    ]
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Mark demo as completed
          localStorage.setItem('demoProgress', JSON.stringify({ level: 4, completed: true }));
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const renderAnalyticsDashboard = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {[
          { label: 'Total Reports', value: analyticsData.totalReports.toLocaleString(), icon: BarChart3, color: 'text-blue-400' },
          { label: 'Resolved', value: analyticsData.resolvedReports.toLocaleString(), icon: CheckCircle, color: 'text-green-400' },
          { label: 'Avg Response', value: analyticsData.averageResponseTime, icon: Zap, color: 'text-yellow-400' },
          { label: 'Satisfaction', value: analyticsData.satisfactionRate, icon: Star, color: 'text-purple-400' }
        ].map((metric, index) => {
          const Icon = metric.icon;
          return (
            <div key={index} className="glass p-4 rounded-lg text-center">
              <Icon className={`w-6 h-6 ${metric.color} mx-auto mb-2`} />
              <div className="text-xl font-bold text-white">{metric.value}</div>
              <div className="text-xs text-gray-400">{metric.label}</div>
            </div>
          );
        })}
      </div>

      {/* Trends Chart */}
      <div className="glass p-6 rounded-xl">
        <h4 className="text-lg font-semibold text-white mb-4">Report Trends by Category</h4>
        <div className="space-y-3">
          {analyticsData.trends.map((trend, index) => (
            <div key={index} className="flex items-center justify-between">
              <span className="text-gray-300">{trend.category}</span>
              <div className="flex items-center gap-3">
                <div className="w-32 bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full"
                    style={{ width: `${(trend.count / 1500) * 100}%` }}
                  />
                </div>
                <span className="text-white font-medium w-12">{trend.count}</span>
                <span className={`text-xs px-2 py-1 rounded ${
                  trend.change.startsWith('+') ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                }`}>
                  {trend.change}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Location Insights */}
      <div className="glass p-6 rounded-xl">
        <h4 className="text-lg font-semibold text-white mb-4">Geographic Distribution</h4>
        <div className="grid gap-3">
          {analyticsData.locations.map((location, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
              <div className="flex items-center gap-3">
                <MapPin className="w-4 h-4 text-primary" />
                <span className="text-white">{location.area}</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-gray-400">{location.reports} reports</span>
                <span className={`text-xs px-2 py-1 rounded ${
                  location.status === 'High Activity' ? 'bg-red-500/20 text-red-400' :
                  location.status === 'Moderate' ? 'bg-yellow-500/20 text-yellow-400' :
                  'bg-blue-500/20 text-blue-400'
                }`}>
                  {location.status}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderPrivacyFeatures = () => (
    <div className="space-y-6">
      <div className="glass p-6 rounded-xl">
        <div className="flex items-center gap-3 mb-4">
          <Shield className="w-6 h-6 text-purple-400" />
          <h4 className="text-lg font-semibold text-white">Anonymous Reporting Options</h4>
        </div>
        
        <div className="grid gap-4">
          {[
            { feature: 'Identity Protection', description: 'Your personal information is encrypted and never shared', status: 'Active' },
            { feature: 'Anonymous Submission', description: 'Submit reports without revealing your identity', status: 'Available' },
            { feature: 'Secure Communication', description: 'End-to-end encrypted messaging with authorities', status: 'Enabled' },
            { feature: 'Data Anonymization', description: 'Personal details automatically removed from reports', status: 'Active' }
          ].map((item, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
              <div>
                <div className="text-white font-medium">{item.feature}</div>
                <div className="text-gray-400 text-sm">{item.description}</div>
              </div>
              <span className="bg-green-500/20 text-green-400 text-xs px-3 py-1 rounded-full">
                {item.status}
              </span>
            </div>
          ))}
        </div>
      </div>

      <div className="glass p-6 rounded-xl">
        <h4 className="text-lg font-semibold text-white mb-4">Privacy Controls</h4>
        <div className="space-y-4">
          {[
            { setting: 'Hide Location Details', enabled: true },
            { setting: 'Anonymous Mode', enabled: true },
            { setting: 'Secure File Upload', enabled: true },
            { setting: 'Auto-Delete Personal Data', enabled: false }
          ].map((setting, index) => (
            <div key={index} className="flex items-center justify-between">
              <span className="text-gray-300">{setting.setting}</span>
              <div className={`w-12 h-6 rounded-full transition-colors duration-300 ${
                setting.enabled ? 'bg-primary' : 'bg-gray-600'
              }`}>
                <div className={`w-5 h-5 bg-white rounded-full mt-0.5 transition-transform duration-300 ${
                  setting.enabled ? 'translate-x-6' : 'translate-x-0.5'
                }`} />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderEmergencyFeatures = () => (
    <div className="space-y-6">
      <div className="glass p-6 rounded-xl border-2 border-red-500/30">
        <div className="flex items-center gap-3 mb-4">
          <Zap className="w-6 h-6 text-red-400" />
          <h4 className="text-lg font-semibold text-white">Emergency Priority System</h4>
        </div>
        
        <div className="space-y-4">
          <div className="p-4 bg-red-500/10 rounded-lg border border-red-500/20">
            <div className="text-red-400 font-semibold mb-2">EMERGENCY ALERT ACTIVE</div>
            <div className="text-white">Report escalated to highest priority</div>
            <div className="text-gray-400 text-sm">Response time: &lt; 5 minutes</div>
          </div>
          
          <div className="grid gap-3">
            {[
              { action: 'Immediate Notification', status: 'Sent to all relevant departments', time: '< 30 seconds' },
              { action: 'Priority Routing', status: 'Bypassed standard queue', time: '< 1 minute' },
              { action: 'Officer Assignment', status: 'Senior officer automatically assigned', time: '< 2 minutes' },
              { action: 'Real-time Tracking', status: 'Live GPS tracking enabled', time: '< 3 minutes' }
            ].map((action, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                <div>
                  <div className="text-white font-medium">{action.action}</div>
                  <div className="text-gray-400 text-sm">{action.status}</div>
                </div>
                <span className="text-red-400 text-sm font-medium">{action.time}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderCommunityFeatures = () => (
    <div className="space-y-6">
      {/* Community Stats */}
      <div className="grid grid-cols-2 gap-4">
        <div className="glass p-4 rounded-lg text-center">
          <Users className="w-6 h-6 text-orange-400 mx-auto mb-2" />
          <div className="text-xl font-bold text-white">{communityData.activeUsers}</div>
          <div className="text-xs text-gray-400">Active Users</div>
        </div>
        <div className="glass p-4 rounded-lg text-center">
          <Shield className="w-6 h-6 text-green-400 mx-auto mb-2" />
          <div className="text-xl font-bold text-white">{communityData.safetyScore}/10</div>
          <div className="text-xs text-gray-400">Safety Score</div>
        </div>
      </div>

      {/* Recent Community Alerts */}
      <div className="glass p-6 rounded-xl">
        <h4 className="text-lg font-semibold text-white mb-4">Community Safety Alerts</h4>
        <div className="space-y-3">
          {communityData.recentAlerts.map((alert, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${
                  alert.severity === 'High' ? 'bg-red-400' :
                  alert.severity === 'Medium' ? 'bg-yellow-400' : 'bg-green-400'
                }`} />
                <div>
                  <div className="text-white font-medium">{alert.type} Alert</div>
                  <div className="text-gray-400 text-sm">{alert.location}</div>
                </div>
              </div>
              <span className="text-gray-500 text-sm">{alert.time}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Top Contributors */}
      <div className="glass p-6 rounded-xl">
        <h4 className="text-lg font-semibold text-white mb-4">Community Champions</h4>
        <div className="space-y-3">
          {communityData.topContributors.map((contributor, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {index + 1}
                </div>
                <div>
                  <div className="text-white font-medium">{contributor.name}</div>
                  <div className="text-orange-400 text-sm">{contributor.badge}</div>
                </div>
              </div>
              <span className="text-gray-400">{contributor.reports} reports</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <section id="demo-level-4" ref={sectionRef} className="min-h-screen py-20 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div
            className={`inline-flex items-center gap-2 glass px-4 py-2 rounded-full mb-6 transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <Crown className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-gray-300">Demo Level 4</span>
          </div>

          <h2
            className={`text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 transition-all duration-1000 delay-200 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <span className="text-white">Advanced </span>
            <span
              className="text-neon"
              style={{
                background: 'linear-gradient(45deg, #00d4ff, #8b5cf6, #10b981)',
                backgroundSize: '200% 200%',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                color: 'transparent',
                animation: 'gradient-shift 3s ease infinite'
              }}
            >
              Features
            </span>
          </h2>

          <p
            className={`text-xl text-gray-300 max-w-3xl mx-auto transition-all duration-1000 delay-400 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            Explore the full power of ReportU with advanced analytics, privacy controls,
            emergency systems, and community features designed for the future of civic engagement.
          </p>
        </div>

        {/* Feature Navigation */}
        <div
          className={`flex flex-wrap justify-center gap-4 mb-12 transition-all duration-1000 delay-600 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          {advancedFeatures.map((feature) => {
            const Icon = feature.icon;
            return (
              <button
                key={feature.id}
                onClick={() => setActiveTab(feature.id)}
                className={`flex items-center gap-3 px-6 py-3 rounded-lg transition-all duration-300 ${
                  activeTab === feature.id
                    ? 'bg-primary text-white'
                    : 'glass text-gray-400 hover:text-white hover-glow'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{feature.name}</span>
              </button>
            );
          })}
        </div>

        {/* Feature Content */}
        <div
          className={`transition-all duration-1000 delay-800 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="glass p-8 rounded-2xl">
            {activeTab === 'analytics' && renderAnalyticsDashboard()}
            {activeTab === 'anonymous' && renderPrivacyFeatures()}
            {activeTab === 'emergency' && renderEmergencyFeatures()}
            {activeTab === 'community' && renderCommunityFeatures()}

            {(activeTab === 'crossborder' || activeTab === 'enterprise') && (
              <div className="text-center py-12">
                <Crown className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-white mb-4">
                  {activeTab === 'crossborder' ? 'Cross-Border Integration' : 'Enterprise Features'}
                </h3>
                <p className="text-gray-300 mb-6">
                  {activeTab === 'crossborder'
                    ? 'Advanced cross-border coordination between Malaysia and Singapore authorities with real-time data sharing and unified case management.'
                    : 'Comprehensive enterprise tools including custom workflows, API access, white-label solutions, and advanced compliance features.'
                  }
                </p>
                <div className="inline-flex items-center gap-2 bg-yellow-500/20 text-yellow-400 px-4 py-2 rounded-full">
                  <Award className="w-4 h-4" />
                  <span className="text-sm font-medium">Premium Feature</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Demo Completion */}
        <div
          className={`text-center mt-16 transition-all duration-1000 delay-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="glass p-12 rounded-2xl max-w-2xl mx-auto">
            <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-6" />
            <h3 className="text-3xl font-bold text-white mb-4">Demo Complete! 🎉</h3>
            <p className="text-gray-300 mb-8">
              You've experienced all 4 levels of the ReportU platform. From basic reporting
              to advanced features, you've seen how we're revolutionizing civic engagement.
            </p>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              {[
                { label: 'Report Submitted', icon: CheckCircle },
                { label: 'AI Routing', icon: CheckCircle },
                { label: 'Real-Time Tracking', icon: CheckCircle },
                { label: 'Advanced Features', icon: CheckCircle }
              ].map((step, index) => {
                const Icon = step.icon;
                return (
                  <div key={index} className="text-center">
                    <Icon className="w-8 h-8 text-green-400 mx-auto mb-2" />
                    <div className="text-sm text-gray-300">{step.label}</div>
                  </div>
                );
              })}
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => window.location.href = '/'}
                className="btn-primary px-6 py-3 flex items-center gap-2"
              >
                <ArrowRight className="w-4 h-4" />
                Return to Homepage
              </button>
              <button
                onClick={() => window.location.reload()}
                className="glass px-6 py-3 text-white hover:text-primary transition-colors duration-300"
              >
                Restart Demo
              </button>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </section>
  );
}
