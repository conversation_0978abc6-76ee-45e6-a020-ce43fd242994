import Navigation from '@/components/ui/Navigation';
import ParticleBackground from '@/components/effects/ParticleBackground';
import HeroSection from '@/components/sections/HeroSection';
import ProblemSolutionSection from '@/components/sections/ProblemSolutionSection';
import FeaturesPreviewSection from '@/components/sections/FeaturesPreviewSection';
import CompetitorComparisonSection from '@/components/sections/CompetitorComparisonSection';
import TestimonialsSection from '@/components/sections/TestimonialsSection';
import PricingSection from '@/components/sections/PricingSection';
import TrustBuildingSection from '@/components/sections/TrustBuildingSection';
import EarlyAdopterSection from '@/components/sections/EarlyAdopterSection';

export default function Home() {
  return (
    <div className="relative min-h-screen">
      {/* Background Effects */}
      <ParticleBackground />

      {/* Navigation */}
      <Navigation />

      {/* Main Content */}
      <main className="relative z-10">
        {/* Hero Section */}
        <HeroSection />

        {/* Problem/Solution Section */}
        <ProblemSolutionSection />

        {/* Features Preview Section */}
        <FeaturesPreviewSection />

        {/* Competitor Comparison Section */}
        <CompetitorComparisonSection />

        {/* Testimonials Section */}
        <TestimonialsSection />

        {/* Pricing Section */}
        <PricingSection />

        {/* Trust Building Section */}
        <TrustBuildingSection />

        {/* Early Adopter Section */}
        <EarlyAdopterSection />

        {/* Demo Section */}
        <div id="demo" className="min-h-screen flex items-center justify-center">
          <div className="glass p-12 rounded-2xl text-center max-w-2xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-6">Interactive Demo</h2>
            <p className="text-gray-300 text-lg mb-6">
              Experience the full ReportU platform with real working simulations.
              Try our 4-level demo system with live functionality.
            </p>
            <button className="btn-primary px-6 py-3">
              Launch Demo Platform
            </button>
          </div>
        </div>

        {/* Features Section */}
        <div id="features" className="min-h-screen flex items-center justify-center">
          <div className="glass p-12 rounded-2xl text-center max-w-2xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-6">Advanced Features</h2>
            <p className="text-gray-300 text-lg">
              Comprehensive feature showcase with interactive elements and animations.
              Competitor comparison, testimonials, and detailed specifications.
            </p>
          </div>
        </div>

        {/* About Section */}
        <div id="about" className="min-h-screen flex items-center justify-center">
          <div className="glass p-12 rounded-2xl text-center max-w-2xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-6">About ReportU</h2>
            <p className="text-gray-300 text-lg">
              Learn more about ReportU's mission, team, and vision for the future
              of cross-border civic engagement.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
