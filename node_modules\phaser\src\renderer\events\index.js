/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * @namespace Phaser.Renderer.Events
 */

module.exports = {

    LOSE_WEBGL: require('./LOSE_WEBGL_EVENT'),
    POST_RENDER: require('./POST_RENDER_EVENT'),
    PRE_RENDER: require('./PRE_RENDER_EVENT'),
    RENDER: require('./RENDER_EVENT'),
    RESIZE: require('./RESIZE_EVENT'),
    RESTORE_WEBGL: require('./RESTORE_WEBGL_EVENT')

};
