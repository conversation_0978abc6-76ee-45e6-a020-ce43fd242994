'use client';

import { useState, useEffect } from 'react';
import { ArrowLeft, Home, Play, Pause, RotateCcw } from 'lucide-react';
import Logo from '@/components/ui/Logo';
import Link from 'next/link';

export default function DemoNavigation() {
  const [scrolled, setScrolled] = useState(false);
  const [currentLevel, setCurrentLevel] = useState(1);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
      
      // Detect current demo level based on scroll position
      const sections = ['demo-level-1', 'demo-level-2', 'demo-level-3', 'demo-level-4'];
      const currentSection = sections.find(id => {
        const element = document.getElementById(id);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom >= 100;
        }
        return false;
      });
      
      if (currentSection) {
        const level = parseInt(currentSection.split('-')[2]);
        setCurrentLevel(level);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const demoLevels = [
    { id: 1, name: 'Basic Report', description: 'Submit your first report' },
    { id: 2, name: 'Smart Routing', description: 'AI-powered department detection' },
    { id: 3, name: 'Real-Time Tracking', description: 'Live status monitoring' },
    { id: 4, name: 'Advanced Features', description: 'Full platform capabilities' }
  ];

  const scrollToLevel = (level) => {
    const element = document.getElementById(`demo-level-${level}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const toggleAutoPlay = () => {
    setIsPlaying(!isPlaying);
    // Auto-play functionality would be implemented here
  };

  const resetDemo = () => {
    // Reset all demo states
    localStorage.removeItem('demoProgress');
    localStorage.removeItem('reportData');
    localStorage.removeItem('trackingData');
    window.location.reload();
  };

  return (
    <>
      <nav 
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          scrolled 
            ? 'glass backdrop-blur-xl border-b border-white/10' 
            : 'bg-transparent'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo & Back Button */}
            <div className="flex items-center gap-4">
              <Link 
                href="/"
                className="glass p-2 rounded-lg text-gray-400 hover:text-white transition-colors duration-300 hover-glow"
              >
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <Logo size="default" />
              <div className="hidden lg:block">
                <span className="text-gray-400 text-sm">Interactive Demo Platform</span>
              </div>
            </div>

            {/* Demo Level Indicators */}
            <div className="hidden lg:flex items-center gap-2">
              {demoLevels.map((level) => (
                <button
                  key={level.id}
                  onClick={() => scrollToLevel(level.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                    currentLevel === level.id
                      ? 'bg-primary text-white'
                      : 'glass text-gray-400 hover:text-white hover-glow'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${
                      currentLevel === level.id ? 'bg-white' : 'bg-gray-500'
                    }`} />
                    <span className="hidden xl:inline">{level.name}</span>
                    <span className="xl:hidden">L{level.id}</span>
                  </div>
                </button>
              ))}
            </div>

            {/* Demo Controls */}
            <div className="flex items-center gap-3">
              <button
                onClick={toggleAutoPlay}
                className={`glass p-2 rounded-lg transition-all duration-300 hover-glow ${
                  isPlaying ? 'text-primary' : 'text-gray-400 hover:text-white'
                }`}
              >
                {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
              </button>
              
              <button
                onClick={resetDemo}
                className="glass p-2 rounded-lg text-gray-400 hover:text-white transition-colors duration-300 hover-glow"
              >
                <RotateCcw className="w-5 h-5" />
              </button>

              <Link 
                href="/"
                className="btn-primary flex items-center gap-2 px-4 py-2"
              >
                <Home className="w-4 h-4" />
                <span className="hidden sm:inline">Home</span>
              </Link>
            </div>
          </div>
        </div>

        {/* Mobile Demo Level Indicators */}
        <div className="lg:hidden border-t border-white/10">
          <div className="flex overflow-x-auto px-4 py-2 gap-2">
            {demoLevels.map((level) => (
              <button
                key={level.id}
                onClick={() => scrollToLevel(level.id)}
                className={`flex-shrink-0 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 ${
                  currentLevel === level.id
                    ? 'bg-primary text-white'
                    : 'glass text-gray-400'
                }`}
              >
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    currentLevel === level.id ? 'bg-white' : 'bg-gray-500'
                  }`} />
                  <span>L{level.id}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Progress Bar */}
      <div className="fixed top-16 lg:top-20 left-0 right-0 z-40">
        <div className="h-1 bg-gray-800">
          <div 
            className="h-full bg-gradient-to-r from-primary to-secondary transition-all duration-500"
            style={{ width: `${(currentLevel / demoLevels.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Spacer for fixed navigation */}
      <div className="h-16 lg:h-20"></div>
    </>
  );
}
