"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("./Texture.cjs.js"),r=require("suspend-react");function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("three"),require("@react-three/fiber");var u=n(e);function a(e=0,n=1024,a){const c=r.suspend((()=>fetch("https://cdn.jsdelivr.net/gh/pmndrs/drei-assets@master/matcaps.json").then((e=>e.json()))),["matcapList"]),s=c[0],o=u.useMemo((()=>Object.keys(c).length),[]),i=`${u.useMemo((()=>"string"==typeof e?e:"number"==typeof e?c[e]:null),[e])||s}${function(e){switch(e){case 64:return"-64px";case 128:return"-128px";case 256:return"-256px";case 512:return"-512px";default:return""}}(n)}.png`,p=`https://rawcdn.githack.com/emmelleppi/matcaps/9b36ccaaf0a24881a39062d05566c9e92be4aa0d/${n}/${i}`;return[t.useTexture(p,a),p,o]}exports.MatcapTexture=({children:e,id:t,format:r,onLoad:n})=>{const c=a(t,r,n);return u.createElement(u.Fragment,null,null==e?void 0:e(c))},exports.useMatcapTexture=a;
