'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Check, Star, Zap, Shield, Globe, 
  Users, Crown, Rocket, ArrowRight,
  Clock, Award, TrendingUp
} from 'lucide-react';

export default function PricingSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [billingCycle, setBillingCycle] = useState('monthly');
  const sectionRef = useRef(null);

  const plans = [
    {
      name: "Community",
      icon: Users,
      price: { monthly: 0, yearly: 0 },
      originalPrice: null,
      description: "Perfect for individual citizens and basic reporting needs",
      badge: "Most Popular",
      badgeColor: "bg-green-500",
      highlight: false,
      color: "from-green-500 to-emerald-500",
      features: [
        "Up to 10 reports per month",
        "Basic AI routing",
        "Standard support",
        "Mobile app access",
        "Real-time tracking",
        "Anonymous reporting",
        "Photo upload (5MB max)"
      ],
      limitations: [
        "No video uploads",
        "Basic analytics only",
        "Standard priority"
      ]
    },
    {
      name: "Professional",
      icon: Shield,
      price: { monthly: 29, yearly: 290 },
      originalPrice: { monthly: 39, yearly: 390 },
      description: "Ideal for businesses, organizations, and power users",
      badge: "Best Value",
      badgeColor: "bg-primary",
      highlight: true,
      color: "from-blue-500 to-cyan-500",
      features: [
        "Unlimited reports",
        "Advanced AI routing",
        "Priority support (24/7)",
        "Web + Mobile access",
        "Advanced analytics",
        "Multimedia uploads (100MB)",
        "Custom report templates",
        "Team collaboration",
        "API access",
        "White-label options"
      ],
      limitations: []
    },
    {
      name: "Enterprise",
      icon: Crown,
      price: { monthly: 99, yearly: 990 },
      originalPrice: { monthly: 129, yearly: 1290 },
      description: "For large organizations and government agencies",
      badge: "Premium",
      badgeColor: "bg-purple-500",
      highlight: false,
      color: "from-purple-500 to-pink-500",
      features: [
        "Everything in Professional",
        "Dedicated account manager",
        "Custom integrations",
        "Advanced security features",
        "Compliance reporting",
        "Multi-tenant support",
        "Custom workflows",
        "SLA guarantees",
        "On-premise deployment",
        "Training & onboarding"
      ],
      limitations: []
    }
  ];

  const savings = {
    Professional: Math.round(((plans[1].originalPrice.yearly - plans[1].price.yearly) / plans[1].originalPrice.yearly) * 100),
    Enterprise: Math.round(((plans[2].originalPrice.yearly - plans[2].price.yearly) / plans[2].originalPrice.yearly) * 100)
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const getPrice = (plan) => {
    return billingCycle === 'yearly' ? plan.price.yearly : plan.price.monthly;
  };

  const getOriginalPrice = (plan) => {
    if (!plan.originalPrice) return null;
    return billingCycle === 'yearly' ? plan.originalPrice.yearly : plan.originalPrice.monthly;
  };

  return (
    <section ref={sectionRef} className="py-20 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div 
            className={`inline-flex items-center gap-2 glass px-4 py-2 rounded-full mb-6 transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <TrendingUp className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-gray-300">Transparent Pricing</span>
          </div>
          
          <h2 
            className={`text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 transition-all duration-1000 delay-200 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <span className="text-white">Choose Your </span>
            <span 
              className="text-neon"
              style={{
                background: 'linear-gradient(45deg, #00d4ff, #8b5cf6, #10b981)',
                backgroundSize: '200% 200%',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                color: 'transparent',
                animation: 'gradient-shift 3s ease infinite'
              }}
            >
              Plan
            </span>
          </h2>
          
          <p 
            className={`text-xl text-gray-300 max-w-3xl mx-auto mb-8 transition-all duration-1000 delay-400 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            Start free and scale as you grow. All plans include our core features 
            with no hidden fees or setup costs.
          </p>

          {/* Billing Toggle */}
          <div 
            className={`flex items-center justify-center gap-4 transition-all duration-1000 delay-600 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <span className={`text-sm font-medium ${billingCycle === 'monthly' ? 'text-white' : 'text-gray-400'}`}>
              Monthly
            </span>
            <button
              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
              className={`relative w-14 h-7 rounded-full transition-colors duration-300 ${
                billingCycle === 'yearly' ? 'bg-primary' : 'bg-gray-600'
              }`}
            >
              <div 
                className={`absolute top-1 w-5 h-5 bg-white rounded-full transition-transform duration-300 ${
                  billingCycle === 'yearly' ? 'translate-x-8' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm font-medium ${billingCycle === 'yearly' ? 'text-white' : 'text-gray-400'}`}>
              Yearly
            </span>
            {billingCycle === 'yearly' && (
              <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                Save up to 25%
              </span>
            )}
          </div>
        </div>

        {/* Pricing Cards */}
        <div 
          className={`grid lg:grid-cols-3 gap-8 transition-all duration-1000 delay-800 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          {plans.map((plan, index) => {
            const Icon = plan.icon;
            const price = getPrice(plan);
            const originalPrice = getOriginalPrice(plan);
            
            return (
              <div
                key={index}
                className={`glass rounded-2xl p-8 relative transition-all duration-500 hover-glow ${
                  plan.highlight 
                    ? 'ring-2 ring-primary/50 scale-105 bg-primary/5' 
                    : 'hover:scale-102'
                }`}
              >
                {/* Badge */}
                {plan.badge && (
                  <div className={`absolute -top-4 left-1/2 -translate-x-1/2 ${plan.badgeColor} text-white px-4 py-1 rounded-full text-sm font-medium`}>
                    {plan.badge}
                  </div>
                )}

                {/* Header */}
                <div className="text-center mb-8">
                  <div 
                    className={`inline-flex p-4 rounded-2xl bg-gradient-to-r ${plan.color} mb-4`}
                  >
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  
                  <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                  <p className="text-gray-400 text-sm mb-4">{plan.description}</p>
                  
                  <div className="flex items-baseline justify-center gap-2">
                    {originalPrice && (
                      <span className="text-gray-500 line-through text-lg">
                        ${originalPrice}
                      </span>
                    )}
                    <span className="text-4xl font-bold text-white">
                      ${price}
                    </span>
                    <span className="text-gray-400">
                      /{billingCycle === 'yearly' ? 'year' : 'month'}
                    </span>
                  </div>
                  
                  {billingCycle === 'yearly' && originalPrice && (
                    <div className="text-green-400 text-sm font-medium mt-2">
                      Save {savings[plan.name]}% annually
                    </div>
                  )}
                </div>

                {/* Features */}
                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-green-400 flex-shrink-0" />
                      <span className="text-gray-300 text-sm">{feature}</span>
                    </div>
                  ))}
                  
                  {plan.limitations.map((limitation, limitIndex) => (
                    <div key={limitIndex} className="flex items-center gap-3 opacity-60">
                      <div className="w-5 h-5 flex-shrink-0 flex items-center justify-center">
                        <div className="w-2 h-2 bg-gray-500 rounded-full" />
                      </div>
                      <span className="text-gray-500 text-sm">{limitation}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Button */}
                <button 
                  className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center gap-2 ${
                    plan.highlight
                      ? 'btn-primary'
                      : 'glass border border-gray-600 text-white hover:border-primary hover:text-primary'
                  }`}
                >
                  {plan.name === 'Community' ? (
                    <>
                      <Rocket className="w-4 h-4" />
                      Get Started Free
                    </>
                  ) : (
                    <>
                      <ArrowRight className="w-4 h-4" />
                      Start {billingCycle === 'yearly' ? 'Annual' : 'Monthly'} Plan
                    </>
                  )}
                </button>
              </div>
            );
          })}
        </div>

        {/* Bottom Features */}
        <div 
          className={`mt-16 text-center transition-all duration-1000 delay-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="glass p-8 rounded-2xl">
            <h3 className="text-2xl font-bold text-white mb-6">All Plans Include</h3>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { icon: Shield, text: "99.9% Uptime SLA" },
                { icon: Globe, text: "Cross-Border Support" },
                { icon: Clock, text: "24/7 System Monitoring" },
                { icon: Award, text: "Government Certified" }
              ].map((item, index) => {
                const Icon = item.icon;
                return (
                  <div key={index} className="flex items-center gap-3">
                    <Icon className="w-5 h-5 text-primary" />
                    <span className="text-gray-300 text-sm">{item.text}</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </section>
  );
}
