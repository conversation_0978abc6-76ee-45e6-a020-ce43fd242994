"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),n=require("@react-three/fiber"),i=require("three-stdlib");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function s(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=o(e),c=s(t);const l=c.forwardRef((function({points:e,color:t=16777215,vertexColors:o,linewidth:s,lineWidth:l,segments:u,dashed:f,...d},m){var h,p;const b=n.useThree((e=>e.size)),y=c.useMemo((()=>u?new i.LineSegments2:new i.Line2),[u]),[v]=c.useState((()=>new i.LineMaterial)),g=4===(null==o||null==(h=o[0])?void 0:h.length)?4:3,j=c.useMemo((()=>{const n=u?new i.LineSegmentsGeometry:new i.LineGeometry,s=e.map((e=>{const t=Array.isArray(e);return e instanceof r.Vector3||e instanceof r.Vector4?[e.x,e.y,e.z]:e instanceof r.Vector2?[e.x,e.y,0]:t&&3===e.length?[e[0],e[1],e[2]]:t&&2===e.length?[e[0],e[1],0]:e}));if(n.setPositions(s.flat()),o){t=16777215;const e=o.map((e=>e instanceof r.Color?e.toArray():e));n.setColors(e.flat(),g)}return n}),[e,u,o,g]);return c.useLayoutEffect((()=>{y.computeLineDistances()}),[e,y]),c.useLayoutEffect((()=>{f?v.defines.USE_DASH="":delete v.defines.USE_DASH,v.needsUpdate=!0}),[f,v]),c.useEffect((()=>()=>{j.dispose(),v.dispose()}),[j]),c.createElement("primitive",a.default({object:y,ref:m},d),c.createElement("primitive",{object:j,attach:"geometry"}),c.createElement("primitive",a.default({object:v,attach:"material",color:t,vertexColors:Boolean(o),resolution:[b.width,b.height],linewidth:null!==(p=null!=s?s:l)&&void 0!==p?p:1,dashed:f,transparent:4===g},d)))}));exports.Line=l;
