'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Shield, Lock, Award, Globe, 
  CheckCircle, Star, Users, Zap,
  Eye, Clock, FileCheck, Server
} from 'lucide-react';

export default function TrustBuildingSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const sectionRef = useRef(null);

  const certifications = [
    {
      name: "ISO 27001",
      description: "Information Security Management",
      icon: Shield,
      badge: "🏆",
      status: "Certified",
      color: "text-blue-400"
    },
    {
      name: "SOC 2 Type II",
      description: "Security & Availability Controls",
      icon: Lock,
      badge: "🔒",
      status: "Compliant",
      color: "text-green-400"
    },
    {
      name: "GDPR",
      description: "Data Protection Regulation",
      icon: Eye,
      badge: "🇪🇺",
      status: "Compliant",
      color: "text-purple-400"
    },
    {
      name: "Malaysia PDPA",
      description: "Personal Data Protection Act",
      icon: FileCheck,
      badge: "🇲🇾",
      status: "Certified",
      color: "text-red-400"
    },
    {
      name: "Singapore PDPC",
      description: "Personal Data Protection Commission",
      icon: Globe,
      badge: "🇸🇬",
      status: "Registered",
      color: "text-cyan-400"
    },
    {
      name: "AWS Security",
      description: "Cloud Infrastructure Security",
      icon: Server,
      badge: "☁️",
      status: "Verified",
      color: "text-orange-400"
    }
  ];

  const trustMetrics = [
    {
      category: "Security",
      items: [
        { label: "Data Encryption", value: "AES-256", icon: Lock },
        { label: "Uptime Guarantee", value: "99.9%", icon: Clock },
        { label: "Security Audits", value: "Monthly", icon: Shield },
        { label: "Backup Frequency", value: "Real-time", icon: Server }
      ]
    },
    {
      category: "Compliance",
      items: [
        { label: "Government Partnerships", value: "12+", icon: Award },
        { label: "Legal Compliance", value: "100%", icon: CheckCircle },
        { label: "Data Residency", value: "Local", icon: Globe },
        { label: "Audit Trail", value: "Complete", icon: FileCheck }
      ]
    },
    {
      category: "Performance",
      items: [
        { label: "Response Time", value: "< 200ms", icon: Zap },
        { label: "Success Rate", value: "99.8%", icon: Star },
        { label: "User Satisfaction", value: "4.9/5", icon: Users },
        { label: "Processing Speed", value: "Instant", icon: CheckCircle }
      ]
    }
  ];

  const partnerships = [
    {
      name: "Malaysia Government",
      logo: "🏛️",
      description: "Official integration with government reporting systems",
      status: "Active Partnership"
    },
    {
      name: "Singapore Authorities",
      logo: "🏢",
      description: "Certified reporting channel for Singapore agencies",
      status: "Certified Partner"
    },
    {
      name: "ASEAN Digital Hub",
      logo: "🌏",
      description: "Regional digital transformation initiative",
      status: "Strategic Alliance"
    },
    {
      name: "Cybersecurity Agency",
      logo: "🛡️",
      description: "Security framework validation and monitoring",
      status: "Security Partner"
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="py-20 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div 
            className={`inline-flex items-center gap-2 glass px-4 py-2 rounded-full mb-6 transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <Shield className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-gray-300">Security & Trust</span>
          </div>
          
          <h2 
            className={`text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 transition-all duration-1000 delay-200 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <span className="text-white">Built for </span>
            <span 
              className="text-neon"
              style={{
                background: 'linear-gradient(45deg, #00d4ff, #8b5cf6, #10b981)',
                backgroundSize: '200% 200%',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                color: 'transparent',
                animation: 'gradient-shift 3s ease infinite'
              }}
            >
              Trust
            </span>
          </h2>
          
          <p 
            className={`text-xl text-gray-300 max-w-3xl mx-auto transition-all duration-1000 delay-400 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            Enterprise-grade security, government certifications, and transparent operations 
            ensure your data and reports are always protected.
          </p>
        </div>

        {/* Certifications Grid */}
        <div 
          className={`grid grid-cols-2 lg:grid-cols-3 gap-6 mb-16 transition-all duration-1000 delay-600 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          {certifications.map((cert, index) => {
            const Icon = cert.icon;
            return (
              <div key={index} className="glass p-6 rounded-xl hover-glow transition-all duration-300 hover:scale-105">
                <div className="flex items-center gap-4 mb-4">
                  <div className="text-3xl">{cert.badge}</div>
                  <div>
                    <h3 className="text-lg font-bold text-white">{cert.name}</h3>
                    <p className="text-sm text-gray-400">{cert.description}</p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <Icon className={`w-6 h-6 ${cert.color}`} />
                  <span className={`text-sm font-medium px-3 py-1 rounded-full ${
                    cert.status === 'Certified' ? 'bg-green-500/20 text-green-400' :
                    cert.status === 'Compliant' ? 'bg-blue-500/20 text-blue-400' :
                    cert.status === 'Registered' ? 'bg-purple-500/20 text-purple-400' :
                    'bg-orange-500/20 text-orange-400'
                  }`}>
                    {cert.status}
                  </span>
                </div>
              </div>
            );
          })}
        </div>

        {/* Trust Metrics */}
        <div 
          className={`transition-all duration-1000 delay-800 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          {/* Tab Navigation */}
          <div className="flex justify-center mb-8">
            <div className="glass p-2 rounded-lg">
              {trustMetrics.map((category, index) => (
                <button
                  key={index}
                  onClick={() => setActiveTab(index)}
                  className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                    activeTab === index 
                      ? 'bg-primary text-white' 
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  {category.category}
                </button>
              ))}
            </div>
          </div>

          {/* Metrics Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {trustMetrics[activeTab].items.map((metric, index) => {
              const Icon = metric.icon;
              return (
                <div key={index} className="glass p-6 rounded-xl text-center hover-glow">
                  <Icon className="w-8 h-8 text-primary mx-auto mb-3" />
                  <div className="text-2xl font-bold text-white mb-1">{metric.value}</div>
                  <div className="text-sm text-gray-400">{metric.label}</div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Government Partnerships */}
        <div 
          className={`transition-all duration-1000 delay-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-white mb-4">Government Partnerships</h3>
            <p className="text-gray-300 max-w-2xl mx-auto">
              Official partnerships and certifications with government agencies ensure 
              seamless integration and compliance.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {partnerships.map((partner, index) => (
              <div key={index} className="glass p-6 rounded-xl hover-glow">
                <div className="flex items-center gap-4 mb-4">
                  <div className="text-4xl">{partner.logo}</div>
                  <div>
                    <h4 className="text-lg font-bold text-white">{partner.name}</h4>
                    <p className="text-sm text-gray-400">{partner.description}</p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-primary font-medium">{partner.status}</span>
                  <CheckCircle className="w-5 h-5 text-green-400" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom CTA */}
        <div 
          className={`text-center mt-16 transition-all duration-1000 delay-1200 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="glass p-8 rounded-2xl max-w-2xl mx-auto">
            <Shield className="w-16 h-16 text-primary mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-white mb-4">
              Your Data is Safe with Us
            </h3>
            <p className="text-gray-300 mb-6">
              Enterprise-grade security, government compliance, and transparent operations. 
              Start reporting with confidence today.
            </p>
            <button 
              onClick={() => document.querySelector('#demo')?.scrollIntoView({ behavior: 'smooth' })}
              className="btn-primary flex items-center gap-2 mx-auto px-6 py-3"
            >
              <Shield className="w-4 h-4" />
              Start Secure Reporting
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </section>
  );
}
