/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * @namespace Phaser.Tilemaps.Parsers.Tiled
 */

module.exports = {

    AssignTileProperties: require('./AssignTileProperties'),
    Base64Decode: require('./Base64Decode'),
    BuildTilesetIndex: require('./BuildTilesetIndex'),
    CreateGroupLayer: require('./CreateGroupLayer'),
    ParseGID: require('./ParseGID'),
    ParseImageLayers: require('./ParseImageLayers'),
    ParseJSONTiled: require('./ParseJSONTiled'),
    ParseObject: require('./ParseObject'),
    ParseObjectLayers: require('./ParseObjectLayers'),
    ParseTileLayers: require('./ParseTileLayers'),
    ParseTilesets: require('./ParseTilesets')

};
