'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Activity, Clock, CheckCircle, AlertCircle,
  MessageSquare, Bell, Eye, ArrowRight,
  User, Calendar, MapPin, FileText,
  Phone, Mail, ExternalLink
} from 'lucide-react';

export default function DemoLevel3() {
  const [isVisible, setIsVisible] = useState(false);
  const [currentStatus, setCurrentStatus] = useState(0);
  const [notifications, setNotifications] = useState([]);
  const [messages, setMessages] = useState([]);
  const [reportData, setReportData] = useState(null);
  const [isLive, setIsLive] = useState(false);
  const sectionRef = useRef(null);

  const statusTimeline = [
    {
      id: 0,
      status: 'Submitted',
      description: 'Report received and logged in system',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      icon: FileText,
      color: 'text-blue-400',
      completed: true
    },
    {
      id: 1,
      status: 'AI Analysis',
      description: 'Automated content analysis and routing',
      timestamp: new Date(Date.now() - 3000000).toISOString(),
      icon: Activity,
      color: 'text-purple-400',
      completed: true
    },
    {
      id: 2,
      status: 'Department Assigned',
      description: 'Routed to Malaysia Traffic Police',
      timestamp: new Date(Date.now() - 2400000).toISOString(),
      icon: CheckCircle,
      color: 'text-green-400',
      completed: true
    },
    {
      id: 3,
      status: 'Under Review',
      description: 'Officer reviewing case details',
      timestamp: new Date(Date.now() - 1800000).toISOString(),
      icon: Eye,
      color: 'text-yellow-400',
      completed: false,
      active: true
    },
    {
      id: 4,
      status: 'Investigation',
      description: 'Field investigation in progress',
      timestamp: null,
      icon: AlertCircle,
      color: 'text-orange-400',
      completed: false
    },
    {
      id: 5,
      status: 'Resolution',
      description: 'Case closed with action taken',
      timestamp: null,
      icon: CheckCircle,
      color: 'text-green-400',
      completed: false
    }
  ];

  const liveNotifications = [
    {
      id: 1,
      type: 'status',
      title: 'Status Update',
      message: 'Your report is now under review by Officer Ahmad Rahman',
      timestamp: new Date(Date.now() - 300000).toISOString(),
      icon: Eye,
      priority: 'medium'
    },
    {
      id: 2,
      type: 'message',
      title: 'New Message',
      message: 'Officer has requested additional location details',
      timestamp: new Date(Date.now() - 600000).toISOString(),
      icon: MessageSquare,
      priority: 'high'
    },
    {
      id: 3,
      type: 'system',
      title: 'Evidence Processed',
      message: 'Uploaded photos have been verified and added to case file',
      timestamp: new Date(Date.now() - 900000).toISOString(),
      icon: CheckCircle,
      priority: 'low'
    }
  ];

  const chatMessages = [
    {
      id: 1,
      sender: 'Officer Ahmad Rahman',
      role: 'Traffic Police Officer',
      message: 'Thank you for your report. I have reviewed the initial details and photos.',
      timestamp: new Date(Date.now() - 1200000).toISOString(),
      type: 'received'
    },
    {
      id: 2,
      sender: 'You',
      role: 'Reporter',
      message: 'Thank you for the quick response. Is there anything else you need from me?',
      timestamp: new Date(Date.now() - 900000).toISOString(),
      type: 'sent'
    },
    {
      id: 3,
      sender: 'Officer Ahmad Rahman',
      role: 'Traffic Police Officer',
      message: 'Could you provide more specific details about the exact location? A landmark or building number would help.',
      timestamp: new Date(Date.now() - 600000).toISOString(),
      type: 'received'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Load report data
          const savedReport = localStorage.getItem('currentReport');
          if (savedReport) {
            setReportData(JSON.parse(savedReport));
          }
          setNotifications(liveNotifications);
          setMessages(chatMessages);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    // Simulate live updates
    if (isVisible) {
      const interval = setInterval(() => {
        setIsLive(prev => !prev);
        
        // Occasionally add new notifications
        if (Math.random() > 0.7) {
          const newNotification = {
            id: Date.now(),
            type: 'system',
            title: 'Live Update',
            message: 'Case priority updated based on traffic conditions',
            timestamp: new Date().toISOString(),
            icon: Activity,
            priority: 'medium'
          };
          setNotifications(prev => [newNotification, ...prev.slice(0, 4)]);
        }
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [isVisible]);

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return date.toLocaleDateString();
  };

  const sendMessage = () => {
    const newMessage = {
      id: Date.now(),
      sender: 'You',
      role: 'Reporter',
      message: 'The incident occurred near the Petronas Twin Towers, specifically at the intersection with Jalan Ampang.',
      timestamp: new Date().toISOString(),
      type: 'sent'
    };
    setMessages(prev => [...prev, newMessage]);
  };

  return (
    <section id="demo-level-3" ref={sectionRef} className="min-h-screen py-20 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div 
            className={`inline-flex items-center gap-2 glass px-4 py-2 rounded-full mb-6 transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <Activity className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-gray-300">Demo Level 3</span>
            {isLive && <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />}
          </div>
          
          <h2 
            className={`text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 transition-all duration-1000 delay-200 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <span className="text-white">Real-Time </span>
            <span 
              className="text-neon"
              style={{
                background: 'linear-gradient(45deg, #00d4ff, #8b5cf6, #10b981)',
                backgroundSize: '200% 200%',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                color: 'transparent',
                animation: 'gradient-shift 3s ease infinite'
              }}
            >
              Tracking
            </span>
          </h2>
          
          <p 
            className={`text-xl text-gray-300 max-w-3xl mx-auto transition-all duration-1000 delay-400 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            Monitor your report's progress in real-time with live updates, direct communication 
            with officers, and complete transparency throughout the process.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Status Timeline */}
          <div 
            className={`lg:col-span-2 transition-all duration-1000 delay-600 ${
              isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'
            }`}
          >
            <div className="glass p-8 rounded-2xl">
              <div className="flex items-center justify-between mb-8">
                <h3 className="text-2xl font-bold text-white">Case Progress</h3>
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${isLive ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`} />
                  <span className="text-sm text-gray-400">Live Updates</span>
                </div>
              </div>

              {/* Timeline */}
              <div className="space-y-6">
                {statusTimeline.map((item, index) => {
                  const Icon = item.icon;
                  return (
                    <div key={item.id} className="flex items-start gap-4">
                      {/* Timeline Line */}
                      <div className="flex flex-col items-center">
                        <div 
                          className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500 ${
                            item.completed ? 'bg-green-500' :
                            item.active ? 'bg-primary animate-pulse' : 'bg-gray-700'
                          }`}
                        >
                          <Icon className="w-6 h-6 text-white" />
                        </div>
                        {index < statusTimeline.length - 1 && (
                          <div 
                            className={`w-1 h-16 mt-2 transition-all duration-500 ${
                              item.completed ? 'bg-green-500' : 'bg-gray-700'
                            }`}
                          />
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 pb-8">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className={`text-lg font-semibold ${
                            item.completed ? 'text-green-400' :
                            item.active ? 'text-primary' : 'text-gray-400'
                          }`}>
                            {item.status}
                          </h4>
                          {item.timestamp && (
                            <span className="text-sm text-gray-500">
                              {formatTime(item.timestamp)}
                            </span>
                          )}
                        </div>
                        <p className="text-gray-300 text-sm">{item.description}</p>
                        
                        {item.active && (
                          <div className="mt-3 p-3 bg-primary/10 rounded-lg border border-primary/20">
                            <div className="flex items-center gap-2 text-primary text-sm">
                              <User className="w-4 h-4" />
                              <span>Assigned to: Officer Ahmad Rahman</span>
                            </div>
                            <div className="flex items-center gap-2 text-gray-400 text-sm mt-1">
                              <Phone className="w-4 h-4" />
                              <span>Contact: +60-3-2266-2222</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Report Details */}
              {reportData && (
                <div className="mt-8 p-6 bg-gray-800/50 rounded-xl">
                  <h4 className="text-lg font-semibold text-white mb-4">Report Details</h4>
                  <div className="grid md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-400">ID:</span>
                      <span className="text-white">{reportData.id}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-400">Submitted:</span>
                      <span className="text-white">{formatTime(reportData.timestamp)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-400">Location:</span>
                      <span className="text-white">{reportData.location}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertCircle className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-400">Priority:</span>
                      <span className="text-white capitalize">{reportData.urgency}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Live Updates & Communication */}
          <div 
            className={`space-y-6 transition-all duration-1000 delay-800 ${
              isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'
            }`}
          >
            {/* Live Notifications */}
            <div className="glass p-6 rounded-2xl">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-bold text-white">Live Updates</h3>
                <Bell className="w-5 h-5 text-primary" />
              </div>
              
              <div className="space-y-4">
                {notifications.slice(0, 3).map((notification) => {
                  const Icon = notification.icon;
                  return (
                    <div key={notification.id} className="p-4 bg-gray-800/50 rounded-lg">
                      <div className="flex items-start gap-3">
                        <Icon className={`w-5 h-5 mt-1 ${
                          notification.priority === 'high' ? 'text-red-400' :
                          notification.priority === 'medium' ? 'text-yellow-400' : 'text-green-400'
                        }`} />
                        <div className="flex-1">
                          <h4 className="text-white font-medium text-sm">{notification.title}</h4>
                          <p className="text-gray-400 text-xs mt-1">{notification.message}</p>
                          <span className="text-gray-500 text-xs">{formatTime(notification.timestamp)}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Communication Panel */}
            <div className="glass p-6 rounded-2xl">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-bold text-white">Direct Communication</h3>
                <MessageSquare className="w-5 h-5 text-primary" />
              </div>
              
              {/* Messages */}
              <div className="space-y-4 mb-4 max-h-64 overflow-y-auto">
                {messages.map((message) => (
                  <div key={message.id} className={`flex ${message.type === 'sent' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-xs p-3 rounded-lg ${
                      message.type === 'sent' 
                        ? 'bg-primary text-white' 
                        : 'bg-gray-800 text-gray-300'
                    }`}>
                      {message.type === 'received' && (
                        <div className="text-xs text-gray-400 mb-1">{message.sender}</div>
                      )}
                      <p className="text-sm">{message.message}</p>
                      <div className="text-xs opacity-70 mt-1">{formatTime(message.timestamp)}</div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Quick Reply */}
              <button 
                onClick={sendMessage}
                className="w-full glass p-3 rounded-lg text-gray-300 hover:text-white transition-colors duration-300 text-sm"
              >
                Send Location Details
              </button>
            </div>

            {/* Quick Actions */}
            <div className="glass p-6 rounded-2xl">
              <h3 className="text-lg font-bold text-white mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button className="w-full glass p-3 rounded-lg text-gray-300 hover:text-white transition-colors duration-300 text-sm flex items-center gap-2">
                  <Phone className="w-4 h-4" />
                  Call Officer
                </button>
                <button className="w-full glass p-3 rounded-lg text-gray-300 hover:text-white transition-colors duration-300 text-sm flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  Email Updates
                </button>
                <button className="w-full glass p-3 rounded-lg text-gray-300 hover:text-white transition-colors duration-300 text-sm flex items-center gap-2">
                  <ExternalLink className="w-4 h-4" />
                  View Full Case
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Continue Button */}
        <div 
          className={`text-center mt-16 transition-all duration-1000 delay-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <button 
            onClick={() => document.getElementById('demo-level-4')?.scrollIntoView({ behavior: 'smooth' })}
            className="btn-primary px-8 py-4 text-lg flex items-center gap-3 mx-auto"
          >
            Explore Advanced Features
            <ArrowRight className="w-5 h-5" />
          </button>
        </div>
      </div>

      <style jsx>{`
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </section>
  );
}
