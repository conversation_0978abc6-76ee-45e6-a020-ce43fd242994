/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2023 Photon Storm Ltd.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * @namespace Phaser.Renderer.WebGL.Shaders
 */

module.exports = {

    AddBlendFrag: require('./AddBlend-frag.js'),
    BitmapMaskFrag: require('./BitmapMask-frag.js'),
    BitmapMaskVert: require('./BitmapMask-vert.js'),
    ColorMatrixFrag: require('./ColorMatrix-frag.js'),
    CopyFrag: require('./Copy-frag.js'),
    FXBarrelFrag: require('./FXBarrel-frag.js'),
    FXBloomFrag: require('./FXBloom-frag.js'),
    FXBlurHighFrag: require('./FXBlurHigh-frag.js'),
    FXBlurLowFrag: require('./FXBlurLow-frag.js'),
    FXBlurMedFrag: require('./FXBlurMed-frag.js'),
    FXBokehFrag: require('./FXBokeh-frag.js'),
    FXCircleFrag: require('./FXCircle-frag.js'),
    FXDisplacementFrag: require('./FXDisplacement-frag.js'),
    FXGlowFrag: require('./FXGlow-frag.js'),
    FXGradientFrag: require('./FXGradient-frag.js'),
    FXPixelateFrag: require('./FXPixelate-frag.js'),
    FXShadowFrag: require('./FXShadow-frag.js'),
    FXShineFrag: require('./FXShine-frag.js'),
    FXVignetteFrag: require('./FXVignette-frag.js'),
    FXWipeFrag: require('./FXWipe-frag.js'),
    LightFrag: require('./Light-frag.js'),
    LinearBlendFrag: require('./LinearBlend-frag.js'),
    MeshFrag: require('./Mesh-frag.js'),
    MeshVert: require('./Mesh-vert.js'),
    MobileFrag: require('./Mobile-frag.js'),
    MobileVert: require('./Mobile-vert.js'),
    MultiFrag: require('./Multi-frag.js'),
    MultiVert: require('./Multi-vert.js'),
    PointLightFrag: require('./PointLight-frag.js'),
    PointLightVert: require('./PointLight-vert.js'),
    PostFXFrag: require('./PostFX-frag.js'),
    QuadVert: require('./Quad-vert.js'),
    SingleFrag: require('./Single-frag.js'),
    SingleVert: require('./Single-vert.js')

};
