{"version": 3, "file": "NURBSCurve.cjs", "sources": ["../../src/curves/NURBSCurve.js"], "sourcesContent": ["import { Curve, Vector3, Vector4 } from 'three'\nimport * as NURBSUtils from '../curves/NURBSUtils'\n\n/**\n * NURBS curve object\n *\n * Derives from Curve, overriding getPoint and getTangent.\n *\n * Implementation is based on (x, y [, z=0 [, w=1]]) control points with w=weight.\n *\n **/\n\nclass NURBSCurve extends Curve {\n  constructor(\n    degree,\n    knots /* array of reals */,\n    controlPoints /* array of Vector(2|3|4) */,\n    startKnot /* index in knots */,\n    endKnot /* index in knots */,\n  ) {\n    super()\n\n    this.degree = degree\n    this.knots = knots\n    this.controlPoints = []\n    // Used by periodic NURBS to remove hidden spans\n    this.startKnot = startKnot || 0\n    this.endKnot = endKnot || this.knots.length - 1\n    for (let i = 0; i < controlPoints.length; ++i) {\n      // ensure Vector4 for control points\n      const point = controlPoints[i]\n      this.controlPoints[i] = new Vector4(point.x, point.y, point.z, point.w)\n    }\n  }\n\n  getPoint(t, optionalTarget) {\n    const point = optionalTarget || new Vector3()\n\n    const u = this.knots[this.startKnot] + t * (this.knots[this.endKnot] - this.knots[this.startKnot]) // linear mapping t->u\n\n    // following results in (wx, wy, wz, w) homogeneous point\n    const hpoint = NURBSUtils.calcBSplinePoint(this.degree, this.knots, this.controlPoints, u)\n\n    if (hpoint.w != 1.0) {\n      // project to 3D space: (wx, wy, wz, w) -> (x, y, z, 1)\n      hpoint.divideScalar(hpoint.w)\n    }\n\n    return point.set(hpoint.x, hpoint.y, hpoint.z)\n  }\n\n  getTangent(t, optionalTarget) {\n    const tangent = optionalTarget || new Vector3()\n\n    const u = this.knots[0] + t * (this.knots[this.knots.length - 1] - this.knots[0])\n    const ders = NURBSUtils.calcNURBSDerivatives(this.degree, this.knots, this.controlPoints, u, 1)\n    tangent.copy(ders[1]).normalize()\n\n    return tangent\n  }\n}\n\nexport { NURBSCurve }\n"], "names": ["Curve", "Vector4", "Vector3", "NURBSUtils.calcBSplinePoint", "NURBSUtils.calcNURBSDerivatives"], "mappings": ";;;;AAYA,MAAM,mBAAmBA,MAAAA,MAAM;AAAA,EAC7B,YACE,QACA,OACA,eACA,WACA,SACA;AACA,UAAO;AAEP,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,gBAAgB,CAAE;AAEvB,SAAK,YAAY,aAAa;AAC9B,SAAK,UAAU,WAAW,KAAK,MAAM,SAAS;AAC9C,aAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,EAAE,GAAG;AAE7C,YAAM,QAAQ,cAAc,CAAC;AAC7B,WAAK,cAAc,CAAC,IAAI,IAAIC,MAAO,QAAC,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAAA,IACvE;AAAA,EACF;AAAA,EAED,SAAS,GAAG,gBAAgB;AAC1B,UAAM,QAAQ,kBAAkB,IAAIC,cAAS;AAE7C,UAAM,IAAI,KAAK,MAAM,KAAK,SAAS,IAAI,KAAK,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,MAAM,KAAK,SAAS;AAGhG,UAAM,SAASC,4BAA4B,KAAK,QAAQ,KAAK,OAAO,KAAK,eAAe,CAAC;AAEzF,QAAI,OAAO,KAAK,GAAK;AAEnB,aAAO,aAAa,OAAO,CAAC;AAAA,IAC7B;AAED,WAAO,MAAM,IAAI,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,EAC9C;AAAA,EAED,WAAW,GAAG,gBAAgB;AAC5B,UAAM,UAAU,kBAAkB,IAAID,cAAS;AAE/C,UAAM,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC,IAAI,KAAK,MAAM,CAAC;AAC/E,UAAM,OAAOE,WAAAA,qBAAgC,KAAK,QAAQ,KAAK,OAAO,KAAK,eAAe,GAAG,CAAC;AAC9F,YAAQ,KAAK,KAAK,CAAC,CAAC,EAAE,UAAW;AAEjC,WAAO;AAAA,EACR;AACH;;"}