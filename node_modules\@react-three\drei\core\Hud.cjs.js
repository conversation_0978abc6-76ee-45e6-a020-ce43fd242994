"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three"),r=require("react"),t=require("@react-three/fiber");function n(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var a=n(e),u=n(r);function c({defaultScene:e,defaultCamera:r,renderPriority:n=1}){const{gl:a,scene:c,camera:l}=t.useThree();let o;return t.useFrame((()=>{o=a.autoClear,1===n&&(a.autoClear=!0,a.render(e,r)),a.autoClear=!1,a.clear<PERSON>epth(),a.render(c,l),a.autoClear=o}),n),u.createElement("group",{onPointerOver:()=>null})}exports.Hud=function({children:e,renderPriority:r=1}){const{scene:n,camera:l}=t.useThree(),[o]=u.useState((()=>new a.Scene));return u.createElement(u.Fragment,null,t.createPortal(u.createElement(u.Fragment,null,e,u.createElement(c,{defaultScene:n,defaultCamera:l,renderPriority:r})),o,{events:{priority:r+1}}))};
