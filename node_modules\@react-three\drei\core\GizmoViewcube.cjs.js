"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("@react-three/fiber"),o=require("./GizmoHelper.cjs.js"),a=require("three");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("./OrthographicCamera.cjs.js"),require("./Fbo.cjs.js"),require("./Hud.cjs.js");var l=n(e),s=i(t);const c="#f0f0f0",u="#999",p="black",m="black",f=["Right","Left","Top","Bottom","Front","Back"],h=e=>new a.Vector3(...e).multiplyScalar(.38),d=[[1,1,1],[1,1,-1],[1,-1,1],[1,-1,-1],[-1,1,1],[-1,1,-1],[-1,-1,1],[-1,-1,-1]].map(h),y=[.25,.25,.25],g=[[1,1,0],[1,0,1],[1,0,-1],[1,-1,0],[0,1,1],[0,1,-1],[0,-1,1],[0,-1,-1],[-1,1,0],[-1,0,1],[-1,0,-1],[-1,-1,0]].map(h),b=g.map((e=>e.toArray().map((e=>0==e?.5:.25)))),x=({hover:e,index:t,font:o="20px Inter var, Arial, sans-serif",faces:n=f,color:i=c,hoverColor:l=u,textColor:h=p,strokeColor:d=m,opacity:y=1})=>{const g=r.useThree((e=>e.gl)),b=s.useMemo((()=>{const e=document.createElement("canvas");e.width=128,e.height=128;const r=e.getContext("2d");return r.fillStyle=i,r.fillRect(0,0,e.width,e.height),r.strokeStyle=d,r.strokeRect(0,0,e.width,e.height),r.font=o,r.textAlign="center",r.fillStyle=h,r.fillText(n[t].toUpperCase(),64,76),new a.CanvasTexture(e)}),[t,n,o,i,h,d]);return s.createElement("meshBasicMaterial",{map:b,"map-anisotropy":g.capabilities.getMaxAnisotropy()||1,attach:`material-${t}`,color:e?l:"white",transparent:!0,opacity:y})},C=e=>{const{tweenCamera:t}=o.useGizmoContext(),[r,a]=s.useState(null);return s.createElement("mesh",{onPointerOut:e=>{e.stopPropagation(),a(null)},onPointerMove:e=>{e.stopPropagation(),a(Math.floor(e.faceIndex/2))},onClick:e.onClick||(e=>{e.stopPropagation(),t(e.face.normal)})},[...Array(6)].map(((t,o)=>s.createElement(x,l.default({key:o,index:o,hover:r===o},e)))),s.createElement("boxGeometry",null))},j=({onClick:e,dimensions:t,position:r,hoverColor:a=u})=>{const{tweenCamera:n}=o.useGizmoContext(),[i,l]=s.useState(!1);return s.createElement("mesh",{scale:1.01,position:r,onPointerOver:e=>{e.stopPropagation(),l(!0)},onPointerOut:e=>{e.stopPropagation(),l(!1)},onClick:e||(e=>{e.stopPropagation(),n(r)})},s.createElement("meshBasicMaterial",{color:i?a:"white",transparent:!0,opacity:.6,visible:i}),s.createElement("boxGeometry",{args:t}))};exports.GizmoViewcube=e=>s.createElement("group",{scale:[60,60,60]},s.createElement(C,e),g.map(((t,r)=>s.createElement(j,l.default({key:r,position:t,dimensions:b[r]},e)))),d.map(((t,r)=>s.createElement(j,l.default({key:r,position:t,dimensions:y},e)))));
