'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  MapPin, Camera, FileText, Clock, 
  ArrowRight, CheckCircle, AlertTriangle,
  Upload, X, Plus, Zap
} from 'lucide-react';

export default function DemoLevel1() {
  const [isVisible, setIsVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    category: '',
    location: '',
    description: '',
    urgency: 'medium',
    files: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const sectionRef = useRef(null);
  const fileInputRef = useRef(null);

  const categories = [
    { id: 'traffic', name: 'Traffic Violation', icon: '🚗', color: 'text-red-400' },
    { id: 'counterfeit', name: 'Counterfeit Products', icon: '🛡️', color: 'text-orange-400' },
    { id: 'public', name: 'Public Disturbance', icon: '📢', color: 'text-yellow-400' },
    { id: 'safety', name: 'Safety Hazard', icon: '⚠️', color: 'text-purple-400' },
    { id: 'environment', name: 'Environmental Issue', icon: '🌱', color: 'text-green-400' },
    { id: 'other', name: 'Other', icon: '📋', color: 'text-blue-400' }
  ];

  const urgencyLevels = [
    { id: 'low', name: 'Low Priority', color: 'bg-green-500', description: 'Non-urgent matter' },
    { id: 'medium', name: 'Medium Priority', color: 'bg-yellow-500', description: 'Standard processing' },
    { id: 'high', name: 'High Priority', color: 'bg-orange-500', description: 'Requires attention' },
    { id: 'emergency', name: 'Emergency', color: 'bg-red-500', description: 'Immediate action needed' }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    const newFiles = files.map(file => ({
      id: Date.now() + Math.random(),
      name: file.name,
      size: file.size,
      type: file.type,
      url: URL.createObjectURL(file)
    }));
    
    setFormData(prev => ({
      ...prev,
      files: [...prev.files, ...newFiles]
    }));
  };

  const removeFile = (fileId) => {
    setFormData(prev => ({
      ...prev,
      files: prev.files.filter(file => file.id !== fileId)
    }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Store in localStorage for demo
    const reportData = {
      id: `RPT-${Date.now()}`,
      ...formData,
      timestamp: new Date().toISOString(),
      status: 'submitted'
    };
    
    localStorage.setItem('currentReport', JSON.stringify(reportData));
    localStorage.setItem('demoProgress', JSON.stringify({ level: 1, completed: true }));
    
    setIsSubmitting(false);
    setIsCompleted(true);
  };

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1: return formData.category;
      case 2: return formData.location;
      case 3: return formData.description.length > 10;
      case 4: return true;
      default: return false;
    }
  };

  return (
    <section id="demo-level-1" ref={sectionRef} className="min-h-screen py-20 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div 
            className={`inline-flex items-center gap-2 glass px-4 py-2 rounded-full mb-6 transition-all duration-1000 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <FileText className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-gray-300">Demo Level 1</span>
          </div>
          
          <h2 
            className={`text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 transition-all duration-1000 delay-200 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <span className="text-white">Submit Your </span>
            <span 
              className="text-neon"
              style={{
                background: 'linear-gradient(45deg, #00d4ff, #8b5cf6, #10b981)',
                backgroundSize: '200% 200%',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                color: 'transparent',
                animation: 'gradient-shift 3s ease infinite'
              }}
            >
              First Report
            </span>
          </h2>
          
          <p 
            className={`text-xl text-gray-300 max-w-3xl mx-auto transition-all duration-1000 delay-400 ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            Experience our streamlined 4-step reporting process. Submit a real report 
            and see how ReportU makes civic engagement effortless.
          </p>
        </div>

        {!isCompleted ? (
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Form Steps */}
            <div 
              className={`transition-all duration-1000 delay-600 ${
                isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'
              }`}
            >
              <div className="glass p-8 rounded-2xl">
                {/* Step Indicator */}
                <div className="flex items-center justify-between mb-8">
                  {[1, 2, 3, 4].map((step) => (
                    <div key={step} className="flex items-center">
                      <div 
                        className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${
                          step <= currentStep 
                            ? 'bg-primary text-white' 
                            : 'bg-gray-700 text-gray-400'
                        }`}
                      >
                        {step < currentStep ? <CheckCircle className="w-5 h-5" /> : step}
                      </div>
                      {step < 4 && (
                        <div 
                          className={`w-12 h-1 mx-2 transition-all duration-300 ${
                            step < currentStep ? 'bg-primary' : 'bg-gray-700'
                          }`}
                        />
                      )}
                    </div>
                  ))}
                </div>

                {/* Step Content */}
                {currentStep === 1 && (
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-4">Select Category</h3>
                    <p className="text-gray-400 mb-6">What type of offense are you reporting?</p>
                    
                    <div className="grid grid-cols-2 gap-4">
                      {categories.map((category) => (
                        <button
                          key={category.id}
                          onClick={() => setFormData(prev => ({ ...prev, category: category.id }))}
                          className={`p-4 rounded-lg border-2 transition-all duration-300 text-left ${
                            formData.category === category.id
                              ? 'border-primary bg-primary/10'
                              : 'border-gray-600 hover:border-gray-500'
                          }`}
                        >
                          <div className="text-2xl mb-2">{category.icon}</div>
                          <div className={`font-semibold ${category.color}`}>{category.name}</div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {currentStep === 2 && (
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-4">Location Details</h3>
                    <p className="text-gray-400 mb-6">Where did this incident occur?</p>
                    
                    <div className="space-y-4">
                      <div className="relative">
                        <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                        <input
                          type="text"
                          placeholder="Enter location or address"
                          value={formData.location}
                          onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                          className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-primary focus:outline-none"
                        />
                      </div>
                      
                      <button className="w-full glass p-3 rounded-lg text-gray-300 hover:text-white transition-colors duration-300 flex items-center justify-center gap-2">
                        <MapPin className="w-4 h-4" />
                        Use Current Location
                      </button>
                    </div>
                  </div>
                )}

                {currentStep === 3 && (
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-4">Description</h3>
                    <p className="text-gray-400 mb-6">Provide details about the incident</p>
                    
                    <textarea
                      placeholder="Describe what happened, when it occurred, and any other relevant details..."
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      rows={6}
                      className="w-full p-4 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-primary focus:outline-none resize-none"
                    />
                    
                    <div className="mt-4 text-sm text-gray-400">
                      {formData.description.length}/500 characters
                    </div>
                  </div>
                )}

                {currentStep === 4 && (
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-4">Evidence & Priority</h3>
                    <p className="text-gray-400 mb-6">Upload files and set priority level</p>
                    
                    {/* File Upload */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Upload Evidence (Photos, Videos, Documents)
                      </label>
                      
                      <div 
                        onClick={() => fileInputRef.current?.click()}
                        className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-primary transition-colors duration-300"
                      >
                        <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-400">Click to upload or drag and drop</p>
                        <p className="text-xs text-gray-500 mt-1">PNG, JPG, MP4, PDF up to 10MB</p>
                      </div>
                      
                      <input
                        ref={fileInputRef}
                        type="file"
                        multiple
                        accept="image/*,video/*,.pdf"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                      
                      {/* File List */}
                      {formData.files.length > 0 && (
                        <div className="mt-4 space-y-2">
                          {formData.files.map((file) => (
                            <div key={file.id} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-primary/20 rounded flex items-center justify-center">
                                  <FileText className="w-4 h-4 text-primary" />
                                </div>
                                <div>
                                  <div className="text-white text-sm">{file.name}</div>
                                  <div className="text-gray-400 text-xs">
                                    {(file.size / 1024 / 1024).toFixed(2)} MB
                                  </div>
                                </div>
                              </div>
                              <button
                                onClick={() => removeFile(file.id)}
                                className="text-gray-400 hover:text-red-400 transition-colors duration-300"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Priority Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-3">
                        Priority Level
                      </label>
                      <div className="grid grid-cols-2 gap-3">
                        {urgencyLevels.map((level) => (
                          <button
                            key={level.id}
                            onClick={() => setFormData(prev => ({ ...prev, urgency: level.id }))}
                            className={`p-3 rounded-lg border-2 transition-all duration-300 text-left ${
                              formData.urgency === level.id
                                ? 'border-primary bg-primary/10'
                                : 'border-gray-600 hover:border-gray-500'
                            }`}
                          >
                            <div className="flex items-center gap-2 mb-1">
                              <div className={`w-3 h-3 rounded-full ${level.color}`} />
                              <span className="font-semibold text-white">{level.name}</span>
                            </div>
                            <p className="text-xs text-gray-400">{level.description}</p>
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex justify-between mt-8">
                  <button
                    onClick={prevStep}
                    disabled={currentStep === 1}
                    className="px-6 py-3 glass rounded-lg text-gray-300 hover:text-white transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  
                  {currentStep < 4 ? (
                    <button
                      onClick={nextStep}
                      disabled={!canProceed()}
                      className="btn-primary px-6 py-3 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                      <ArrowRight className="w-4 h-4" />
                    </button>
                  ) : (
                    <button
                      onClick={handleSubmit}
                      disabled={isSubmitting || !canProceed()}
                      className="btn-primary px-6 py-3 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          Submitting...
                        </>
                      ) : (
                        <>
                          <Zap className="w-4 h-4" />
                          Submit Report
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Preview Panel */}
            <div 
              className={`transition-all duration-1000 delay-800 ${
                isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'
              }`}
            >
              <div className="glass p-8 rounded-2xl sticky top-24">
                <h3 className="text-xl font-bold text-white mb-6">Report Preview</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="text-sm text-gray-400">Category</label>
                    <div className="text-white">
                      {formData.category ? 
                        categories.find(c => c.id === formData.category)?.name : 
                        'Not selected'
                      }
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm text-gray-400">Location</label>
                    <div className="text-white">{formData.location || 'Not specified'}</div>
                  </div>
                  
                  <div>
                    <label className="text-sm text-gray-400">Description</label>
                    <div className="text-white text-sm">
                      {formData.description || 'No description provided'}
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm text-gray-400">Priority</label>
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${
                        urgencyLevels.find(l => l.id === formData.urgency)?.color
                      }`} />
                      <span className="text-white">
                        {urgencyLevels.find(l => l.id === formData.urgency)?.name}
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm text-gray-400">Evidence Files</label>
                    <div className="text-white">{formData.files.length} file(s) attached</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* Success State */
          <div className="text-center max-w-2xl mx-auto">
            <div className="glass p-12 rounded-2xl">
              <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-6" />
              <h3 className="text-3xl font-bold text-white mb-4">Report Submitted Successfully!</h3>
              <p className="text-gray-300 mb-6">
                Your report has been received and is being processed. You can now proceed to 
                Level 2 to see how our AI routing system works.
              </p>
              <button 
                onClick={() => document.getElementById('demo-level-2')?.scrollIntoView({ behavior: 'smooth' })}
                className="btn-primary px-6 py-3 flex items-center gap-2 mx-auto"
              >
                Continue to Level 2
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </section>
  );
}
