/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

var Rectangle = require('./Rectangle');

Rectangle.Area = require('./Area');
Rectangle.Ceil = require('./Ceil');
Rectangle.CeilAll = require('./CeilAll');
Rectangle.CenterOn = require('./CenterOn');
Rectangle.Clone = require('./Clone');
Rectangle.Contains = require('./Contains');
Rectangle.ContainsPoint = require('./ContainsPoint');
Rectangle.ContainsRect = require('./ContainsRect');
Rectangle.CopyFrom = require('./CopyFrom');
Rectangle.Decompose = require('./Decompose');
Rectangle.Equals = require('./Equals');
Rectangle.FitInside = require('./FitInside');
Rectangle.FitOutside = require('./FitOutside');
Rectangle.Floor = require('./Floor');
Rectangle.FloorAll = require('./FloorAll');
Rectangle.FromPoints = require('./FromPoints');
Rectangle.FromXY = require('./FromXY');
Rectangle.GetAspectRatio = require('./GetAspectRatio');
Rectangle.GetCenter = require('./GetCenter');
Rectangle.GetPoint = require('./GetPoint');
Rectangle.GetPoints = require('./GetPoints');
Rectangle.GetSize = require('./GetSize');
Rectangle.Inflate = require('./Inflate');
Rectangle.Intersection = require('./Intersection');
Rectangle.MarchingAnts = require('./MarchingAnts');
Rectangle.MergePoints = require('./MergePoints');
Rectangle.MergeRect = require('./MergeRect');
Rectangle.MergeXY = require('./MergeXY');
Rectangle.Offset = require('./Offset');
Rectangle.OffsetPoint = require('./OffsetPoint');
Rectangle.Overlaps = require('./Overlaps');
Rectangle.Perimeter = require('./Perimeter');
Rectangle.PerimeterPoint = require('./PerimeterPoint');
Rectangle.Random = require('./Random');
Rectangle.RandomOutside = require('./RandomOutside');
Rectangle.SameDimensions = require('./SameDimensions');
Rectangle.Scale = require('./Scale');
Rectangle.Union = require('./Union');

module.exports = Rectangle;
