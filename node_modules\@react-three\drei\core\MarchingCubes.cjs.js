"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("three"),t=require("react"),n=require("three-stdlib"),u=require("@react-three/fiber");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var o=a(e),c=l(r),s=l(t);const i=s.createContext(null),d=s.forwardRef((({resolution:e=28,maxPolyCount:r=1e4,enableUvs:t=!1,enableColors:a=!1,children:l,...c},d)=>{const f=s.useRef(null);s.useImperativeHandle(d,(()=>f.current),[]);const m=s.useMemo((()=>new n.MarchingCubes(e,null,t,a,r)),[e,r,t,a]),b=s.useMemo((()=>({getParent:()=>f})),[]);return u.useFrame((()=>{m.update(),m.reset()}),-1),s.createElement(s.Fragment,null,s.createElement("primitive",o.default({object:m,ref:f},c),s.createElement(i.Provider,{value:b},l)))})),f=s.forwardRef((({strength:e=.5,subtract:r=12,color:t,...n},a)=>{const{getParent:l}=s.useContext(i),d=s.useMemo((()=>l()),[l]),f=s.useRef(null);s.useImperativeHandle(a,(()=>f.current),[]);const m=new c.Vector3;return u.useFrame((n=>{d.current&&f.current&&(f.current.getWorldPosition(m),d.current.addBall(.5+.5*m.x,.5+.5*m.y,.5+.5*m.z,e,r,t))})),s.createElement("group",o.default({ref:f},n))})),m=s.forwardRef((({planeType:e="x",strength:r=.5,subtract:t=12,...n},a)=>{const{getParent:l}=s.useContext(i),c=s.useMemo((()=>l()),[l]),d=s.useRef(null);s.useImperativeHandle(a,(()=>d.current),[]);const f=s.useMemo((()=>"x"===e?"addPlaneX":"y"===e?"addPlaneY":"addPlaneZ"),[e]);return u.useFrame((()=>{c.current&&d.current&&c.current[f](r,t)})),s.createElement("group",o.default({ref:d},n))}));exports.MarchingCube=f,exports.MarchingCubes=d,exports.MarchingPlane=m;
