"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("@react-three/fiber"),a=require("three"),o=require("./GizmoHelper.cjs.js");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("./OrthographicCamera.cjs.js"),require("./Fbo.cjs.js"),require("./Hud.cjs.js");var i=l(e),c=n(t);function s({scale:e=[.8,.05,.05],color:t,rotation:r}){return c.createElement("group",{rotation:r},c.createElement("mesh",{position:[.4,0,0]},c.createElement("boxGeometry",{args:e}),c.createElement("meshBasicMaterial",{color:t,toneMapped:!1})))}function u({onClick:e,font:t,disabled:o,arcStyle:l,label:n,labelColor:s,axisHeadScale:u=1,...p}){const f=r.useThree((e=>e.gl)),d=c.useMemo((()=>{const e=document.createElement("canvas");e.width=64,e.height=64;const r=e.getContext("2d");return r.beginPath(),r.arc(32,32,16,0,2*Math.PI),r.closePath(),r.fillStyle=l,r.fill(),n&&(r.font=t,r.textAlign="center",r.fillStyle=s,r.fillText(n,32,41)),new a.CanvasTexture(e)}),[l,n,s,t]),[m,b]=c.useState(!1),h=(n?1:.75)*(m?1.2:1)*u;return c.createElement("sprite",i.default({scale:h,onPointerOver:o?void 0:e=>{e.stopPropagation(),b(!0)},onPointerOut:o?void 0:e||(e=>{e.stopPropagation(),b(!1)})},p),c.createElement("spriteMaterial",{map:d,"map-anisotropy":f.capabilities.getMaxAnisotropy()||1,alphaTest:.3,opacity:n?1:.75,toneMapped:!1}))}exports.GizmoViewport=({hideNegativeAxes:e,hideAxisHeads:t,disabled:r,font:a="18px Inter var, Arial, sans-serif",axisColors:l=["#ff2060","#20df80","#2080ff"],axisHeadScale:n=1,axisScale:p,labels:f=["X","Y","Z"],labelColor:d="#000",onClick:m,...b})=>{const[h,E,g]=l,{tweenCamera:x}=o.useGizmoContext(),y={font:a,disabled:r,labelColor:d,onClick:m,axisHeadScale:n,onPointerDown:r?void 0:e=>{x(e.object.position),e.stopPropagation()}};return c.createElement("group",i.default({scale:40},b),c.createElement(s,{color:h,rotation:[0,0,0],scale:p}),c.createElement(s,{color:E,rotation:[0,0,Math.PI/2],scale:p}),c.createElement(s,{color:g,rotation:[0,-Math.PI/2,0],scale:p}),!t&&c.createElement(c.Fragment,null,c.createElement(u,i.default({arcStyle:h,position:[1,0,0],label:f[0]},y)),c.createElement(u,i.default({arcStyle:E,position:[0,1,0],label:f[1]},y)),c.createElement(u,i.default({arcStyle:g,position:[0,0,1],label:f[2]},y)),!e&&c.createElement(c.Fragment,null,c.createElement(u,i.default({arcStyle:h,position:[-1,0,0]},y)),c.createElement(u,i.default({arcStyle:E,position:[0,-1,0]},y)),c.createElement(u,i.default({arcStyle:g,position:[0,0,-1]},y)))))};
