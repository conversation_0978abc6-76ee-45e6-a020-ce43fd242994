module.exports = [
    '#define SHADER_NAME PHASER_LIGHT_FS',
    'precision mediump float;',
    'struct Light',
    '{',
    '    vec2 position;',
    '    vec3 color;',
    '    float intensity;',
    '    float radius;',
    '};',
    'const int kMaxLights = %LIGHT_COUNT%;',
    'uniform vec4 uCamera; /* x, y, rotation, zoom */',
    'uniform vec2 uResolution;',
    'uniform sampler2D uMainSampler;',
    'uniform sampler2D uNormSampler;',
    'uniform vec3 uAmbientLightColor;',
    'uniform Light uLights[kMaxLights];',
    'uniform mat3 uInverseRotationMatrix;',
    'uniform int uLightCount;',
    'varying vec2 outTexCoord;',
    'varying float outTexId;',
    'varying float outTintEffect;',
    'varying vec4 outTint;',
    'void main ()',
    '{',
    '    vec3 finalColor = vec3(0.0, 0.0, 0.0);',
    '    vec4 texel = vec4(outTint.bgr * outTint.a, outTint.a);',
    '    vec4 texture = texture2D(uMainSampler, outTexCoord);',
    '    vec4 color = texture * texel;',
    '    if (outTintEffect == 1.0)',
    '    {',
    '        color.rgb = mix(texture.rgb, outTint.bgr * outTint.a, texture.a);',
    '    }',
    '    else if (outTintEffect == 2.0)',
    '    {',
    '        color = texel;',
    '    }',
    '    vec3 normalMap = texture2D(uNormSampler, outTexCoord).rgb;',
    '    vec3 normal = normalize(uInverseRotationMatrix * vec3(normalMap * 2.0 - 1.0));',
    '    vec2 res = vec2(min(uResolution.x, uResolution.y)) * uCamera.w;',
    '    for (int index = 0; index < kMaxLights; ++index)',
    '    {',
    '        if (index < uLightCount)',
    '        {',
    '            Light light = uLights[index];',
    '            vec3 lightDir = vec3((light.position.xy / res) - (gl_FragCoord.xy / res), 0.1);',
    '            vec3 lightNormal = normalize(lightDir);',
    '            float distToSurf = length(lightDir) * uCamera.w;',
    '            float diffuseFactor = max(dot(normal, lightNormal), 0.0);',
    '            float radius = (light.radius / res.x * uCamera.w) * uCamera.w;',
    '            float attenuation = clamp(1.0 - distToSurf * distToSurf / (radius * radius), 0.0, 1.0);',
    '            vec3 diffuse = light.color * diffuseFactor;',
    '            finalColor += (attenuation * diffuse) * light.intensity;',
    '        }',
    '    }',
    '    vec4 colorOutput = vec4(uAmbientLightColor + finalColor, 1.0);',
    '    gl_FragColor = color * vec4(colorOutput.rgb * colorOutput.a, colorOutput.a);',
    '}',
].join('\n');
